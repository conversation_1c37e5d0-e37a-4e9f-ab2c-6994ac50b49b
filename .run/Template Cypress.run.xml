<component name="ProjectRunConfigurationManager">
  <configuration default="true" type="JavaScriptTestRunnerCypress">
    <config value="$PROJECT_DIR$/cypress.config.ts" />
    <node-interpreter value="project" />
    <cypress-package value="$PROJECT_DIR$/node_modules/cypress" />
    <cypress-options value="-q --component" />
    <envs />
    <scope-kind value="TEST_FILE" />
    <method v="2" />
  </configuration>
</component>