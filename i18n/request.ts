import {getRequestConfig} from 'next-intl/server';
import {routing} from './routing';
import {defaultTimeZone} from './config';

export default getRequestConfig(async ({requestLocale}) => {
    let locale = await requestLocale;

    if (!locale || !routing.locales.includes(locale as any)) {
        locale = routing.defaultLocale;
    }

    return {
        locale,
        messages: (await import(`@/messages/${locale}.json`)).default,
        timeZone: defaultTimeZone
    };
});
