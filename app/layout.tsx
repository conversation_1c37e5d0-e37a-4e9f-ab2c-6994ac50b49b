import "@radix-ui/themes/styles.css";
import "./globals.css";
import {ReactNode} from "react";
import RootLayout from "@/src/client/Shared/layouts/RootLayout/RootLayout";

import localFont from "next/font/local";
import Background from "@/src/client/Shared/components/Background/Background";
import {Metadata} from "next";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});

const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export default function RootLayoutContainer({children}: Readonly<{ children: ReactNode }>) {
  const font = `${geistMono.variable} ${geistSans.variable}`;

  return (
    <RootLayout font={font}>
      {children}
      <Background/>
    </RootLayout>
  );
}
