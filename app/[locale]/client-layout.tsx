'use client';

import {FC, PropsWithChildren} from "react";
import {ConvexQueryCacheProvider} from "convex-helpers/react/cache";
import {ReduxProvider} from "@/src/client/Shared/providers/ReduxProvider/ReduxProvider";
import {NextIntlClientProvider} from 'next-intl';
import {defaultTimeZone} from '@/i18n/config';

type Props = PropsWithChildren<{
  locale: string;
  messages: Record<string, unknown>;
}>;

const ClientLayout: FC<Props> = ({children, locale, messages}) => {
  return (
    <NextIntlClientProvider locale={locale} messages={messages} timeZone={defaultTimeZone}>
      <ConvexQueryCacheProvider>
        <ReduxProvider>
          {children}
        </ReduxProvider>
      </ConvexQueryCacheProvider>
    </NextIntlClientProvider>
  );
};

export default ClientLayout;
