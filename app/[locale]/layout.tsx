import {FC} from "react";
import {getMessages} from 'next-intl/server';
import ClientLayout from "./client-layout";

type Props = {
  children: React.ReactNode;
  params: Promise<{locale: string}>;
};

const Layout: FC<Props> = async ({children, params}) => {
  const {locale} = await params;
  const messages = await getMessages();

  return (
    <ClientLayout locale={locale} messages={messages}>
      {children}
    </ClientLayout>
  );
};

export default Layout;