'use client'

import {FC, PropsWithChildren} from 'react';
import {Flex} from '@radix-ui/themes';
import {ReduxProvider} from '@/src/client/Shared/providers/ReduxProvider/ReduxProvider';
import {ConvexQueryCacheProvider} from 'convex-helpers/react/cache';

const PlayGameLayout: FC<PropsWithChildren> = ({children}) => (
  <Flex direction="column" className="h-screen">
    <ConvexQueryCacheProvider>
      <ReduxProvider>{children}</ReduxProvider>
    </ConvexQueryCacheProvider>
  </Flex>
);

export default PlayGameLayout;
