# Refactoring Plan

This plan describes how to refactor components and hooks to respect the Clean Architecture and humble object guidelines.

## 1. Identify and Categorize Logic
- List all logic currently in hooks and components.
- Classify logic into application, presentation, or infrastructure layers.

## 2. Extract Use Cases
- Create command or query handlers for each responsibility currently in hooks and components.
- Place handlers under `src/client/[BoundedContext]/application`.
- Each UseCase can have a Request named `[UseCase]Request.ts`

## 3. Create Selectors
- Move derived calculations to selectors in the application layer.
- Components should read Redux state only via selectors.

## 4. Refactor Hooks
- Convert hooks to glue code that only orchestrate commands and queries.
- Expose selectors for data retrieval.

## 5. Refactor Components
- Keep components purely presentational following the humble object pattern.
- Receive data and event handlers through props.

## 6. Update Tests
- Write acceptance tests for use cases using the Arrange/Act/Assert structure.
- Ensure components render correctly based on props without internal logic.

## 7. Incremental Migration
- Refactor one bounded context at a time.
- Commit after each step with passing lint and tests.

## 8. Maintain Project Integrity
- Follow conventional commits and branch naming in English.
- Run `npm run lint` and `npm test` before each commit.

### Completed Refactoring
- The following hooks and components have been successfully refactored to follow Clean Architecture:
- ✅ `useAutoSaveDeck` - Extracted auto-save logic, router navigation, and deck creation/updating logic to application layer commands and queries
- ✅ `useWaitingForOpponentPage` - Extracted timer management, match creation monitoring, and navigation logic to application layer
- ✅ `usePlayGamePage` - Extracted deck selection logic, match making queue logic, and navigation logic to application layer
- ✅ `GameScene` - Extracted Redux dispatching and game state initialization logic to application layer commands
- ✅ `LeaveMatchButton` - Extracted mutation calls, error handling, and state management to application layer
- ✅ `Match` - Extracted match end monitoring and navigation logic to useMatchEndNavigation hook
- ✅ `EditDeckInitializer` - Extracted deck initialization logic to initializeDeckEditor command and useInitializeDeckEditor hook
- ✅ `DeckList` - Extracted deck creation and navigation logic to createDeckAndNavigate command and useDeckCreation hook

### Files Already Following Clean Architecture
- The following files were already well-structured and didn't require refactoring:
- ✅ `useDeckBuilder` - Already delegates to application layer commands and queries
- ✅ `useResponsiveColumnCount` - Contains only presentation logic (responsive design)
- ✅ `DeckBuilderPanel` - Already uses hooks properly and is purely presentational
- ✅ `CreateOrRenameDeckDialog` - Already purely presentational with props-based event handling

All files have been successfully refactored to follow Clean Architecture principles.

