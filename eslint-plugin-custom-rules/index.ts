import describeNamingConvention from './rules/describe-naming-convention';
import itShouldStartWithShould from './rules/it-should-start-with-should';
import requireArrangeActAssertComments from './rules/require-arrange-act-assert-comments';

export = {
  rules: {
    'describe-naming-convention': describeNamingConvention,
    'it-should-start-with-should': itShouldStartWithShould,
    'require-arrange-act-assert-comments': requireArrangeActAssertComments,
  },
};
