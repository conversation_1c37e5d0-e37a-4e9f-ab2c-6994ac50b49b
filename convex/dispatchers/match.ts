import {MutationCtx} from "../_generated/server";
import {Id} from "../_generated/dataModel";
import {matchEndedSaga} from "@/convex/dispatchers/sagas/matchEndedSaga";

export type MatchEvent =
  | { type: "MatchEnded"; payload: { winner: string; loser: string; matchId: string } };

async function runMatchSagas(ctx: MutationCtx, ev: MatchEvent) {
  switch (ev.type) {
    case "MatchEnded":
      return matchEndedSaga(ctx, ev.payload);
  }
}

export async function dispatchEvent(
  ctx: MutationCtx,
  gameId: string,
  matchId: string,
  event: MatchEvent,
) {
  await ctx.db.insert("matchEvents", {
    gameId: gameId as Id<'games'>,
    matchId: matchId as Id<'matches'>,
    type: event.type,
    payload: event.payload,
    occurredAt: Date.now(),
  });

  console.log("dispatched", event.type, event.payload);

  await runMatchSagas(ctx, event);
}