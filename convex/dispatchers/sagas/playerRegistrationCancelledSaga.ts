import {MutationCtx} from "../../_generated/server";
import {UpdateSinglePlayerStatusCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler";
import {ConvexAppUserRepository} from "@/src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository";

export async function playerRegistrationCancelledSaga(
  ctx: MutationCtx,
  payload: { playerId: string }
) {
  const handler = new UpdateSinglePlayerStatusCommandHandler(
    new ConvexAppUserRepository(ctx)
  );
  await handler.handle({playerId: payload.playerId, status: "idle"});
}
