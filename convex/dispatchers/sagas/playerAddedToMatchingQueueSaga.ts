import {MutationCtx} from "../../_generated/server";
import {MakeMatchCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler";
import {UpdatePlayersStatusCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler";
import {ConvexMatchmakingQueueRepository} from "@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";
import {ConvexAppUserRepository} from "@/src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository";
import {ConvexEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/ConvexEventBus";
import {InitializeMatchCommandHandler} from "@/src/server/Gaming/application/commands/Match/InitializeMatch/InitializeMatchCommandHandler";
import {ConvexGameSettingsRepository} from "@/src/server/Gaming/infrastructure/repositories/GameSettings/ConvexGameSettingsRepository";
import {StartMulliganPhaseCommandHandler} from "@/src/server/Gaming/application/commands/Mulligan/StartMulliganPhase/StartMulliganPhaseCommandHandler";

export async function playerAddedToMatchMakingQueueSaga(
  ctx: MutationCtx,
  payload: {
    gameId: string;
    playerId: string;
    queueId: string;
  }
) {
  const eventBus = new ConvexEventBus(ctx);
  const matchRepository = new ConvexMatchRepository(ctx);
  const gameSettingsRepository = new ConvexGameSettingsRepository(ctx);
  const startMulliganHandler = new StartMulliganPhaseCommandHandler(matchRepository);
  const initializeMatchHandler = new InitializeMatchCommandHandler(matchRepository, gameSettingsRepository, startMulliganHandler);
  const makeMatchCommandHandler = new MakeMatchCommandHandler(
    eventBus,
    new ConvexMatchmakingQueueRepository(ctx),
    matchRepository,
    initializeMatchHandler
  );
  const updatePlayersStatusCommandHandler = new UpdatePlayersStatusCommandHandler(
    new ConvexAppUserRepository(ctx)
  );

  await Promise.all([
    makeMatchCommandHandler.handle({ gameId: payload.gameId as string,  playerId: payload.playerId,  queueId: payload.queueId as string}),
    updatePlayersStatusCommandHandler.handle({players: [payload.playerId], status: "waiting-for-opponent"}),
  ]);
}
