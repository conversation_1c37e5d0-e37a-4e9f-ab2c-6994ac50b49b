import {MutationCtx} from "../_generated/server";
import {Id} from "../_generated/dataModel";
import {playerAddedToMatchMakingQueueSaga} from "@/convex/dispatchers/sagas/playerAddedToMatchingQueueSaga";
import {matchCreatedSaga} from "@/convex/dispatchers/sagas/matchCreatedSaga";
import {playerRegistrationCancelledSaga} from "@/convex/dispatchers/sagas/playerRegistrationCancelledSaga";

export type MatchmakingEvent =
  | {
  type: "PlayerAddedToMatchMakingQueue";
  payload: {
    playerId: string;
    gameId: string;
    deckId: string;
    queueId: string;
  };
}
  | {
  type: "MatchCreated";
  payload: {
    matchId: string;
    players: string[];
  };
}
  | {
  type: "PlayerRemovedFromQueue";
  payload: {
    playerId: string;
    reason: "matched" | "timeout" | "cancelled";
  };
}
  | {
  type: "PlayerRegistrationCancelled";
  payload: {
    playerId: string;
  };
};

async function runMatchmakingSagas(ctx: MutationCtx, ev: MatchmakingEvent) {
  switch (ev.type) {
    case "PlayerAddedToMatchMakingQueue":
      await playerAddedToMatchMakingQueueSaga(ctx, ev.payload);
      break;
    case "MatchCreated":
      await matchCreatedSaga(ctx, ev.payload);
      break;
    case "PlayerRegistrationCancelled":
      await playerRegistrationCancelledSaga(ctx, ev.payload);
      break;
  }
}

export async function dispatchEvent(
  ctx: MutationCtx,
  gameId: string,
  aggregateId: string,
  event: MatchmakingEvent,
) {
  await ctx.db.insert("matchmakingEvents", {
    gameId: gameId as Id<'games'>,
    aggregateId: aggregateId as Id<'matchmakingQueue'>,
    type: event.type,
    payload: event.payload,
    occurredAt: Date.now(),
  });

  console.log("dispatched", event.type, event.payload);

  await runMatchmakingSagas(ctx, event);
}