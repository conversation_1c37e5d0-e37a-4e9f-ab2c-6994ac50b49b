import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  ConvexDeckBuilderSettingsRepository
} from "@/src/server/DeckBuilding/infrastructure/repositories/DeckBuilderSettings/ConvexDeckBuilderSettingsRepository";
import {
  LoadDeckBuilderSettingsByIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadDeckBuilderSettingsById/LoadDeckBuilderSettingsByIdQueryHandler";
import {
  LoadDeckBuilderSettingsByIdWebPresenter
} from "@/src/server/DeckBuilding/presentation/presenters/LoadDeckBuilderSettingsByIdWebPresenter";
import {
  ConvexGameFilterListRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameFilterList/ConvexGameFilterListRepository";
import {
  LoadGameFilterListQueryHandler
} from "@/src/server/Gaming/application/queries/LoadGameFilterList/LoadGameFilterListQueryHandler";
import {
  LoadGameFilterListWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadGameFilterListWebPresenter";

export const loadDeckBuilderSettingsByGameId = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexDeckBuilderSettingsRepository(ctx);
    const queryHandler = new LoadDeckBuilderSettingsByIdQueryHandler(repository);
    const presenter = new LoadDeckBuilderSettingsByIdWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});

export const loadAvailableFilters = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameFilterListRepository(ctx);
    const queryHandler = new LoadGameFilterListQueryHandler(repository);
    const presenter = new LoadGameFilterListWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});