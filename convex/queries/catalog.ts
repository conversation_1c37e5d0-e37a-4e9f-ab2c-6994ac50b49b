import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {ConvexGameRepository} from "@/src/server/Gaming/infrastructure/repositories/Game/ConvexGameRepository";
import {LoadGameByIdQueryHandler} from "@/src/server/Gaming/application/queries/LoadGameById/LoadGameByIdQueryHandler";
import {LoadGameByIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadGameByIdWebPresenter";
import {
  ConvexCatalogCardListRepository
} from "@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/ConvexCatalogCardListRepository";
import {
  LoadCatalogCardsByGameIdWebPresenter
} from "@/src/server/DeckBuilding/presentation/presenters/LoadCatalogCardsByGameIdWebPresenter";
import {
  LoadCatalogCardsByGameIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadCatalogCardsByGameId/LoadCatalogCardsByGameIdQueryHandler";
import {
  ConvexGameListRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameList/ConvexGameListRepository";
import {LoadGameListWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadGameListWebPresenter";
import {LoadGameListQueryHandler} from "@/src/server/Gaming/application/queries/LoadGameList/LoadGameListQueryHandler";

export const loadGameById = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameRepository(ctx);
    const queryHandler = new LoadGameByIdQueryHandler(repository);
    const presenter = new LoadGameByIdWebPresenter();

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});

export const loadCatalogCardsByGameId = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexCatalogCardListRepository(ctx);
    const presenter = new LoadCatalogCardsByGameIdWebPresenter();
    const queryHandler = new LoadCatalogCardsByGameIdQueryHandler(repository);

    await queryHandler.handle({gameId}, presenter);

    return presenter.getViewModel();
  },
});

export const loadGameList = protectedQuery({
  args: {},
  handler: async (ctx) => {
    const repository = new ConvexGameListRepository(ctx);
    const presenter = new LoadGameListWebPresenter();
    const queryHandler = new LoadGameListQueryHandler(repository);
    await queryHandler.handle({}, presenter);
    return presenter.getViewModel();
  },
});

