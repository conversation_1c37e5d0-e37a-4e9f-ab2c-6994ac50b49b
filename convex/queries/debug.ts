import {v} from "convex/values";
import {query} from "../_generated/server";
import {
  LoadMatchmakingEventsForGameQueryHandler
} from "@/src/server/Shared/application/queries/Debug/LoadMatchmakingEventsForGameQueryHandler";
import {
  LoadMatchEventsForMatchQueryHandler
} from "@/src/server/Shared/application/queries/Debug/LoadMatchEventsForMatchQueryHandler";

export const matchmakingEventsForGame = query({
  args: {
    gameId: v.id("games"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, {gameId, limit = 20}) => {
    const queryHandler = new LoadMatchmakingEventsForGameQueryHandler(ctx);
    return await queryHandler.handle({gameId, limit});
  },
});

export const matchEventsForMatch = query({
  args: {
    matchId: v.id("matches"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, {matchId, limit = 20}) => {
    const queryHandler = new LoadMatchEventsForMatchQueryHandler(ctx);
    return await queryHandler.handle({matchId, limit});
  },
});