import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";

export const getBoardState = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const match = await ctx.db.get(matchId);
    if (!match) {
      return null;
    }

    return {
      boardState: match.boardState || {
        player1Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        },
        player2Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        }
      },
      currentTurn: match.currentTurn,
      gamePhase: match.gamePhase,
      lastUpdated: Date.now()
    };
  },
});