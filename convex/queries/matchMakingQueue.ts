import {v} from "convex/values";
import {protectedQuery} from "@/convex/helpers";
import {Id} from "@/convex/_generated/dataModel";

export const getPlayerQueueStatus = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const queueItem = await ctx.db
      .query('matchmakingQueue')
      .withIndex('by_gameId', q => q.eq('gameId', gameId as Id<'games'>))
      .filter(q => q.eq(q.field('playerId'), ctx.userId))
      .first();

    return {
      isInQueue: !!queueItem,
      queueItem: queueItem ? {
        id: queueItem._id,
        gameId: queueItem.gameId,
        deckId: queueItem.deckId,
        queuedAt: queueItem.queuedAt
      } : null
    };
  },
});