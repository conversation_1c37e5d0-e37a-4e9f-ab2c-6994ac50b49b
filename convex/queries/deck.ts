import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  ConvexDeckReadRepository
} from "@/src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckReadRepository";
import {
  LoadDeckByIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadDeckById/LoadDeckByIdQueryHandler";
import {LoadDeckByIdWebPresenter} from "@/src/server/DeckBuilding/presentation/presenters/LoadDeckByIdWebPresenter";
import {
  ConvexDeckListRepository
} from "@/src/server/DeckBuilding/infrastructure/repositories/DeckList/ConvexDeckListRepository";
import {
  ConvexCatalogCardListRepository
} from "@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/ConvexCatalogCardListRepository";
import {
  LoadDecksByUserIdAndGameIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQueryHandler";
import {
  LoadDecksByUserIdAndGameIdWebPresenter
} from "@/src/server/DeckBuilding/presentation/presenters/LoadDecksByUserIdAndGameIdWebPresenter";

export const loadDeckById = protectedQuery({
  args: {deckId: v.id("decks")},
  handler: async (ctx, {deckId}) => {
    const repository = new ConvexDeckReadRepository(ctx);
    const queryHandler = new LoadDeckByIdQueryHandler(repository);
    const presenter = new LoadDeckByIdWebPresenter();

    await queryHandler.handle({deckId}, presenter);

    return presenter.getViewModel();
  },
});

export const loadDecksByUserIdAndGameId = protectedQuery({
  args: {
    gameId: v.id("games"),
    locale: v.string(),
  },
  handler: async (ctx, {gameId, locale}) => {
    const deckRepository = new ConvexDeckListRepository(ctx);
    const catalogRepository = new ConvexCatalogCardListRepository(ctx);
    const queryHandler = new LoadDecksByUserIdAndGameIdQueryHandler(deckRepository, catalogRepository);
    const presenter = new LoadDecksByUserIdAndGameIdWebPresenter();

    await queryHandler.handle({gameId, userId: ctx.userId, locale}, presenter);

    return presenter.getViewModel();
  },
});