import {v} from 'convex/values';
import {protectedMutation} from '@/convex/helpers';
import {ConvexGameRepository} from '@/src/server/GameManagement/infrastructure/repositories/Game/ConvexGameRepository';
import {DeleteGameCommandHandler} from '@/src/server/GameManagement/application/commands/DeleteGame/DeleteGameCommandHandler';

export const endpoint = protectedMutation({
  args: {gameId: v.id('games')},
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexGameRepository(ctx);
    const handler = new DeleteGameCommandHandler(repository);
    await handler.handle({gameId: gameId as string, userId: ctx.userId});
  },
});
