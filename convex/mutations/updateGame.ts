import {v} from 'convex/values';
import {protectedMutation} from '@/convex/helpers';
import {ConvexGameRepository} from '@/src/server/GameManagement/infrastructure/repositories/Game/ConvexGameRepository';
import {UpdateGameCommandHandler} from '@/src/server/GameManagement/application/commands/UpdateGame/UpdateGameCommandHandler';

export const endpoint = protectedMutation({
  args: {gameId: v.id('games'), name: v.string()},
  handler: async (ctx, {gameId, name}) => {
    const repository = new ConvexGameRepository(ctx);
    const handler = new UpdateGameCommandHandler(repository);
    await handler.handle({gameId: gameId as string, userId: ctx.userId, name});
  },
});
