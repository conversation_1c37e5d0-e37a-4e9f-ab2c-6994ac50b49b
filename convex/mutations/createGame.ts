import {v} from 'convex/values';
import {protectedMutation} from '@/convex/helpers';
import {ConvexGameRepository} from '@/src/server/GameManagement/infrastructure/repositories/Game/ConvexGameRepository';
import {CreateGameCommandHandler} from '@/src/server/GameManagement/application/commands/CreateGame/CreateGameCommandHandler';
import {CreateGameCommand} from "@/src/server/GameManagement/application/commands/CreateGame/CreateGameCommand";

export const endpoint = protectedMutation({
  args: {name: v.string()},
  handler: async (ctx, {name}) => {
    const repository = new ConvexGameRepository(ctx);
    const handler = new CreateGameCommandHandler(repository);
    const command: CreateGameCommand = {ownerId: ctx.userId, name};
    return handler.handle(command);
  },
});
