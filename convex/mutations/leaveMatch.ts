import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {LeaveMatchCommandHandler} from "@/src/server/Gaming/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";
import {ConvexEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/ConvexEventBus";

export const endpoint = protectedMutation({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchRepository(ctx);
    const eventBus = new ConvexEventBus(ctx);
    const handler = new LeaveMatchCommandHandler(eventBus, repository);
    await handler.handle({matchId: matchId as string, userId: ctx.userId});
  },
});
