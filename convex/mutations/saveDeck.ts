import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {ConvexDeckRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository";
import {SaveDeckCommandHandler} from "@/src/server/DeckBuilding/application/commands/SaveDeck/SaveDeckCommandHandler";

export const endpoint = protectedMutation({
  args: {
    gameId: v.id("games"),
    name: v.string(),
    tags: v.array(v.string()),
    cards: v.array(v.object({cardId: v.string(), quantity: v.number()})),
  },
  handler: async (ctx, {gameId, name, tags, cards}) => {
    const repository = new ConvexDeckRepository(ctx);
    const handler = new SaveDeckCommandHandler(repository);
    return handler.handle({
      gameId: gameId as string,
      playerId: ctx.userId,
      name,
      tags,
      cards,
    });
  },
});
