import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {SubmitMulliganSelectionCommandHandler} from "@/src/server/Gaming/application/commands/Mulligan/SubmitMulliganSelection/SubmitMulliganSelectionCommandHandler";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";
import {ConvexGameDeckRepository} from "@/src/server/Gaming/infrastructure/repositories/GameDeck/ConvexGameDeckRepository";
import {ConvexGameDeckReadRepository} from "@/src/server/Gaming/infrastructure/repositories/GameDeck/ConvexGameDeckReadRepository";
import {ConvexMulliganSelectionRepository} from "@/src/server/Gaming/infrastructure/repositories/MulliganSelection/ConvexMulliganSelectionRepository";
import {ConvexGameSettingsRepository} from "@/src/server/Gaming/infrastructure/repositories/GameSettings/ConvexGameSettingsRepository";

export const endpoint = protectedMutation({
  args: {
    matchId: v.string(),
    selectedCardIds: v.array(v.string()),
    skipped: v.boolean(),
    round: v.number(),
  },
  handler: async (ctx, {matchId, selectedCardIds, skipped, round}) => {
    const matchRepository = new ConvexMatchRepository(ctx);
    const deckRepository = new ConvexGameDeckRepository(ctx);
    const deckReadRepository = new ConvexGameDeckReadRepository(ctx);
    const mulliganRepository = new ConvexMulliganSelectionRepository(ctx);
    const gameSettingsRepository = new ConvexGameSettingsRepository(ctx);
    
    const handler = new SubmitMulliganSelectionCommandHandler(
      matchRepository,
      deckRepository,
      deckReadRepository,
      mulliganRepository,
      gameSettingsRepository
    );
    
    await handler.handle({
      matchId,
      playerId: ctx.userId,
      selectedCardIds,
      skipped,
      round
    });
  },
});