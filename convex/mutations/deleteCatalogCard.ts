import {v} from 'convex/values';
import {protectedMutation} from '@/convex/helpers';
import {ConvexGameRepository} from '@/src/server/GameManagement/infrastructure/repositories/Game/ConvexGameRepository';
import {ConvexCatalogCardRepository} from '@/src/server/CatalogManagement/infrastructure/repositories/CatalogCard/ConvexCatalogCardRepository';
import {DeleteCatalogCardCommandHandler} from '@/src/server/CatalogManagement/application/commands/DeleteCatalogCard/DeleteCatalogCardCommandHandler';

export const endpoint = protectedMutation({
  args: {cardId: v.id('catalogCards')},
  handler: async (ctx, {cardId}) => {
    const games = new ConvexGameRepository(ctx);
    const cards = new ConvexCatalogCardRepository(ctx);
    const handler = new DeleteCatalogCardCommandHandler(games, cards);
    await handler.handle({userId: ctx.userId, cardId: cardId as string});
  },
});
