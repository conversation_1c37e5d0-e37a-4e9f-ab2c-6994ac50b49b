import {v} from 'convex/values';
import {protectedMutation} from '@/convex/helpers';
import {ConvexGameRepository} from '@/src/server/GameManagement/infrastructure/repositories/Game/ConvexGameRepository';
import {ConvexCatalogCardRepository} from '@/src/server/CatalogManagement/infrastructure/repositories/CatalogCard/ConvexCatalogCardRepository';
import {UpdateCatalogCardCommandHandler} from '@/src/server/CatalogManagement/application/commands/UpdateCatalogCard/UpdateCatalogCardCommandHandler';

export const endpoint = protectedMutation({
  args: {
    cardId: v.id('catalogCards'),
    name: v.string(),
    image: v.string(),
    minDeckQuantity: v.number(),
    maxDeckQuantity: v.number(),
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const games = new ConvexGameRepository(ctx);
    const cards = new ConvexCatalogCardRepository(ctx);
    const handler = new UpdateCatalogCardCommandHandler(games, cards);
    await handler.handle({userId: ctx.userId, ...args, cardId: args.cardId as string});
  },
});
