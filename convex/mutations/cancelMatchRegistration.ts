import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {CancelMatchRegistrationCommandHandler} from "@/src/server/MatchMaking/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler";
import {ConvexMatchmakingQueueRepository} from "@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {ConvexEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/ConvexEventBus";

export const endpoint = protectedMutation({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexMatchmakingQueueRepository(ctx);
    const eventBus = new ConvexEventBus(ctx);
    const handler = new CancelMatchRegistrationCommandHandler(eventBus, repository);
    await handler.handle({gameId, userId: ctx.userId});
  },
});
