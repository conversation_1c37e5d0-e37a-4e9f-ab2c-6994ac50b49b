import {v} from 'convex/values';
import {protectedMutation} from '@/convex/helpers';
import {ConvexGameRepository} from '@/src/server/GameManagement/infrastructure/repositories/Game/ConvexGameRepository';
import {ConvexCatalogCardRepository} from '@/src/server/CatalogManagement/infrastructure/repositories/CatalogCard/ConvexCatalogCardRepository';
import {CreateCatalogCardCommandHandler} from '@/src/server/CatalogManagement/application/commands/CreateCatalogCard/CreateCatalogCardCommandHandler';

export const endpoint = protectedMutation({
  args: {
    gameId: v.id('games'),
    name: v.string(),
    image: v.string(),
    minDeckQuantity: v.number(),
    maxDeckQuantity: v.number(),
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const games = new ConvexGameRepository(ctx);
    const cards = new ConvexCatalogCardRepository(ctx);
    const handler = new CreateCatalogCardCommandHandler(games, cards);
    return handler.handle({userId: ctx.userId, ...args, gameId: args.gameId as string});
  },
});
