/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as dispatchers_match from "../dispatchers/match.js";
import type * as dispatchers_matchmaking from "../dispatchers/matchmaking.js";
import type * as dispatchers_sagas_matchCreatedSaga from "../dispatchers/sagas/matchCreatedSaga.js";
import type * as dispatchers_sagas_matchEndedSaga from "../dispatchers/sagas/matchEndedSaga.js";
import type * as dispatchers_sagas_playerAddedToMatchingQueueSaga from "../dispatchers/sagas/playerAddedToMatchingQueueSaga.js";
import type * as dispatchers_sagas_playerRegistrationCancelledSaga from "../dispatchers/sagas/playerRegistrationCancelledSaga.js";
import type * as helpers from "../helpers.js";
import type * as http from "../http.js";
import type * as mutations_addPlayerToMatchMakingQueue from "../mutations/addPlayerToMatchMakingQueue.js";
import type * as mutations_cancelMatchRegistration from "../mutations/cancelMatchRegistration.js";
import type * as mutations_createCatalogCard from "../mutations/createCatalogCard.js";
import type * as mutations_createDeck from "../mutations/createDeck.js";
import type * as mutations_createGame from "../mutations/createGame.js";
import type * as mutations_deleteCatalogCard from "../mutations/deleteCatalogCard.js";
import type * as mutations_deleteGame from "../mutations/deleteGame.js";
import type * as mutations_leaveMatch from "../mutations/leaveMatch.js";
import type * as mutations_playCard from "../mutations/playCard.js";
import type * as mutations_saveDeck from "../mutations/saveDeck.js";
import type * as mutations_submitMulliganSelection from "../mutations/submitMulliganSelection.js";
import type * as mutations_updateCatalogCard from "../mutations/updateCatalogCard.js";
import type * as mutations_updateDeck from "../mutations/updateDeck.js";
import type * as mutations_updateGame from "../mutations/updateGame.js";
import type * as queries_auth from "../queries/auth.js";
import type * as queries_board from "../queries/board.js";
import type * as queries_catalog from "../queries/catalog.js";
import type * as queries_debug from "../queries/debug.js";
import type * as queries_deck from "../queries/deck.js";
import type * as queries_deckBuilder from "../queries/deckBuilder.js";
import type * as queries_gaming from "../queries/gaming.js";
import type * as queries_match from "../queries/match.js";
import type * as queries_matchMakingQueue from "../queries/matchMakingQueue.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  "dispatchers/match": typeof dispatchers_match;
  "dispatchers/matchmaking": typeof dispatchers_matchmaking;
  "dispatchers/sagas/matchCreatedSaga": typeof dispatchers_sagas_matchCreatedSaga;
  "dispatchers/sagas/matchEndedSaga": typeof dispatchers_sagas_matchEndedSaga;
  "dispatchers/sagas/playerAddedToMatchingQueueSaga": typeof dispatchers_sagas_playerAddedToMatchingQueueSaga;
  "dispatchers/sagas/playerRegistrationCancelledSaga": typeof dispatchers_sagas_playerRegistrationCancelledSaga;
  helpers: typeof helpers;
  http: typeof http;
  "mutations/addPlayerToMatchMakingQueue": typeof mutations_addPlayerToMatchMakingQueue;
  "mutations/cancelMatchRegistration": typeof mutations_cancelMatchRegistration;
  "mutations/createCatalogCard": typeof mutations_createCatalogCard;
  "mutations/createDeck": typeof mutations_createDeck;
  "mutations/createGame": typeof mutations_createGame;
  "mutations/deleteCatalogCard": typeof mutations_deleteCatalogCard;
  "mutations/deleteGame": typeof mutations_deleteGame;
  "mutations/leaveMatch": typeof mutations_leaveMatch;
  "mutations/playCard": typeof mutations_playCard;
  "mutations/saveDeck": typeof mutations_saveDeck;
  "mutations/submitMulliganSelection": typeof mutations_submitMulliganSelection;
  "mutations/updateCatalogCard": typeof mutations_updateCatalogCard;
  "mutations/updateDeck": typeof mutations_updateDeck;
  "mutations/updateGame": typeof mutations_updateGame;
  "queries/auth": typeof queries_auth;
  "queries/board": typeof queries_board;
  "queries/catalog": typeof queries_catalog;
  "queries/debug": typeof queries_debug;
  "queries/deck": typeof queries_deck;
  "queries/deckBuilder": typeof queries_deckBuilder;
  "queries/gaming": typeof queries_gaming;
  "queries/match": typeof queries_match;
  "queries/matchMakingQueue": typeof queries_matchMakingQueue;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
