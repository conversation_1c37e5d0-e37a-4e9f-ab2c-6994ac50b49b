import fs from "fs";
import path from "path";
import https from "https";
import { Card, SetData } from "./types";

const sourceRoot = path.resolve(__dirname, "source");
const destinationRoot = path.resolve(__dirname, "../../public/game-assets/cards");
const imageType = "thumbnail"; // full, thumbnail, foilMask

const downloadImage = (url: string, outputPath: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (fs.existsSync(outputPath)) return resolve();

    const file = fs.createWriteStream(outputPath);
    https.get(url, (res) => {
      if (res.statusCode !== 200) {
        file.close();
        if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
        return reject(new Error(`HTTP ${res.statusCode} - ${url}`));
      }

      res.pipe(file);
      file.on("finish", () => {
        file.close();
        resolve();
      });
    }).on("error", (err) => {
      file.close();
      if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
      reject(err);
    });
  });
};

const processLanguage = async (lang: string) => {
  const langSourcePath = path.resolve(sourceRoot, lang, "allCards.json");
  const langDestinationPath = path.resolve(destinationRoot, lang, "allCards.json");
  const imagesDir = path.resolve(destinationRoot, lang, imageType);

  if (!fs.existsSync(langSourcePath)) {
    console.warn(`⚠️  Fichier source introuvable pour la langue "${lang}"`);
    return;
  }

  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
  }

  const sourceData = fs.readFileSync(langSourcePath, "utf8");
  const parsed: SetData = JSON.parse(sourceData);

  const transformed = parsed.cards.map((card: Card) => ({
    name: card.fullName,
    image: `${card.id}.jpg`,
    language: lang,
    gameId: 'm1791s0ekbnefxjhbr22atm5mh7gx6b2',
    data: {
      cardId: card.id,
      color: card.color.replace("é", "e").toUpperCase(),
      cost: card.cost,
      inkable: card.inkwell,
      lore: card.lore,
      strength: card.strength,
      willpower: card.willpower,
      type: card.type.toUpperCase(),
      subtypes: card.subtypes ? card.subtypes.map((s) => s.toUpperCase()) : [],
    }
  }));

  const downloadPromises = parsed.cards.map((card) => {
    const imagePath = path.resolve(imagesDir, `${card.id}.jpg`);
    return downloadImage(card.images[imageType], imagePath)
      .then(() => console.log(`🖼️  [${lang}] ${card.id}.jpg téléchargée.`))
      .catch((err) => console.error(`❌ [${lang}] ${card.id}.jpg:`, err.message));
  });

  await Promise.allSettled(downloadPromises);

  fs.writeFileSync(langDestinationPath, JSON.stringify(transformed, null, 2), "utf8");
  console.log(`✅ Fichier JSON écrit pour "${lang}" à : ${langDestinationPath}`);
};

const main = async () => {
  const languageDirs = fs.readdirSync(sourceRoot).filter((entry) => {
    const fullPath = path.resolve(sourceRoot, entry);
    return fs.statSync(fullPath).isDirectory();
  });

  for (const lang of languageDirs) {
    await processLanguage(lang);
  }
};

main().catch((err) => {
  console.error("❌ Erreur générale :", err);
  process.exit(1);
});