export interface Ability {
  effect?: string;
  fullText: string;
  name?: string;
  type: 'static' | 'keyword' | 'triggered' | 'activated';
  keyword?: string;
  keywordValue?: string;
  keywordValueNumber?: number;
  reminderText?: string;
  costs?: string[];
  costsText?: string;
}

export interface ExternalLinks {
  cardTraderId?: number;
  cardTraderUrl?: string;
  cardmarketId?: number;
  cardmarketUrl?: string;
  tcgPlayerId?: number;
  tcgPlayerUrl?: string;
}

export interface Images {
  full: string;
  thumbnail: string;
  foilMask: string;
}

export interface Card {
  abilities: Ability[];
  setCode: string;
  artists: string[];
  artistsText: string;
  code: string;
  color: string;
  cost: number;
  externalLinks?: ExternalLinks;
  flavorText?: string;
  foilTypes: ('None' | 'Cold' | 'Lava' | 'Silver' | 'Tempest' | 'FreeForm')[];
  fullIdentifier: string;
  fullName: string;
  fullText: string;
  fullTextSections: string[];
  id: number;
  images: Images;
  inkwell: boolean;
  lore: number;
  name: string;
  number: number;
  rarity: 'Common' | 'Uncommon' | 'Rare' | 'Super Rare' | 'Legendary' | 'Enchanted' | 'Special';
  simpleName: string;
  story: string;
  strength: number;
  subtypes: string[];
  subtypesText: string;
  type: 'Character' | 'Action' | 'Item';
  version: string;
  willpower: number;
  keywordAbilities?: string[];
  enchantedId?: number;
  promoIds?: number[];
  clarifications?: string[];
  errata?: string[];
  effects?: string[];
  promoGrouping?: string;
  nonPromoId?: number;
  isExternalReveal?: boolean;
  historicData?: {
    effects: string[];
    fullText: string;
    fullTextSections: string[];
    usedUntil: string;
  }[]
}

export interface Metadata {
  formatVersion: string;
  generatedOn: string;
  language: string;
}

export interface SetData {
  prereleaseDate: string;
  releaseDate: string;
  hasAllCards: boolean;
  type: 'expansion';
  number: number;
  name: string;
  code: string;
  metadata: Metadata;
  cards: Card[];
}