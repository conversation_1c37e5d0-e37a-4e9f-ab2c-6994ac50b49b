import fs from "fs";
import path from "path";

const toBeExcluded = ["node_modules", "dist", ".git", ".next"];

type Counters = { candidates: number; deleted: number; excludedHits: number; failed: number };
type Options = { dryRun: boolean; root: string; excluded: Set<string> };

const getRoot = () => process.cwd();
const getExcluded = () => new Set(toBeExcluded);
const parseArgs = (argv: string[]) => new Set(argv);
const isDryRun = (args: Set<string>) => args.has("--dry") || args.has("--dry-run");
const toRelative = (root: string, p: string) => path.relative(root, p) || p;

const safeLstat = async (p: string) => {
  try {
    return await fs.promises.lstat(p);
  } catch {
    return undefined;
  }
};

const isSymlink = async (p: string) => {
  const st = await safeLstat(p);
  return st ? st.isSymbolicLink() : false;
};

const readDir = async (p: string) => {
  try {
    return await fs.promises.readdir(p, { withFileTypes: true });
  } catch {
    return [] as fs.Dirent[];
  }
};

const containsExcludedPart = (root: string, excluded: Set<string>, p: string) => {
  const rel = path.relative(root, p);
  if (!rel || rel.startsWith("..")) return false;
  const parts = rel.split(path.sep);
  return parts.some((part) => excluded.has(part));
};

const removeDir = async (p: string) => {
  try {
    await fs.promises.rm(p, { recursive: false, force: false });
    return { ok: true, error: undefined as undefined | string };
  } catch (e) {
    try {
      await fs.promises.rmdir(p);
      return { ok: true, error: undefined };
    } catch (e2) {
      const msg = e2 instanceof Error ? e2.message : String(e2);
      return { ok: false, error: msg };
    }
  }
};

const logDry = (root: string, p: string) => console.log(`[dry] ${toRelative(root, p)}`);
const logDeleted = (root: string, p: string) => console.log(`[deleted] ${toRelative(root, p)}`);

const removeIfEmpty = async (dir: string, isRoot: boolean, options: Options, counters: Counters): Promise<boolean> => {
  if (await isSymlink(dir)) return false;
  const entries = await readDir(dir);
  let isEmpty = true;
  for (const entry of entries) {
    const full = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      if (containsExcludedPart(options.root, options.excluded, full)) {
        isEmpty = false;
        counters.excludedHits += 1;
        continue;
      }
      const removed = await removeIfEmpty(full, false, options, counters);
      if (!removed) isEmpty = false;
    } else {
      isEmpty = false;
    }
  }
  if (isEmpty && !isRoot && !containsExcludedPart(options.root, options.excluded, dir)) {
    counters.candidates += 1;
    if (options.dryRun) {
      logDry(options.root, dir);
      return true;
    }
    const res = await removeDir(dir);
    if (res.ok) {
      counters.deleted += 1;
      logDeleted(options.root, dir);
      return true;
    }
    counters.failed += 1;
    if (res.error) console.log(`[error] ${toRelative(options.root, dir)} :: ${res.error}`);
  }
  return false;
};

const printStart = (dry: boolean) => console.log(dry ? "Clean dry-run" : "Clean start");
const printSummary = (counters: Counters, dry: boolean) => {
  console.log(
    JSON.stringify({ candidates: counters.candidates, deleted: counters.deleted, failed: counters.failed, excludedHits: counters.excludedHits, mode: dry ? "dry" : "apply" })
  );
};

const main = async () => {
  const root = getRoot();
  const args = parseArgs(process.argv.slice(2));
  const options: Options = { dryRun: isDryRun(args), root, excluded: getExcluded() };
  const counters: Counters = { candidates: 0, deleted: 0, excludedHits: 0, failed: 0 };
  printStart(options.dryRun);
  await removeIfEmpty(root, true, options, counters);
  printSummary(counters, options.dryRun);
};

main();
