import {SaveDeckCommand} from "./SaveDeckCommand";
import {DeckRepository} from "@/src/server/DeckBuilding/application/ports/DeckRepository";
import {Deck} from "@/src/server/DeckBuilding/domain/Deck/Deck";
export class SaveDeckCommandHandler {
  private readonly repository: DeckRepository;
  constructor(repository: DeckRepository) {
    this.repository = repository;
  }

  handle(command: SaveDeckCommand) {
    return this.repository.save(
      Deck.create({
        gameId: command.gameId,
        playerId: command.playerId,
        name: command.name,
        tags: command.tags,
        cards: command.cards,
      })
    );
  }
}
