import {DeckRepository} from '@/src/server/DeckBuilding/application/ports/DeckRepository';
import {DeckNotOwnedError} from '@/src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError';
import {UpdateDeckCommand} from './UpdateDeckCommand';

export class UpdateDeckCommandHandler {
  private readonly repository: DeckRepository;

  constructor(repository: DeckRepository) {
    this.repository = repository;
  }

  async handle({deckId, userId, name, cards, tags}: UpdateDeckCommand) {
    const existingDeck = await this.repository.findById(deckId);
    if (!existingDeck) return;
    if (!existingDeck.isOwnedBy(userId)) {
      throw new DeckNotOwnedError();
    }
    existingDeck.renameTo(name);
    existingDeck.useTags(tags);
    existingDeck.useCards(cards);
    await this.repository.save(existingDeck);
  }
}
