import {
  LoadDecksByUserIdAndGameIdQuery
} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQuery";
import {DeckListRepository} from "@/src/server/DeckBuilding/application/ports/DeckListRepository";
import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";
import {
  LoadDecksByUserIdAndGameIdPresenter
} from "@/src/server/DeckBuilding/application/ports/LoadDecksByUserIdAndGameIdPresenter";
import {DeckList} from "@/src/server/DeckBuilding/domain/Deck/DeckList";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";

export class LoadDecksByUserIdAndGameIdQueryHandler {
  private readonly deckRepository: DeckListRepository;
  private readonly catalogRepository: CatalogCardListRepository;

  constructor(
    deckRepository: DeckListRepository,
    catalogRepository: CatalogCardListRepository,
  ) {
    this.deckRepository = deckRepository;
    this.catalogRepository = catalogRepository;
  }

  async handle(query: LoadDecksByUserIdAndGameIdQuery, presenter: LoadDecksByUserIdAndGameIdPresenter) {
    try {
      const deckList = await this.deckRepository.getByUserIdAndGameId(query.userId, query.gameId);
      const catalog = await this.catalogRepository.getByGameId(query.gameId);

      deckList.sortByName();

      const deckPreviewList = this.generateDeckPreviews(deckList, catalog, query);

      presenter.displayDeckPreviewList(deckPreviewList);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }

  private generateDeckPreviews(deckList: DeckList, catalog: CatalogCardList, query: LoadDecksByUserIdAndGameIdQuery) {
    return {
      decks: deckList.mapEachDeck(deck => {
        const snapshot = deck.toSnapshot();
        const images = this.extractFirstThreeCardImages(snapshot.cards, catalog, query.locale);
        return {...snapshot, images};
      })
    };
  }

  private extractFirstThreeCardImages(cards: { cardId: string }[], catalog: CatalogCardList, locale: string): string[] {
    const images: string[] = [];
    for (const {cardId} of cards) {
      const card = catalog.findCardById(cardId);
      if (card) {
        images.push(`/game-assets/cards/${locale}/thumbnail/${card.image}`);
        if (images.length === 3) break;
      }
    }
    return images;
  }
}
