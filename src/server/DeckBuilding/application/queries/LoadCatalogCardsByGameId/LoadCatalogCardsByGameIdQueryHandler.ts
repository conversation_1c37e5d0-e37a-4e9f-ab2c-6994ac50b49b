import {LoadCatalogCardsByGameIdQuery} from "@/src/server/DeckBuilding/application/queries/LoadCatalogCardsByGameId/LoadCatalogCardsByGameIdQuery";
import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";
import {LoadCatalogCardsByGameIdPresenter} from "@/src/server/DeckBuilding/application/ports/LoadCatalogCardsByGameIdPresenter";

export class LoadCatalogCardsByGameIdQueryHandler {
  private readonly repository: CatalogCardListRepository;

  constructor(repository: CatalogCardListRepository) {
    this.repository = repository;
  }

  async handle(
    {gameId}: LoadCatalogCardsByGameIdQuery,
    presenter: LoadCatalogCardsByGameIdPresenter
  ) {
    try {
      const cards = await this.repository.getByGameId(gameId);
      presenter.display(cards);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
