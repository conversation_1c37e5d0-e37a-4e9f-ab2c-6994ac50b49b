import {LoadDeckBuilderSettingsByIdQuery} from '@/src/server/DeckBuilding/application/queries/LoadDeckBuilderSettingsById/LoadDeckBuilderSettingsByIdQuery';
import {DeckBuilderSettingsRepository} from '@/src/server/DeckBuilding/application/ports/DeckBuilderSettingsRepository';
import {LoadDeckBuilderSettingsByIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDeckBuilderSettingsByIdPresenter';

export class LoadDeckBuilderSettingsByIdQueryHandler {
  private readonly repository: DeckBuilderSettingsRepository;

  constructor(repository: DeckBuilderSettingsRepository) {
    this.repository = repository;
  }

  async handle({gameId}: LoadDeckBuilderSettingsByIdQuery, presenter: LoadDeckBuilderSettingsByIdPresenter) {
    try {
      const settings = await this.repository.getByGameId(gameId);
      presenter.display(settings);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
