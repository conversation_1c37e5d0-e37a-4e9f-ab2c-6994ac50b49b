import {LoadCatalogCardsByGameIdQueryHandler} from '@/src/server/DeckBuilding/application/queries/LoadCatalogCardsByGameId/LoadCatalogCardsByGameIdQueryHandler';
import {InMemoryCatalogCardListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/InMemoryCatalogCardListRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadCatalogCardsByGameIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadCatalogCardsByGameIdPresenter';
import {InMemoryFailingCatalogCardListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/InMemoryFailingCatalogCardListRepository';
import {CatalogCard} from '@/src/server/DeckBuilding/domain/Catalog/CatalogCard';
import {CARD_ONE, CARD_TWO} from '@/src/server/Shared/specs/helpers/fakes/fakeCatalogCards';

describe('LoadCatalogCardsByGameIdQueryHandler', () => {
  describe('When retrieving cards successfully', () => {
    let repository: InMemoryCatalogCardListRepository;
    let presenter: MockProxy<LoadCatalogCardsByGameIdPresenter>;
    let handler: LoadCatalogCardsByGameIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryCatalogCardListRepository();
      presenter = mock<LoadCatalogCardsByGameIdPresenter>();
      handler = new LoadCatalogCardsByGameIdQueryHandler(repository);
    });

    describe('When cards exist for the game', () => {
      it('should display them', async () => {
        // Arrange
        const gameId = 'game-123';
        const cards: CatalogCard[] = [CARD_ONE, CARD_TWO];
        repository.addCardsForGame(gameId, cards);

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({cards});
      });
    });

    describe('When no cards exist for the game', () => {
      it('should return an empty list', async () => {
        // Arrange
        const gameId = 'game-123';

        // Act
        await handler.handle({gameId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({cards: []});
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingCatalogCardListRepository();
      const presenter = mock<LoadCatalogCardsByGameIdPresenter>();
      const handler = new LoadCatalogCardsByGameIdQueryHandler(repository);

      // Act
      await handler.handle({gameId: 'game-123'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('game-123'));
    });
  });
});
