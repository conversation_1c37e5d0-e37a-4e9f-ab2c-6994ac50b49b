import {LoadDecksByUserIdAndGameIdQueryHandler} from '@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQueryHandler';
import {InMemoryDeckListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/DeckList/InMemoryDeckListRepository';
import {InMemoryCatalogCardListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/InMemoryCatalogCardListRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadDecksByUserIdAndGameIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDecksByUserIdAndGameIdPresenter';
import {InMemoryFailingDeckListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/DeckList/InMemoryFailingDeckListRepository';
import {LoadDecksByUserIdAndGameIdResponse} from '@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdResponse';
import {DECK_A, DECK_B} from '@/src/server/DeckBuilding/specs/helpers/fakes/fakeDecks';

describe('LoadDecksByUserIdAndGameIdQueryHandler', () => {
  describe('When retrieving decks successfully', () => {
    let repository: InMemoryDeckListRepository;
    let catalogRepository: InMemoryCatalogCardListRepository;
    let presenter: MockProxy<LoadDecksByUserIdAndGameIdPresenter>;
    let handler: LoadDecksByUserIdAndGameIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryDeckListRepository();
      catalogRepository = new InMemoryCatalogCardListRepository();
      presenter = mock<LoadDecksByUserIdAndGameIdPresenter>();
      handler = new LoadDecksByUserIdAndGameIdQueryHandler(repository, catalogRepository);
    });

    describe('When decks exist for the user and game', () => {
      it('should display them ordered by name', async () => {
        // Arrange
        const gameId = 'game-123';
        const userId = 'user-456';
        const decks = [DECK_B, DECK_A];
        repository.addDecksFor(userId, gameId, decks);

        // Act
        await handler.handle({gameId, userId, locale: 'en'}, presenter);

        // Assert
        const call = presenter.displayDeckPreviewList.mock.calls[0][0] as LoadDecksByUserIdAndGameIdResponse;
        expect(call.decks.map(d => d.name)).toEqual(['A Deck', 'B Deck']);
      });
    });

    describe('When no decks exist for the user and game', () => {
      it('should return an empty list', async () => {
        // Arrange
        const gameId = 'game-123';
        const userId = 'user-456';

        // Act
        await handler.handle({gameId, userId, locale: 'en'}, presenter);

        // Assert
        const call = presenter.displayDeckPreviewList.mock.calls[0][0] as LoadDecksByUserIdAndGameIdResponse;
        expect(call.decks).toEqual([]);
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingDeckListRepository();
      const catalogRepository = new InMemoryCatalogCardListRepository();
      const presenter = mock<LoadDecksByUserIdAndGameIdPresenter>();
      const handler = new LoadDecksByUserIdAndGameIdQueryHandler(repository, catalogRepository);

      // Act
      await handler.handle({gameId: 'g1', userId: 'u1', locale: 'en'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('u1-g1'));
    });
  });
});
