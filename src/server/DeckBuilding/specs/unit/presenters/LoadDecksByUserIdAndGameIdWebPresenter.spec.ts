import {LoadDecksByUserIdAndGameIdWebPresenter} from '@/src/server/DeckBuilding/presentation/presenters/LoadDecksByUserIdAndGameIdWebPresenter';
import {LoadDecksByUserIdAndGameIdResponse} from '@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdResponse';

describe('LoadDecksByUserIdAndGameIdWebPresenter', () => {
  let presenter: LoadDecksByUserIdAndGameIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadDecksByUserIdAndGameIdWebPresenter();
  });

  describe('When displaying decks', () => {
    it('should set deck data', () => {
      // Arrange
      const response: LoadDecksByUserIdAndGameIdResponse = {
        decks: [
          {id: 'd1', gameId: 'g1', playerId: 'p1', name: 'Deck 1', cards: [], images: []},
          {id: 'd2', gameId: 'g1', playerId: 'p1', name: 'Deck 2', cards: [], images: []},
        ],
      };

      // Act
      presenter.displayDeckPreviewList(response);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual(response);
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
