import {LoadDeckByIdWebPresenter} from '@/src/server/DeckBuilding/presentation/presenters/LoadDeckByIdWebPresenter';
import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';

describe('LoadDeckByIdWebPresenter', () => {
  let presenter: LoadDeckByIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadDeckByIdWebPresenter();
  });

  describe('When displaying a deck', () => {
    it('should set deck data', () => {
      // Arrange
      const deck = Deck.fromSnapshot({
        id: 'd1',
        gameId: 'g1',
        playerId: 'p1',
        name: 'My deck',
        tags: [],
        cards: []
      });

      // Act
      presenter.display(deck);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({
        id: 'd1',
        gameId: 'g1',
        playerId: 'p1',
        name: 'My deck',
        tags: [],
        cards: []
      });
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
