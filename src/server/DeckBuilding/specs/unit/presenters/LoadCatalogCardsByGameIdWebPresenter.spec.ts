import {LoadCatalogCardsByGameIdWebPresenter} from "@/src/server/DeckBuilding/presentation/presenters/LoadCatalogCardsByGameIdWebPresenter";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";
import {CARD_ONE, CARD_TWO} from "@/src/server/Shared/specs/helpers/fakes/fakeCatalogCards";

describe('LoadCatalogCardsByGameIdWebPresenter', () => {
  let presenter: LoadCatalogCardsByGameIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadCatalogCardsByGameIdWebPresenter();
  });

  describe('When displaying cards', () => {
    it('should set card data', () => {
      // Arrange
      const cards = CatalogCardList.createFrom([CARD_ONE, CARD_TWO]);

      // Act
      presenter.display(cards);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({cards: [CARD_ONE, CARD_TWO]});
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
