import {UpdateDeckCommandHandler} from '@/src/server/DeckBuilding/application/commands/UpdateDeck/UpdateDeckCommandHandler';
import {UpdateDeckCommand} from '@/src/server/DeckBuilding/application/commands/UpdateDeck/UpdateDeckCommand';
import {InMemoryDeckRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/Deck/InMemoryDeckRepository';
import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';
import {DeckNotOwnedError} from '@/src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError';

let repository: InMemoryDeckRepository;
let handler: UpdateDeckCommandHandler;

beforeEach(() => {
  repository = new InMemoryDeckRepository();
  handler = new UpdateDeckCommandHandler(repository);
});

describe('UpdateDeckCommandHandler', () => {
  describe('When updating an existing deck', () => {
    it('should persist the new values', async () => {
      // Arrange
      repository.addDeck({id: 'd1', gameId: 'g1', playerId: 'p1', name: 'Old', tags: [], cards: []});
      const command: UpdateDeckCommand = {deckId: 'd1', userId: 'p1', name: 'New', tags: ['control'], cards: [{cardId: 'c1', quantity: 2}]};

      // Act
      await handler.handle(command);

      // Assert
      const updatedDeck = ((repository as unknown) as {items: Map<string, Deck>}).items.get('d1');
      expect(updatedDeck?.toSnapshot()).toEqual({id: 'd1', gameId: 'g1', playerId: 'p1', name: 'New', tags: ['control'], cards: [{cardId: 'c1', quantity: 2}]});
    });
  });

  describe('When the user is not the owner', () => {
    it('should throw an error', async () => {
      // Arrange
      repository.addDeck({id: 'd1', gameId: 'g1', playerId: 'p1', name: 'Deck', tags: [], cards: []});
      const command: UpdateDeckCommand = {deckId: 'd1', userId: 'p2', name: 'Hack', tags: [], cards: []};

      // Act
      const executing = handler.handle(command);

      // Assert
      await expect(executing).rejects.toThrow(new DeckNotOwnedError());
    });
  });

  describe('When the deck does not exist', () => {
    it('should do nothing', async () => {
      // Arrange
      const command: UpdateDeckCommand = {deckId: 'missing', userId: 'p1', name: 'X', tags: [], cards: []};

      // Act
      await handler.handle(command);

      // Assert
      const remainingDecks = ((repository as unknown) as {items: Map<string, Deck>}).items.size;
      expect(remainingDecks).toBe(0);
    });
  });
});
