import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/server/Shared/specs/helpers/requests/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";
import {createFakeDeck} from "@/src/server/DeckBuilding/specs/helpers/createFakeDeck";
import {getAllFrom} from "@/src/server/Shared/specs/helpers/requests/getAllFrom";
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {DeckNotOwnedError} from "@/src/server/DeckBuilding/domain/Deck/errors/DeckNotOwnedError";

describe('updateDeck', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;
  let deckId: Id<'decks'>;
  let gameId: Id<'games'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    asSophie = test.withIdentity(SOPHIE_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createFakeGame({name: 'Lorcana'}, asAdmin) as Id<'games'>;
    deckId = await createFakeDeck({name: 'Deck', gameId, playerId: JOHN_APP_USER.appUserId}, asAdmin) as Id<'decks'>;
  });

  describe('When updating an existing deck', () => {
    it('should store the new content', async () => {
      // Act
      await updateDeck(asJohn, {deckId, name: 'Updated', tags: [], cards: [{cardId: 'c', quantity: 2}]});

      // Assert
      const decks = await getAllFrom('decks', asAdmin);
      expect(decks[0].name).toBe('Updated');
      expect(decks[0].cards).toEqual([{cardId: 'c', quantity: 2}]);
    });
  });

  describe('When updating tags', () => {
    it('should persist the new tags', async () => {
      // Act
      await updateDeck(asJohn, {deckId, name: 'Deck', tags: ['control'], cards: []});

      // Assert
      const decks = await getAllFrom('decks', asAdmin);
      expect(decks[0].tags).toEqual(['control']);
    });
  });

  describe('When another user tries to update the deck', () => {
    it('should return an error', async () => {
      // Act
      const updating = updateDeck(asSophie, {deckId, name: 'Hacked', tags: [], cards: []});

      // Assert
      await expect(updating).rejects.toThrow(new DeckNotOwnedError());
      const decks = await getAllFrom('decks', asAdmin);
      expect(decks[0].name).toBe('Deck');
    });
  });

  function updateDeck(user: TestConvexForDataModel<DataModel>, {
    deckId, name, tags, cards
  }: {deckId: Id<'decks'>; name: string; tags: string[]; cards: {cardId: string; quantity: number}[]}) {
    return user.mutation(api.mutations.updateDeck.endpoint, {deckId, name, tags, cards});
  }
});
