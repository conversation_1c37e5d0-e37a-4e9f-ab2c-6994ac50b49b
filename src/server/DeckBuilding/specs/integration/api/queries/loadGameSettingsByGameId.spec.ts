import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/server/Shared/specs/helpers/requests/createFakeGame";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";
import {createFakeGameSettings} from "@/src/server/DeckBuilding/specs/helpers/createFakeGameSettings";
import {api} from "@/convex/_generated/api";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {LORCANA} from "@/src/server/Shared/specs/helpers/fakes/fakeGames";

describe('loadGameSettingsByGameId', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When settings exist for the game', () => {
    it('should load them', async () => {
      // Arrange
      const gameId = await createFakeGame(LORCANA, asAdmin) as Id<'games'>;
      await createFakeGameSettings({gameId, maxCardsInDeck: 60}, asAdmin);

      // Act
      const result = await loadGameSettings(asSophie, gameId);

      // Assert
      expect(result.data!.maxCardsInDeck).toBe(60);
    });
  });

  describe('When settings do not exist for the game', () => {
    it('should return null', async () => {
      // Arrange
      const gameId = await createFakeGame(LORCANA, asAdmin) as Id<'games'>;

      // Act
      const result = await loadGameSettings(asSophie, gameId);

      // Assert
      expect(result.data).toBeNull();
    });
  });

  function loadGameSettings(user: TestConvexForDataModel<DataModel>, gameId: Id<'games'>) {
    return user.query(api.queries.gaming.loadGameSettingsByGameId, {gameId});
  }
});
