import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/server/Shared/specs/helpers/requests/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";
import {createFakeDeck} from "@/src/server/DeckBuilding/specs/helpers/createFakeDeck";
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";

describe('loadDeckById', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let deckId: Id<'decks'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    const gameId = await createFakeGame({name: 'Lorcana'}, asAdmin) as Id<'games'>;
    deckId = await createFakeDeck({name: 'Deck', gameId, playerId: JOHN_APP_USER.appUserId}, asAdmin) as Id<'decks'>;
  });

  describe('When the deck exists', () => {
    it('should load the deck information', async () => {
      // Act
      const deck = await loadDeckById(asJohn, deckId);

      // Assert
      expect(deck.data?.id).toBe(deckId);
    });
  });

  async function loadDeckById(user: TestConvexForDataModel<DataModel>, id: Id<'decks'>) {
    return user.query(api.queries.deck.loadDeckById, {deckId: id});
  }
});
