import {createFakeGame} from "@/src/server/Shared/specs/helpers/requests/createFakeGame";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import schema from "@/convex/schema";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";
import {LORCANA} from "@/src/server/Shared/specs/helpers/fakes/fakeGames";
import {createFakeDeck} from "@/src/server/DeckBuilding/specs/helpers/createFakeDeck";
import {createFakeCatalogCard} from "@/src/server/CatalogManagement/specs/helpers/createFakeCatalogCard";
import {api} from "@/convex/_generated/api";

describe('LoadDecksByUserIdAndGameId', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When the user has no deck for the game', () => {
    it('should return an empty list', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);

      // Act
      const result = await loadDecksByUserIdAndGameId(asSophie, lorcanaId);

      // Assert
      expect(result.data!.decks).toEqual([]);
    });
  });

  describe('When the user has decks for the game', () => {
    it('should return the list of decks with preview images', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const c1 = await createFakeCatalogCard({gameId: lorcanaId as Id<'games'>, name: 'Card 1', image: '1.jpg', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}}, asAdmin) as Id<'catalogCards'>;
      const c2 = await createFakeCatalogCard({gameId: lorcanaId as Id<'games'>, name: 'Card 2', image: '2.jpg', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}}, asAdmin) as Id<'catalogCards'>;
      const c3 = await createFakeCatalogCard({gameId: lorcanaId as Id<'games'>, name: 'Card 3', image: '3.jpg', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}}, asAdmin) as Id<'catalogCards'>;
      await createFakeDeck({name: 'Deck 1', gameId: lorcanaId, playerId: SOPHIE_APP_USER.appUserId, cards: [{cardId: c1, quantity:1}, {cardId: c2, quantity:1}, {cardId: c3, quantity:1}]}, asAdmin);
      await createFakeDeck({name: 'Deck 2', gameId: lorcanaId, playerId: SOPHIE_APP_USER.appUserId}, asAdmin);

      // Act
      const result = await loadDecksByUserIdAndGameId(asSophie, lorcanaId);

      // Assert
      expect(result.data!.decks[0].name).toBe('Deck 1');
      expect(result.data!.decks[1].name).toBe('Deck 2');
      expect(result.data!.decks[0].images).toEqual([
        '/game-assets/cards/en/thumbnail/1.jpg',
        '/game-assets/cards/en/thumbnail/2.jpg',
        '/game-assets/cards/en/thumbnail/3.jpg',
      ]);
    });
  });

  function loadDecksByUserIdAndGameId(currentUser: TestConvexForDataModel<DataModel>, gameId: string,) {
    return currentUser.query(api.queries.deck.loadDecksByUserIdAndGameId, {gameId: gameId as Id<"games">, locale: 'en'});
  }
});