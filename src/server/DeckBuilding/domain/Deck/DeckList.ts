import {Deck} from "@/src/server/DeckBuilding/domain/Deck/Deck";

export class DeckList {
  private readonly decks: Deck[];

  private constructor(decks: Deck[]) {
    this.decks = decks;
  }

  static createFrom(decks: Deck[]) {
    return new DeckList(decks);
  }

  sortByName() {
    this.decks.sort((a, b) =>
      a.toSnapshot().name.localeCompare(b.toSnapshot().name)
    );
  }

  mapEachDeck<T>(callback: (deck: Deck) => T) {
    return this.decks.map(callback);
  }
}
