export interface DeckProps {
  id?: string;
  gameId: string;
  playerId: string;
  name: string;
  tags: string[];
  cards: {cardId: string; quantity: number}[];
}

export class Deck {
  private props: DeckProps;
  constructor(props: DeckProps) {
    this.props = props;
  }

  static create(props: DeckProps) {
    return new Deck(props);
  }

  static fromSnapshot(snapshot: Required<DeckProps>): Deck {
    return new Deck({...snapshot});
  }

  toSnapshot(): Required<DeckProps> {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      playerId: this.props.playerId,
      name: this.props.name,
      tags: this.props.tags,
      cards: this.props.cards,
    };
  }

  isOwnedBy(playerId: string) {
    return this.props.playerId === playerId;
  }

  renameTo(name: string) {
    this.props.name = name;
  }

  useTags(tags: string[]) {
    this.props.tags = tags;
  }

  useCards(cards: {cardId: string; quantity: number}[]) {
    this.props.cards = cards;
  }

}
