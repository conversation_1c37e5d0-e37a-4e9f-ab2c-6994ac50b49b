export type DeckBuilderSettingsSnapshot = { gameId: string };

export class DeckBuilderSettings {
  private readonly props: DeckBuilderSettingsSnapshot;

  constructor(props: DeckBuilderSettingsSnapshot) {
    this.props = props;
  }

  static fromSnapshot(snapshot: DeckBuilderSettingsSnapshot): DeckBuilderSettings {
    return new DeckBuilderSettings({...snapshot});
  }

  toSnapshot(): DeckBuilderSettingsSnapshot {
    return {...this.props};
  }
}
