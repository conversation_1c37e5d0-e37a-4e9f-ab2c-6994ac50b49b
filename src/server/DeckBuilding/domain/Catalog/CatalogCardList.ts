import {CatalogCard} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCard";

export class CatalogCardList {
  private readonly cards: CatalogCard[];

  private constructor(cards: CatalogCard[]) {
    this.cards = cards;
  }

  static createFrom(cards: CatalogCard[]) {
    return new CatalogCardList(cards);
  }

  findCardById(id: string) {
    return this.cards.find(c => c.id === id);
  }

  toSnapshot() {
    return {
      cards: this.cards.map(c => ({
        id: c.id,
        name: c.name,
        image: c.image,
        minDeckQuantity: c.minDeckQuantity,
        maxDeckQuantity: c.maxDeckQuantity,
        data: c.data,
      }))
    }
  }
}
