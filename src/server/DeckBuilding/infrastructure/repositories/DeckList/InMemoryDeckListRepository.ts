import {DeckListRepository} from "@/src/server/DeckBuilding/application/ports/DeckListRepository";
import {DeckList} from "@/src/server/DeckBuilding/domain/Deck/DeckList";
import {Deck} from "@/src/server/DeckBuilding/domain/Deck/Deck";

export class InMemoryDeckListRepository implements DeckListRepository {
  private decks: Map<string, Deck[]> = new Map();

  async getByUserIdAndGameId(userId: string, gameId: string): Promise<DeckList> {
    const key = this.key(userId, gameId);
    const deckList = this.decks.get(key) || [];
    return DeckList.createFrom(deckList);
  }

  addDecksFor(userId: string, gameId: string, decks: Deck[]): void {
    this.decks.set(this.key(userId, gameId), decks);
  }

  clear(): void {
    this.decks.clear();
  }

  private key(userId: string, gameId: string): string {
    return `${userId}-${gameId}`;
  }
}
