import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";
import {CatalogCard} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCard";

export class InMemoryCatalogCardListRepository implements CatalogCardListRepository {
  private cards: Map<string, CatalogCard[]> = new Map();

  async getByGameId(gameId: string): Promise<CatalogCardList> {
    const cards = this.cards.get(gameId) || [];
    return CatalogCardList.createFrom(cards);
  }

  addCardsForGame(gameId: string, cards: CatalogCard[]): void {
    this.cards.set(gameId, cards);
  }

  clear(): void {
    this.cards.clear();
  }
}
