import {DeckBuilderSettingsRepository} from '@/src/server/DeckBuilding/application/ports/DeckBuilderSettingsRepository';
import {DeckBuilderSettings} from '@/src/server/DeckBuilding/domain/DeckBuilderSettings/DeckBuilderSettings';

export class InMemoryFailingDeckBuilderSettingsRepository implements DeckBuilderSettingsRepository {
  async getByGameId(gameId: string): Promise<DeckBuilderSettings | null> {
    throw new Error(gameId);
  }
}
