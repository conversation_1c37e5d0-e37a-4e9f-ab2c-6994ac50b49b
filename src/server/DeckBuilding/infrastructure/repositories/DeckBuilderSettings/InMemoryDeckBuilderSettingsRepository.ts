import {DeckBuilderSettingsRepository} from '@/src/server/DeckBuilding/application/ports/DeckBuilderSettingsRepository';
import {DeckBuilderSettings} from '@/src/server/DeckBuilding/domain/DeckBuilderSettings/DeckBuilderSettings';

export class InMemoryDeckBuilderSettingsRepository implements DeckBuilderSettingsRepository {
  private settings: Map<string, DeckBuilderSettings> = new Map();

  async getByGameId(gameId: string): Promise<DeckBuilderSettings | null> {
    return this.settings.get(gameId) || null;
  }

  addSettingsForGame(gameId: string, snapshot: {gameId: string}): void {
    this.settings.set(gameId, DeckBuilderSettings.fromSnapshot(snapshot));
  }

  clear(): void {
    this.settings.clear();
  }
}
