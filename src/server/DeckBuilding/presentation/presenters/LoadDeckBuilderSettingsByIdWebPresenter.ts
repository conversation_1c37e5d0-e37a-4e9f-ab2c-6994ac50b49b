import {DeckBuilderSettings} from '@/src/server/DeckBuilding/domain/DeckBuilderSettings/DeckBuilderSettings';
import {LoadDeckBuilderSettingsByIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDeckBuilderSettingsByIdPresenter';
import {LoadDeckBuilderSettingsByIdViewModel} from '@/src/server/DeckBuilding/presentation/viewModels/LoadDeckBuilderSettingsByIdViewModel';

export class LoadDeckBuilderSettingsByIdWebPresenter implements LoadDeckBuilderSettingsByIdPresenter {
  private viewModel: LoadDeckBuilderSettingsByIdViewModel = {error: null, data: null};

  display(deckBuilderSettings: DeckBuilderSettings | null): void {
    this.viewModel = {
      error: null,
      data: deckBuilderSettings ? deckBuilderSettings.toSnapshot() : null,
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadDeckBuilderSettingsByIdViewModel {
    return this.viewModel;
  }
}
