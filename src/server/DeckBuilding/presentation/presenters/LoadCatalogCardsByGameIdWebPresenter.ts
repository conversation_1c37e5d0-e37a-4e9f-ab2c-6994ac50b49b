import {
  LoadCatalogCardsByGameIdPresenter
} from "@/src/server/DeckBuilding/application/ports/LoadCatalogCardsByGameIdPresenter";
import {
  LoadCatalogCardsByGameIdViewModel
} from "@/src/server/DeckBuilding/presentation/viewModels/LoadCatalogCardsByGameIdViewModel";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";

export class LoadCatalogCardsByGameIdWebPresenter implements LoadCatalogCardsByGameIdPresenter {
  private viewModel: LoadCatalogCardsByGameIdViewModel = {error: null, data: {cards: []}};

  display(cardList: CatalogCardList): void {
    const snapshot = cardList.toSnapshot();
    this.viewModel = {
      error: null,
      data: {cards: snapshot.cards},
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadCatalogCardsByGameIdViewModel {
    return this.viewModel;
  }
}
