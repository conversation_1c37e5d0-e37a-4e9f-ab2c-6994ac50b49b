import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';
import {LoadDeckByIdPresenter} from '@/src/server/DeckBuilding/application/ports/LoadDeckByIdPresenter';
import {LoadDeckByIdViewModel} from '@/src/server/DeckBuilding/presentation/viewModels/LoadDeckByIdViewModel';

export class LoadDeckByIdWebPresenter implements LoadDeckByIdPresenter {
  private viewModel: LoadDeckByIdViewModel = { error: null, data: null };

  display(deck: Deck): void {
    this.viewModel = { error: null, data: deck.toSnapshot() };
  }

  displayError(error: Error): void {
    this.viewModel = { error: error.message, data: null };
  }

  getViewModel(): LoadDeckByIdViewModel {
    return this.viewModel;
  }
}
