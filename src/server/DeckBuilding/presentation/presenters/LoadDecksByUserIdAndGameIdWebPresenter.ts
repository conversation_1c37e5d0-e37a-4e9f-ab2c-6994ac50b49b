import {LoadDecksByUserIdAndGameIdPresenter} from "@/src/server/DeckBuilding/application/ports/LoadDecksByUserIdAndGameIdPresenter";
import {LoadDecksByUserIdAndGameIdResponse} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdResponse";
import {LoadDecksByUserIdAndGameIdViewModel} from "@/src/server/DeckBuilding/presentation/viewModels/LoadDecksByUserIdAndGameIdViewModel";

export class LoadDecksByUserIdAndGameIdWebPresenter implements LoadDecksByUserIdAndGameIdPresenter {
  private viewModel: LoadDecksByUserIdAndGameIdViewModel = { error: null, data: null };

  displayDeckPreviewList(response: LoadDecksByUserIdAndGameIdResponse): void {
    this.viewModel = { error: null, data: response };
  }

  displayError(error: Error): void {
    this.viewModel = { error: error.message, data: null };
  }

  getViewModel(): LoadDecksByUserIdAndGameIdViewModel {
    return this.viewModel;
  }
}
