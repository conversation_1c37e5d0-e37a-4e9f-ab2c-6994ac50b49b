import {MatchmakingQueueItem} from "@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem";
import {MatchmakingQueue} from "@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue";

export interface MatchmakingQueueRepository {
  findByGameId(gameId: string): Promise<MatchmakingQueue>;

  save(item: MatchmakingQueueItem): Promise<string>;

  remove(id: string): Promise<void>;
}
