import {UpdatePlayersStatusCommand} from "./UpdatePlayersStatusCommand";
import {AppUserRepository} from "@/src/server/Authentication/application/ports/AppUserRepository";
export class UpdatePlayersStatusCommandHandler {
  private readonly repository: AppUserRepository;

  constructor(repository: AppUserRepository) {
    this.repository = repository;
  }

  async handle({players, status}: UpdatePlayersStatusCommand) {
    for (const player of players) {
      const user = await this.repository.findById(player);
      if (user) {
        user.setStatus(status);
        await this.repository.save(user);
      }
    }
  }
}