import {EventBus} from "@/src/server/Shared/application/ports/EventBus";
import {CleanUpMatchMakingQueueCommand} from "./CleanUpMatchMakingQueueCommand";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {MatchmakingQueueRepository} from "@/src/server/MatchMaking/application/ports/MatchmakingQueueRepository";

export class CleanUpMatchMakingQueueCommandHandler {
  private readonly eventBus: EventBus;
  private readonly matchRepository: MatchRepository;
  private readonly queueRepository: MatchmakingQueueRepository;

  constructor(
    eventBus: EventBus,
    matchRepository: MatchRepository,
    queueRepository: MatchmakingQueueRepository
  ) {
    this.eventBus = eventBus
    this.matchRepository = matchRepository;
    this.queueRepository = queueRepository;
  }

  async handle({matchId, players}: CleanUpMatchMakingQueueCommand) {
    const match = await this.matchRepository.findById(matchId);
    if (!match) return;

    const gameId = match.getGameId();

    const queue = await this.queueRepository.findByGameId(gameId);
    const matchedItems = queue.findItemsForPlayers(players);

    for (const item of matchedItems) {
      await this.queueRepository.remove(item.getId());

      await this.eventBus.dispatchMatchmakingEvent(gameId, item.getId(), {
        type: "PlayerRemovedFromQueue",
        payload: {
          playerId: item.getPlayerId(),
          reason: "matched",
        },
      });
    }
  }
}