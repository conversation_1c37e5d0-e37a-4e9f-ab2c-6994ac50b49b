import {EventBus} from "@/src/server/Shared/application/ports/EventBus";
import {CancelMatchRegistrationCommand} from "./CancelMatchRegistrationCommand";
import {MatchmakingQueueRepository} from "@/src/server/MatchMaking/application/ports/MatchmakingQueueRepository";
export class CancelMatchRegistrationCommandHandler {
  private readonly eventBus: EventBus;
  private readonly repository: MatchmakingQueueRepository;

  constructor(eventBus: EventBus, repository: MatchmakingQueueRepository) {
    this.eventBus = eventBus;
    this.repository = repository;
  }

  async handle({gameId, userId}: CancelMatchRegistrationCommand) {
    const queue = await this.repository.findByGameId(gameId);
    const item = queue.findItemByPlayer(userId);
    if (!item) return;

    await this.repository.remove(item.getId());

    await this.eventBus.dispatchMatchmakingEvent(gameId, item.getId(), {
      type: "PlayerRemovedFromQueue",
      payload: {
        playerId: userId,
        reason: "cancelled",
      },
    });

    await this.eventBus.dispatchMatchmakingEvent(gameId, item.getId(), {
      type: "PlayerRegistrationCancelled",
      payload: {
        playerId: userId,
      },
    });
  }
}
