import { MatchmakingQueueItem } from './MatchmakingQueueItem';

export class MatchmakingQueue {
  private readonly items: MatchmakingQueueItem[];

  constructor(items: MatchmakingQueueItem[]) {
    this.items = items;
  }

  static fromItems(items: MatchmakingQueueItem[]) {
    return new MatchmakingQueue(items);
  }

  toItems() {
    return [...this.items];
  }

  hasPlayer(playerId: string): boolean {
    return this.items.some(item => item.belongsTo(playerId));
  }

  findItemByPlayer(playerId: string): MatchmakingQueueItem | undefined {
    return this.items.find(item => item.belongsTo(playerId));
  }

  findItemsForPlayers(players: string[]): MatchmakingQueueItem[] {
    return this.items.filter(item => players.includes(item.getPlayerId()));
  }

  findOpponentsOf(playerId: string): MatchmakingQueueItem[] {
    return this.items.filter(item => !item.belongsTo(playerId));
  }
}
