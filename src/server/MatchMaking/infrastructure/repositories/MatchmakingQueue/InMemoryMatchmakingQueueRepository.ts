import {MatchmakingQueueRepository} from "@/src/server/MatchMaking/application/ports/MatchmakingQueueRepository";
import {MatchmakingQueueItem, MatchmakingQueueItemProps} from "@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueueItem";
import {MatchmakingQueue} from "@/src/server/MatchMaking/domain/MatchmakingQueue/MatchmakingQueue";

export class InMemoryMatchmakingQueueRepository implements MatchmakingQueueRepository {
  private items = new Map<string, MatchmakingQueueItem>();
  private counter = 0;

  async findByGameId(gameId: string): Promise<MatchmakingQueue> {
    const items = Array.from(this.items.values()).filter(i => i.getGameId() === gameId);
    return MatchmakingQueue.fromItems(items);
  }

  async save(item: MatchmakingQueueItem) {
    const props = (item as unknown as {props: MatchmakingQueueItemProps}).props;
    props.id = `q${++this.counter}`;
    this.items.set(props.id, item);
    return props.id;
  }

  async remove(id: string): Promise<void> {
    this.items.delete(id);
  }
}
