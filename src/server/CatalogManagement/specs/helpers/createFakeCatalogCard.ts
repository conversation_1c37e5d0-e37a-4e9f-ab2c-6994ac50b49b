import {TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';

export async function createFakeCatalogCard(
  card: {gameId: Id<'games'>; name: string; image: string; minDeckQuantity: number; maxDeckQuantity: number; data: Record<string, unknown>},
  user: TestConvexForDataModel<DataModel>,
) {
  return user.run(ctx => ctx.db.insert('catalogCards', {
    gameId: card.gameId,
    name: card.name,
    image: card.image,
    language: 'en',
    minDeckQuantity: card.minDeckQuantity,
    maxDeckQuantity: card.maxDeckQuantity,
    data: card.data,
  }));
}
