import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {api} from '@/convex/_generated/api';
import {createAppUser} from '@/src/server/Authentication/specs/helpers/createAppUsers';
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY, SOPHIE_IDENTITY, SOPHIE_APP_USER} from '@/src/server/Authentication/specs/helpers/fakes/fakeUsers';
import {getAllFrom} from '@/src/server/Shared/specs/helpers/requests/getAllFrom';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';
import {createFakeCatalogCard} from '@/src/server/CatalogManagement/specs/helpers/createFakeCatalogCard';

describe('deleteCatalogCard', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;
  let gameId: Id<'games'>;
  let cardId: Id<'catalogCards'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    asSophie = test.withIdentity(SOPHIE_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createGame(asJohn, {name: 'Lorcana'}) as Id<'games'>;
    cardId = await createFakeCatalogCard({gameId, name: 'Card', image: 'img', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}}, asAdmin) as Id<'catalogCards'>;
  });

  describe('When the owner deletes a card', () => {
    it('should remove it from the catalog', async () => {
      // Act
      await deleteCard(asJohn, {cardId});

      // Assert
      const cards = await getAllFrom('catalogCards', asAdmin);
      expect(cards).toHaveLength(0);
    });
  });

  describe('When another user tries to delete the card', () => {
    it('should return an error', async () => {
      // Act
      const deleting = deleteCard(asSophie, {cardId});

      // Assert
      await expect(deleting).rejects.toThrow(new GameNotOwnedError());
      const cards = await getAllFrom('catalogCards', asAdmin);
      expect(cards).toHaveLength(1);
    });
  });

  function createGame(user: TestConvexForDataModel<DataModel>, {name}: {name: string}) {
    return user.mutation(api.mutations.createGame.endpoint, {name});
  }

  function deleteCard(user: TestConvexForDataModel<DataModel>, {cardId}: {cardId: Id<'catalogCards'>}) {
    return user.mutation(api.mutations.deleteCatalogCard.endpoint, {cardId});
  }
});
