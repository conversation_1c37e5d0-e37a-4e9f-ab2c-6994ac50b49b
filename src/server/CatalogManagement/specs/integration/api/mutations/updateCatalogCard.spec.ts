import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {api} from '@/convex/_generated/api';
import {createAppUser} from '@/src/server/Authentication/specs/helpers/createAppUsers';
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY, SOPHIE_IDENTITY, SOPHIE_APP_USER} from '@/src/server/Authentication/specs/helpers/fakes/fakeUsers';
import {getAllFrom} from '@/src/server/Shared/specs/helpers/requests/getAllFrom';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';
import {createFakeCatalogCard} from '@/src/server/CatalogManagement/specs/helpers/createFakeCatalogCard';

describe('updateCatalogCard', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;
  let gameId: Id<'games'>;
  let cardId: Id<'catalogCards'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    asSophie = test.withIdentity(SOPHIE_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createGame(asJohn, {name: 'Lorcana'}) as Id<'games'>;
    cardId = await createFakeCatalogCard({gameId, name: 'Card', image: 'img', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}}, asAdmin) as Id<'catalogCards'>;
  });

  describe('When the owner updates a card', () => {
    it('should store the new content', async () => {
      // Act
      await updateCard(asJohn, {cardId, name: 'Updated', image: 'img2', minDeckQuantity: 1, maxDeckQuantity: 5, data: {}});

      // Assert
      const cards = await getAllFrom('catalogCards', asAdmin);
      expect(cards[0].name).toBe('Updated');
      expect(cards[0].minDeckQuantity).toBe(1);
    });
  });

  describe('When another user tries to update the card', () => {
    it('should return an error', async () => {
      // Act
      const updating = updateCard(asSophie, {cardId, name: 'Hacked', image: 'img', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}});

      // Assert
      await expect(updating).rejects.toThrow(new GameNotOwnedError());
      const cards = await getAllFrom('catalogCards', asAdmin);
      expect(cards[0].name).toBe('Card');
    });
  });

  function createGame(user: TestConvexForDataModel<DataModel>, {name}: {name: string}) {
    return user.mutation(api.mutations.createGame.endpoint, {name});
  }

  function updateCard(user: TestConvexForDataModel<DataModel>, params: {cardId: Id<'catalogCards'>; name: string; image: string; minDeckQuantity: number; maxDeckQuantity: number; data: Record<string, unknown>}) {
    return user.mutation(api.mutations.updateCatalogCard.endpoint, params);
  }
});
