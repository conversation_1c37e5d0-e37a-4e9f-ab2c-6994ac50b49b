import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {api} from '@/convex/_generated/api';
import {createAppUser} from '@/src/server/Authentication/specs/helpers/createAppUsers';
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY, SOPHIE_IDENTITY, SOPHIE_APP_USER} from '@/src/server/Authentication/specs/helpers/fakes/fakeUsers';
import {getAllFrom} from '@/src/server/Shared/specs/helpers/requests/getAllFrom';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';

describe('createCatalogCard', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;
  let gameId: Id<'games'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    asSophie = test.withIdentity(SOPHIE_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createGame(asJohn, {name: 'Lorcana'}) as Id<'games'>;
  });

  describe('When the owner adds a card', () => {
    it('should store it in the catalog', async () => {
      // Act
      await createCard(asJohn, {gameId, name: 'Card', image: 'img', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}});

      // Assert
      const cards = await getAllFrom('catalogCards', asAdmin);
      expect(cards).toHaveLength(1);
      expect(cards[0].name).toBe('Card');
    });
  });

  describe('When another user tries to add a card', () => {
    it('should return an error', async () => {
      // Act
      const creating = createCard(asSophie, {gameId, name: 'Hacked', image: 'img', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}});

      // Assert
      await expect(creating).rejects.toThrow(new GameNotOwnedError());
      const cards = await getAllFrom('catalogCards', asAdmin);
      expect(cards).toHaveLength(0);
    });
  });

  function createGame(user: TestConvexForDataModel<DataModel>, {name}: {name: string}) {
    return user.mutation(api.mutations.createGame.endpoint, {name});
  }

  function createCard(user: TestConvexForDataModel<DataModel>, params: {gameId: Id<'games'>; name: string; image: string; minDeckQuantity: number; maxDeckQuantity: number; data: Record<string, unknown>}) {
    return user.mutation(api.mutations.createCatalogCard.endpoint, params);
  }
});
