import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {CatalogCardRepository} from '@/src/server/CatalogManagement/application/ports/CatalogCardRepository';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';
import {CreateCatalogCardCommand} from './CreateCatalogCardCommand';
import {CatalogCard} from '@/src/server/CatalogManagement/domain/CatalogCard/CatalogCard';

export class CreateCatalogCardCommandHandler {
  private readonly games: GameRepository;
  private readonly cards: CatalogCardRepository;

  constructor(games: GameRepository, cards: CatalogCardRepository) {
    this.games = games;
    this.cards = cards;
  }

  async handle(command: CreateCatalogCardCommand) {
    const game = await this.games.findById(command.gameId);
    if (!game) return;
    if (!game.isOwnedBy(command.userId)) {
      throw new GameNotOwnedError();
    }
    const card = CatalogCard.create({
      name: command.name,
      image: command.image,
      minDeckQuantity: command.minDeckQuantity,
      maxDeckQuantity: command.maxDeckQuantity,
      data: command.data,
    });
    return this.cards.save(command.gameId, card);
  }
}
