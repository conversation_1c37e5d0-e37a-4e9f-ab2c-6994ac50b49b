import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {CatalogCardRepository} from '@/src/server/CatalogManagement/application/ports/CatalogCardRepository';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';
import {UpdateCatalogCardCommand} from './UpdateCatalogCardCommand';
import {CatalogCard} from '@/src/server/CatalogManagement/domain/CatalogCard/CatalogCard';

export class UpdateCatalogCardCommandHandler {
  private readonly games: GameRepository;
  private readonly cards: CatalogCardRepository;

  constructor(games: GameRepository, cards: CatalogCardRepository) {
    this.games = games;
    this.cards = cards;
  }

  async handle(command: UpdateCatalogCardCommand) {
    const existing = await this.cards.findById(command.cardId);
    if (!existing) return;
    const game = await this.games.findById(existing.gameId);
    if (!game || !game.isOwnedBy(command.userId)) {
      throw new GameNotOwnedError();
    }
    const card = CatalogCard.create({
      id: command.cardId,
      name: command.name,
      image: command.image,
      minDeckQuantity: command.minDeckQuantity,
      maxDeckQuantity: command.maxDeckQuantity,
      data: command.data,
    });
    await this.cards.save(existing.gameId, card);
  }
}
