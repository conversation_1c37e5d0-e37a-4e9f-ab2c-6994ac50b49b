import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {CatalogCardRepository} from '@/src/server/CatalogManagement/application/ports/CatalogCardRepository';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';
import {DeleteCatalogCardCommand} from './DeleteCatalogCardCommand';

export class DeleteCatalogCardCommandHandler {
  private readonly games: GameRepository;
  private readonly cards: CatalogCardRepository;

  constructor(games: GameRepository, cards: CatalogCardRepository) {
    this.games = games;
    this.cards = cards;
  }

  async handle(command: DeleteCatalogCardCommand) {
    const existing = await this.cards.findById(command.cardId);
    if (!existing) return;
    const game = await this.games.findById(existing.gameId);
    if (!game || !game.isOwnedBy(command.userId)) {
      throw new GameNotOwnedError();
    }
    await this.cards.delete(command.cardId);
  }
}
