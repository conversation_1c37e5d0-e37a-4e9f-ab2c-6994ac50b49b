import {CardData} from '@/src/server/DeckBuilding/domain/CardData/CardData';

export type CatalogCardProps = {
  id?: string;
  name: string;
  image: string;
  minDeckQuantity: number;
  maxDeckQuantity: number;
  data: CardData;
};

export class CatalogCard {
  private props: CatalogCardProps;

  constructor(props: CatalogCardProps) {
    this.props = props;
  }

  static create(props: CatalogCardProps) {
    return new CatalogCard(props);
  }

  toSnapshot(): Required<CatalogCardProps> {
    return {
      id: this.props.id!,
      name: this.props.name,
      image: this.props.image,
      minDeckQuantity: this.props.minDeckQuantity,
      maxDeckQuantity: this.props.maxDeckQuantity,
      data: this.props.data,
    };
  }

  rename(name: string) {
    this.props.name = name;
  }

  setImage(image: string) {
    this.props.image = image;
  }

  setQuantities(min: number, max: number) {
    this.props.minDeckQuantity = min;
    this.props.maxDeckQuantity = max;
  }

  setData(data: CardData) {
    this.props.data = data;
  }
}
