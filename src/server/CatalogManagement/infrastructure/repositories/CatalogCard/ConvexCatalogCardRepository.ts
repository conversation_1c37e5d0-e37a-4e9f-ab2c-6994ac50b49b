import {GenericMutationCtx} from 'convex/server';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {CatalogCardRepository} from '@/src/server/CatalogManagement/application/ports/CatalogCardRepository';
import {CatalogCard} from '@/src/server/CatalogManagement/domain/CatalogCard/CatalogCard';

export class ConvexCatalogCardRepository implements CatalogCardRepository {
  private readonly ctx: GenericMutationCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel>) {
    this.ctx = ctx;
  }

  async save(gameId: string, card: CatalogCard): Promise<string> {
    const data = card.toSnapshot();
    if (data.id) {
      await this.ctx.db.patch(data.id as Id<'catalogCards'>, {
        name: data.name,
        image: data.image,
        minDeckQuantity: data.minDeckQuantity,
        maxDeckQuantity: data.maxDeckQuantity,
        data: data.data,
      });
      return data.id;
    }
    return await this.ctx.db.insert('catalogCards', {
      gameId: gameId as Id<'games'>,
      name: data.name,
      image: data.image,
      language: 'en',
      minDeckQuantity: data.minDeckQuantity,
      maxDeckQuantity: data.maxDeckQuantity,
      data: data.data,
    });
  }

  async findById(id: string): Promise<{gameId: string; card: CatalogCard} | null> {
    const doc = await this.ctx.db.get(id as Id<'catalogCards'>);
    return doc
      ? {
          gameId: doc.gameId,
          card: CatalogCard.create({
            id: doc._id,
            name: doc.name,
            image: doc.image,
            minDeckQuantity: doc.minDeckQuantity,
            maxDeckQuantity: doc.maxDeckQuantity,
            data: doc.data,
          }),
        }
      : null;
  }

  async delete(id: string): Promise<void> {
    await this.ctx.db.delete(id as Id<'catalogCards'>);
  }
}
