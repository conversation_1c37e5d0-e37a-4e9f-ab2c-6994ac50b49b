import {CurrentUserRepository} from '@/src/server/Authentication/application/ports/CurrentUserRepository';
import {CurrentUser} from '@/src/server/Authentication/domain/User/CurrentUser';

export class InMemoryCurrentUserRepository implements CurrentUserRepository {
  private user: CurrentUser | null = null;

  setCurrentUser(user: CurrentUser | null) {
    this.user = user;
  }

  async getCurrentUser(): Promise<CurrentUser | null> {
    return this.user;
  }
}
