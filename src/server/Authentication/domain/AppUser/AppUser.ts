import {Aggregate} from "@evyweb/simple-ddd-toolkit";
import {AppUserId} from "@/src/server/Authentication/domain/AppUser/valueObjects/AppUserId";

interface AppUserProperties {
  id: AppUserId;
  authId: string;
  name: string;
  image: string;
  email: string;
  status?: string;
}

export class AppUser extends Aggregate<AppUserProperties> {
  private constructor(props: AppUserProperties) {
    super(props);
  }

  static fromSnapshot(snapshot: { id: string; authId: string; name: string; image: string; email: string }): AppUser {
    return new AppUser({
      id: AppUserId.createFrom(snapshot.id),
      authId: snapshot.authId,
      name: snapshot.name,
      image: snapshot.image,
      email: snapshot.email,
      status: undefined,
    });
  }

  toSnapshot() {
    const data = this.toObject();
    return {
      id: data.id.get('value'),
      authId: data.authId,
      name: data.name,
      image: data.image,
      email: data.email,
      status: data.status,
    };
  }

  setStatus(status: string) {
    this.set('status', status);
  }
}
