import {GetCurrentUserPresenter} from '@/src/server/Authentication/application/ports/GetCurrentUserPresenter';
import {GetCurrentUserViewModel} from '@/src/server/Authentication/presentation/viewModels/GetCurrentUserViewModel';
import {CurrentUser} from '@/src/server/Authentication/domain/User/CurrentUser';

export class GetCurrentUserWebPresenter implements GetCurrentUserPresenter {
  private viewModel: GetCurrentUserViewModel = {error: null, data: null};

  display(user: CurrentUser | null): void {
    this.viewModel = {error: null, data: user};
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): GetCurrentUserViewModel {
    return this.viewModel;
  }
}
