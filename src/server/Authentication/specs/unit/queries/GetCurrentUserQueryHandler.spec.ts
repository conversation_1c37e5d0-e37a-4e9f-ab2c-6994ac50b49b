import {GetCurrentUserQueryHandler} from '@/src/server/Authentication/application/queries/GetCurrentUser/GetCurrentUserQueryHandler';
import {InMemoryCurrentUserRepository} from '@/src/server/Authentication/infrastructure/repositories/CurrentUser/InMemoryCurrentUserRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {GetCurrentUserPresenter} from '@/src/server/Authentication/application/ports/GetCurrentUserPresenter';
import {InMemoryFailingCurrentUserRepository} from '@/src/server/Authentication/infrastructure/repositories/CurrentUser/InMemoryFailingCurrentUserRepository';

describe('GetCurrentUserQueryHandler', () => {
  describe('When retrieving the user successfully', () => {
    let repository: InMemoryCurrentUserRepository;
    let presenter: <PERSON><PERSON><PERSON>roxy<GetCurrentUserPresenter>;
    let handler: GetCurrentUserQueryHandler;

    beforeEach(() => {
      repository = new InMemoryCurrentUserRepository();
      presenter = mock<GetCurrentUserPresenter>();
      handler = new GetCurrentUserQueryHandler(repository);
    });

    describe('When the user exists', () => {
      it('should display it', async () => {
        // Arrange
        repository.setCurrentUser({userId: 'u1', email: '<EMAIL>'});

        // Act
        await handler.handle(presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({userId: 'u1', email: '<EMAIL>'});
      });
    });

    describe('When the user does not exist', () => {
      it('should display null', async () => {
        // Arrange
        repository.setCurrentUser(null);

        // Act
        await handler.handle(presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(null);
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingCurrentUserRepository();
      const presenter = mock<GetCurrentUserPresenter>();
      const handler = new GetCurrentUserQueryHandler(repository);

      // Act
      await handler.handle(presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('load-error'));
    });
  });
});
