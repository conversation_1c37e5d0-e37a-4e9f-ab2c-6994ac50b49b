import type {<PERSON>Model, Doc} from "@/convex/_generated/dataModel";
import type {TestConvexForDataModel} from "convex-test";

export function getAppUser(client: TestConvexForDataModel<DataModel>, appUserId: string): Promise<Doc<'appUsers'> | null> {
  return client.run(ctx => ctx.db.query('appUsers')
    .withIndex('by_appUserId', (q) => q.eq('appUserId', appUserId))
    .first())!;
}

export async function getAppUserAttribute(client: TestConvexForDataModel<DataModel>, appUserId: string, attribute: keyof Doc<'appUsers'>) {
  const user = await client.run(ctx => ctx.db.query('appUsers')
    .withIndex('by_appUserId', (q) => q.eq('appUserId', appUserId))
    .first())!;

  return user![attribute];
}
