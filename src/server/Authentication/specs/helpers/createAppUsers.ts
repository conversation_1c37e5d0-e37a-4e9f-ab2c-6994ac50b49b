import {TestConvexForDataModel} from "convex-test";
import {DataModel} from "@/convex/_generated/dataModel";

export type UserDTO = {
  convexUserId: string;
  appUserId: string;
  name: string;
  avatar: string;
  email: string;
};

export async function createAppUser(currentUser: TestConvexForDataModel<DataModel>, user: UserDTO) {
  return await currentUser.run(async (ctx) => {
    await ctx.db.insert('appUsers', { ...user, createdAt: Date.now() });
  });
}

export async function createAppUsers(currentUser: TestConvexForDataModel<DataModel>, users: UserDTO[]) {
  return await currentUser.run(async (ctx) => {
    for (const user of users) {
      await ctx.db.insert('appUsers', { ...user, createdAt: Date.now() });
    }
  });
}