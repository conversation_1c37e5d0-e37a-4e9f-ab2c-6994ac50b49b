import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {Game} from '@/src/server/GameManagement/domain/Game/Game';
import {CreateGameCommand} from './CreateGameCommand';

export class CreateGameCommandHandler {
  private readonly repository: GameRepository;

  constructor(repository: GameRepository) {
    this.repository = repository;
  }

  handle(command: CreateGameCommand) {
    return this.repository.save(Game.create({ownerId: command.ownerId, name: command.name}));
  }
}
