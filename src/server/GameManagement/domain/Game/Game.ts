export interface GameProps {
  id?: string;
  ownerId: string;
  name: string;
}

export class Game {
  private props: GameProps;

  constructor(props: GameProps) {
    this.props = props;
  }

  static create(props: GameProps) {
    return new Game(props);
  }

  static fromSnapshot(snapshot: Required<GameProps>): Game {
    return new Game({...snapshot});
  }

  toSnapshot(): Required<GameProps> {
    return {
      id: this.props.id!,
      ownerId: this.props.ownerId,
      name: this.props.name,
    };
  }

  renameTo(name: string) {
    this.props.name = name;
  }

  isOwnedBy(userId: string) {
    return this.props.ownerId === userId;
  }
}
