import {GenericMutationCtx} from 'convex/server';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {GameRepository} from '@/src/server/GameManagement/application/ports/GameRepository';
import {Game} from '@/src/server/GameManagement/domain/Game/Game';

export class ConvexGameRepository implements GameRepository {
  private readonly ctx: GenericMutationCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel>) {
    this.ctx = ctx;
  }

  async save(game: Game) {
    const data = game.toSnapshot();
    if (data.id) {
      await this.ctx.db.patch(data.id as Id<'games'>, {name: data.name});
      return data.id;
    }
    return await this.ctx.db.insert('games', {name: data.name, ownerId: data.ownerId});
  }

  async findById(id: string): Promise<Game | null> {
    const doc = await this.ctx.db.get(id as Id<'games'>);
    return doc ? Game.fromSnapshot({id: doc._id, name: doc.name, ownerId: doc.ownerId}) : null;
  }

  async delete(id: string): Promise<void> {
    await this.ctx.db.delete(id as Id<'games'>);
  }
}
