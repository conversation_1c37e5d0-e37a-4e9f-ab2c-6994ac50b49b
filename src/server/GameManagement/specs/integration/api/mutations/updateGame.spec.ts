import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {api} from '@/convex/_generated/api';
import {createAppUser} from '@/src/server/Authentication/specs/helpers/createAppUsers';
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY, SOPHIE_IDENTITY, SOPHIE_APP_USER} from '@/src/server/Authentication/specs/helpers/fakes/fakeUsers';
import {getAllFrom} from '@/src/server/Shared/specs/helpers/requests/getAllFrom';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';

describe('updateGame', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;
  let gameId: Id<'games'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    asSophie = test.withIdentity(SOPHIE_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createGame(asJohn, {name: 'Lorcana'}) as Id<'games'>;
  });

  describe('When the owner updates the game', () => {
    it('should store the new name', async () => {
      // Act
      await updateGame(asJohn, {gameId, name: 'Updated'});

      // Assert
      const games = await getAllFrom('games', asAdmin);
      expect(games[0].name).toBe('Updated');
    });
  });

  describe('When another user tries to update the game', () => {
    it('should return an error', async () => {
      // Act
      const updating = updateGame(asSophie, {gameId, name: 'Hacked'});

      // Assert
      await expect(updating).rejects.toThrow(new GameNotOwnedError());
      const games = await getAllFrom('games', asAdmin);
      expect(games[0].name).toBe('Lorcana');
    });
  });

  function createGame(user: TestConvexForDataModel<DataModel>, {name}: {name: string}) {
    return user.mutation(api.mutations.createGame.endpoint, {name});
  }

  function updateGame(user: TestConvexForDataModel<DataModel>, {gameId, name}: {gameId: Id<'games'>; name: string}) {
    return user.mutation(api.mutations.updateGame.endpoint, {gameId, name});
  }
});
