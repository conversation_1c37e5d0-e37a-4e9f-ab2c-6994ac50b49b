import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {api} from '@/convex/_generated/api';
import {createAppUser} from '@/src/server/Authentication/specs/helpers/createAppUsers';
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY, SOPHIE_IDENTITY, SOPHIE_APP_USER} from '@/src/server/Authentication/specs/helpers/fakes/fakeUsers';
import {getAllFrom} from '@/src/server/Shared/specs/helpers/requests/getAllFrom';
import {GameNotOwnedError} from '@/src/server/GameManagement/domain/Game/errors/GameNotOwnedError';

describe('deleteGame', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;
  let gameId: Id<'games'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    asSophie = test.withIdentity(SOPHIE_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createGame(asJohn, {name: 'Lorcana'}) as Id<'games'>;
  });

  describe('When the owner deletes the game', () => {
    it('should remove it from storage', async () => {
      // Act
      await deleteGame(asJohn, {gameId});

      // Assert
      const games = await getAllFrom('games', asAdmin);
      expect(games).toHaveLength(0);
    });
  });

  describe('When another user tries to delete the game', () => {
    it('should return an error', async () => {
      // Act
      const deleting = deleteGame(asSophie, {gameId});

      // Assert
      await expect(deleting).rejects.toThrow(new GameNotOwnedError());
      const games = await getAllFrom('games', asAdmin);
      expect(games).toHaveLength(1);
    });
  });

  function createGame(user: TestConvexForDataModel<DataModel>, {name}: {name: string}) {
    return user.mutation(api.mutations.createGame.endpoint, {name});
  }

  function deleteGame(user: TestConvexForDataModel<DataModel>, {gameId}: {gameId: Id<'games'>}) {
    return user.mutation(api.mutations.deleteGame.endpoint, {gameId});
  }
});
