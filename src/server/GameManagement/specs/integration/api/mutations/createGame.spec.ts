import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel} from '@/convex/_generated/dataModel';
import {api} from '@/convex/_generated/api';
import {createAppUser} from '@/src/server/Authentication/specs/helpers/createAppUsers';
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY} from '@/src/server/Authentication/specs/helpers/fakes/fakeUsers';
import {getAllFrom} from '@/src/server/Shared/specs/helpers/requests/getAllFrom';

describe('createGame', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
  });

  describe('When creating a game', () => {
    it('should store the game with the owner', async () => {
      // Act
      await createGame(asJohn, {name: 'Lorcana'});

      // Assert
      const games = await getAllFrom('games', asAdmin);
      expect(games).toHaveLength(1);
      const game = games[0];
      expect(game.name).toBe('Lorcana');
      expect(game.ownerId).toBe(JOHN_APP_USER.appUserId);
    });
  });

  function createGame(user: TestConvexForDataModel<DataModel>, {name}: {name: string}) {
    return user.mutation(api.mutations.createGame.endpoint, {name});
  }
});
