import {LoadMatchDataPresenter} from "@/src/server/Gaming/application/ports/LoadMatchDataPresenter";
import {LoadMatchDataViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadMatchDataViewModel";

export class Load<PERSON>atchDataWebPresenter implements LoadMatchDataPresenter {
  private viewModel: LoadMatchDataViewModel = { error: null, data: null };

  display(myCardIds: string[], opponentCardCount: number): void {
    this.viewModel = {
      error: null,
      data: { myCardIds, opponentCardCount },
    };
  }

  displayError(error: Error): void {
    this.viewModel = { error: error.message, data: null };
  }

  getViewModel(): LoadMatchDataViewModel {
    return this.viewModel;
  }
}

