import {GameList} from "@/src/server/Gaming/domain/Game/GameList";
import {LoadGameListPresenter} from "@/src/server/Gaming/application/ports/LoadGameListPresenter";
import {LoadGameListViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadGameListViewModel";

export class LoadGameListWebPresenter implements LoadGameListPresenter {
  private viewModel: LoadGameListViewModel = {error: null, data: null};

  display(gameList: GameList): void {
    this.viewModel = {
      error: null,
      data: gameList.games.map(game => game.toSnapshot()),
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadGameListViewModel {
    return this.viewModel;
  }
}
