import {LoadMatchEventsPresenter} from "@/src/server/Gaming/application/ports/LoadMatchEventsPresenter";
import {MatchEventList} from "@/src/server/Gaming/domain/MatchEvent/MatchEventList";
import {
  LoadMatchEventsViewModel,
  MatchEventViewModel,
} from "@/src/server/Gaming/presentation/viewModels/LoadMatchEventsViewModel";

export class LoadMatchEventsWebPresenter implements LoadMatchEventsPresenter {
  private viewModel: LoadMatchEventsViewModel = {error: null, data: null};

  display(matchEvents: MatchEventList): void {
    const events: MatchEventViewModel[] = matchEvents.matchEvents.map(ev => ({
      id: ev.id,
      type: ev.type,
      payload: ev.payload,
      occurredAt: ev.occurredAt,
    }));

    this.viewModel = {
      error: null,
      data: {events},
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadMatchEventsViewModel {
    return this.viewModel;
  }
}
