import {Game} from "@/src/server/Gaming/domain/Game/Game";
import {LoadGameByIdPresenter} from "@/src/server/Gaming/application/ports/LoadGameByIdPresenter";
import {LoadGameByIdViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadGameByIdViewModel";

export class LoadGameByIdWebPresenter implements LoadGameByIdPresenter {
  private viewModel: LoadGameByIdViewModel = {error: null, data: null};

  display(game: Game): void {
    this.viewModel = {error: null, data: game.toSnapshot()};
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadGameByIdViewModel {
    return this.viewModel;
  }
}
