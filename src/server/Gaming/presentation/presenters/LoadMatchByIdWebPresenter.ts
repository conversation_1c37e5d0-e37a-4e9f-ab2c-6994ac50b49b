import {Match} from "@/src/server/Gaming/domain/Match/Match";
import {LoadMatchByIdPresenter} from "@/src/server/Gaming/application/ports/LoadMatchByIdPresenter";
import {LoadMatchByIdViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadMatchByIdViewModel";
import {Id} from "@/convex/_generated/dataModel";

export class LoadMatchByIdWebPresenter implements LoadMatchByIdPresenter {
  private viewModel: LoadMatchByIdViewModel = {error: null, data: null};

  display(match: Match, userId: string): void {
    const players = match.getPlayers();
    const currentPlayerPosition = players[0] === userId ? 'player1' : 'player2';
    
    this.viewModel = {
      error: null,
      data: {
        id: match.getId(),
        status: match.getStatus(),
        players: players,
        isWinner: match.getWinner() === userId,
        gameId: match.getGameId() as Id<'games'>,
        currentPlayerPosition,
      },
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadMatchByIdViewModel {
    return this.viewModel;
  }
}
