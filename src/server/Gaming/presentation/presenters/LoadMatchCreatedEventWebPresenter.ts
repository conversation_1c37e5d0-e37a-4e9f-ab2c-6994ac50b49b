import {LoadMatchCreatedEventPresenter} from "@/src/server/Gaming/application/ports/LoadMatchCreatedEventPresenter";
import {LoadMatchCreatedEventViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadMatchCreatedEventViewModel";
import {MatchCreatedEvent} from "@/src/server/Gaming/domain/Match/MatchCreatedEvent";

export class LoadMatchCreatedEventWebPresenter implements LoadMatchCreatedEventPresenter {
  private viewModel: LoadMatchCreatedEventViewModel = {error: null, data: null};

  display(event: MatchCreatedEvent): void {
    this.viewModel = {error: null, data: event};
  }

  notifyNoMatchFoundForUser(): void {
    this.viewModel = {error: null, data: null};
  }

  notifyMatchAlreadyFinished(): void {
    this.viewModel = {error: null, data: null};
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadMatchCreatedEventViewModel {
    return this.viewModel;
  }
}
