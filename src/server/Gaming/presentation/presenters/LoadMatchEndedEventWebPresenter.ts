import {MatchEndedEvent} from "@/src/server/Gaming/domain/MatchEvent/MatchEndedEvent";
import {LoadMatchEndedEventPresenter} from "@/src/server/Gaming/application/ports/LoadMatchEndedEventPresenter";
import {LoadMatchEndedEventViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadMatchEndedEventViewModel";

export class LoadMatchEndedEventWebPresenter implements LoadMatchEndedEventPresenter {
  private viewModel: LoadMatchEndedEventViewModel = {error: null, data: null};

  display(event: MatchEndedEvent | null): void {
    this.viewModel = {error: null, data: event};
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadMatchEndedEventViewModel {
    return this.viewModel;
  }
}
