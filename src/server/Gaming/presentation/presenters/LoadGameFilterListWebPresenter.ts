import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";
import {LoadGameFilterListPresenter} from "@/src/server/Gaming/application/ports/LoadGameFilterListPresenter";
import {
  FilterViewModel,
  LoadGameFilterListViewModel
} from "@/src/server/Gaming/presentation/viewModels/LoadGameFilterListViewModel";
import {GameFilter} from "@/src/server/Gaming/domain/GameFilter/GameFilter";

export class LoadGameFilterListWebPresenter implements LoadGameFilterListPresenter {
  private viewModel: LoadGameFilterListViewModel = {
    error: null,
    data: null,
  };

  display(gameFilterList: GameFilterList): void {
    const availableFilters = gameFilterList.gameFilters.map(
      filter => this.mapGameFilterToViewModel(filter)
    );

    const groupedFilters = Object.fromEntries(
      Object.entries(groupBy(availableFilters, filter => filter.dataProperty))
        .map(([key, filters]) => [key, {name: key, filters: filters || []}])
    );

    this.viewModel = {
      error: null,
      data: {
        groupedFilters,
        availableFilters,
      },
    };
  }

  displayError(error: Error): void {
    this.viewModel = {
      error: error.message,
      data: null,
    };
  }

  getViewModel(): LoadGameFilterListViewModel {
    return this.viewModel;
  }

  private mapGameFilterToViewModel(filter: GameFilter): FilterViewModel {
    const baseFilter = {
      id: filter.id,
      name: filter.name,
      text: filter.text,
      dataProperty: filter.dataProperty,
      order: filter.order,
    };

    switch (filter.dataType) {
      case 'string':
        return {...baseFilter, dataType: 'string' as const, value: filter.value as string};
      case 'number':
        return {...baseFilter, dataType: 'number' as const, value: filter.value as number};
      case 'boolean':
        return {...baseFilter, dataType: 'boolean' as const, value: filter.value as boolean};
      case 'string[]':
        return {...baseFilter, dataType: 'string[]' as const, value: filter.value as string};
      default:
        throw new Error(`Unknown dataType: ${filter.dataType}`);
    }
  }
}

function groupBy<T>(items: T[], key: (item: T) => string): Record<string, T[]> {
  return items.reduce((acc, item) => {
    const group = key(item);
    if (!acc[group]) acc[group] = [];
    acc[group].push(item);
    return acc;
  }, {} as Record<string, T[]>);
}

