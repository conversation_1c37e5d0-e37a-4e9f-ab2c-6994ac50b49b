import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';
import {LoadGameSettingsByGameIdPresenter} from '@/src/server/Gaming/application/ports/LoadGameSettingsByGameIdPresenter';
import {LoadGameSettingsByGameIdViewModel} from '@/src/server/Gaming/presentation/viewModels/LoadGameSettingsByGameIdViewModel';

export class LoadGameSettingsByGameIdWebPresenter implements LoadGameSettingsByGameIdPresenter {
  private viewModel: LoadGameSettingsByGameIdViewModel = {error: null, data: null};

  display(gameSettings: GameSettings | null): void {
    this.viewModel = {
      error: null,
      data: gameSettings ? gameSettings.toSnapshot() : null,
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadGameSettingsByGameIdViewModel {
    return this.viewModel;
  }
}
