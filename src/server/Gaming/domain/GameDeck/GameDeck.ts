import {GameCard, GameCardSnapshot} from '@/src/server/Gaming/domain/GameCard/GameCard';

export interface GameDeckProps {
  id?: string;
  gameId: string;
  playerId: string;
  cards: GameCard[];
}

export type GameDeckSnapshot = {
  id: string;
  gameId: string;
  playerId: string;
  cards: GameCardSnapshot[];
};

export class GameDeck {
  private readonly props: GameDeckProps;

  constructor(props: GameDeckProps) {
    this.props = props;
  }

  static create(props: GameDeckProps): GameDeck {
    return new GameDeck(props);
  }

  static fromSnapshot(snapshot: GameDeckSnapshot): GameDeck {
    return new GameDeck({
      id: snapshot.id,
      gameId: snapshot.gameId,
      playerId: snapshot.playerId,
      cards: snapshot.cards.map(GameCard.fromSnapshot),
    });
  }

  toSnapshot(): GameDeckSnapshot {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      playerId: this.props.playerId,
      cards: this.props.cards.map((c) => c.toSnapshot()),
    };
  }
}
