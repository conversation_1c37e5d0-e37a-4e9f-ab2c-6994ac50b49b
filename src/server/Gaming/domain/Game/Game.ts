export type GameSnapshot = {
  id: string;
  name: string;
  description?: string;
  publisher?: string;
  playerCount?: string;
  imageUrl?: string;
  bannerUrl?: string;
  playersOnline?: number;
  totalMatches?: number;
  screenshots?: string[];
  videos?: string[];
  reviews?: GameReview[];
};

export type GameReview = {
  id: string;
  playerName: string;
  rating: number;
  comment: string;
  createdAt: string;
};

export class Game {
  private readonly props: GameSnapshot;

  constructor(props: GameSnapshot) {
    this.props = props;
  }

  static fromSnapshot(snapshot: GameSnapshot): Game {
    return new Game({...snapshot});
  }

  toSnapshot(): GameSnapshot {
    return {...this.props};
  }
}
