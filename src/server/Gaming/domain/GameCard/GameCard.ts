export type GameCardSnapshot = {
  id: string;
  catalogCardId: string;
  data: import('@/src/server/DeckBuilding/domain/CardData/CardData').CardData;
};

export class GameCard {
  private readonly props: GameCardSnapshot;

  constructor(props: GameCardSnapshot) {
    this.props = props;
  }

  static fromSnapshot(snapshot: GameCardSnapshot): GameCard {
    return new GameCard({...snapshot});
  }

  toSnapshot(): GameCardSnapshot {
    return {...this.props};
  }
}
