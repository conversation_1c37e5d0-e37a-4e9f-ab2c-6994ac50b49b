interface PlacedCard {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: string;
}

interface BoardState {
  player1Board: {
    firstRow: (PlacedCard | null)[];
    secondRow: (PlacedCard | null)[];
  };
  player2Board: {
    firstRow: (PlacedCard | null)[];
    secondRow: (PlacedCard | null)[];
  };
}

export interface MatchProps {
  id?: string;
  gameId: string;
  players: string[];
  status: string;
  winner?: string;
  currentMulliganRound?: number;
  boardState?: BoardState;
  currentTurn?: string;
  gamePhase?: string;
}

export type MatchSnapshot = Omit<MatchProps, 'id'> & { id: string };

export class Match {
  private props: MatchProps;
  constructor(props: MatchProps) {
    this.props = props;
  }

  static create(props: MatchProps) {
    return new Match(props);
  }

  static fromSnapshot(snapshot: MatchSnapshot): Match {
    return new Match({...snapshot});
  }

  toSnapshot(): MatchSnapshot {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      players: this.props.players,
      status: this.props.status,
      winner: this.props.winner,
      currentMulliganRound: this.props.currentMulliganRound,
      boardState: this.props.boardState,
      currentTurn: this.props.currentTurn,
      gamePhase: this.props.gamePhase,
    };
  }

  getId() { return this.props.id!; }
  getGameId() { return this.props.gameId; }
  getPlayers() { return [...this.props.players]; }
  getStatus() { return this.props.status; }
  getWinner() { return this.props.winner; }
  getCurrentMulliganRound() { return this.props.currentMulliganRound || 1; }

  isFinished() { return this.props.status === 'finished'; }

  finishMatchWithWinner(winner: string) {
    this.props.winner = winner;
    this.props.status = 'finished';
  }

  getOpponentOf(playerId: string): string {
    return this.props.players.find(p => p !== playerId)!;
  }

  startMulliganPhase(): void {
    if (this.props.status !== 'setup') {
      throw new Error(`Cannot start mulligan from ${this.props.status} state`);
    }
    this.props.status = 'waiting_for_mulligan';
    this.props.currentMulliganRound = 1;
  }

  advanceToNextMulliganRound(): void {
    if (this.props.status !== 'waiting_for_mulligan') {
      throw new Error(`Cannot advance mulligan round from ${this.props.status} state`);
    }
    this.props.currentMulliganRound = (this.props.currentMulliganRound || 1) + 1;
  }

  completeMulliganPhase(): void {
    this.props.status = 'active';
  }

  makeReadyForPlay(): void {
    this.props.status = 'active';
  }

  isWaitingForMulligan(): boolean {
    return this.props.status === 'waiting_for_mulligan';
  }

  startDirectly(): void {
    if (this.props.status !== 'setup') {
      throw new Error(`Cannot start directly from ${this.props.status} state`);
    }
    this.props.status = 'active';
  }

  initializeBoardState(): void {
    if (!this.props.boardState) {
      this.props.boardState = {
        player1Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        },
        player2Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        }
      };
    }
  }

  placeCard(playerId: string, cardId: string, rowType: 'first' | 'second', slotIndex: number): void {
    this.initializeBoardState();
    
    const playerBoard = playerId === this.props.players[0] ? 'player1Board' : 'player2Board';
    const row = this.props.boardState![playerBoard][rowType === 'first' ? 'firstRow' : 'secondRow'];
    
    if (slotIndex < 0 || slotIndex >= row.length) {
      throw new Error(`Invalid slot index: ${slotIndex}`);
    }
    
    if (row[slotIndex] !== null) {
      throw new Error(`Slot ${slotIndex} is already occupied`);
    }
    
    row[slotIndex] = {
      cardId,
      placedAt: new Date().toISOString(),
      slotIndex,
      rowType
    };
  }

  getBoardState(): BoardState | undefined {
    return this.props.boardState;
  }

  getCurrentTurn(): string | undefined {
    return this.props.currentTurn;
  }

  setCurrentTurn(playerId: string): void {
    this.props.currentTurn = playerId;
  }

  getGamePhase(): string | undefined {
    return this.props.gamePhase;
  }

  setGamePhase(phase: string): void {
    this.props.gamePhase = phase;
  }
}
