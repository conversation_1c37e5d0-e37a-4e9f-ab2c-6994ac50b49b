export interface MulliganSelectionProps {
  id?: string;
  playerId: string;
  matchId: string;
  selectedCardIds: string[];
  skipped: boolean;
  round: number;
}

export type MulliganSelectionSnapshot = Omit<MulliganSelectionProps, 'id'> & {
  id: string;
};

export class MulliganSelection {
  private readonly props: MulliganSelectionProps;

  constructor(props: MulliganSelectionProps) {
    this.props = props;
  }

  static create(props: Omit<MulliganSelectionProps, 'id'>): MulliganSelection {
    if (props.selectedCardIds.length > 7) {
      throw new Error('Cannot select more than 7 cards for mulligan');
    }
    return new MulliganSelection(props);
  }

  static createSkipped(playerId: string, matchId: string, round: number): MulliganSelection {
    return new MulliganSelection({
      playerId,
      matchId,
      selectedCardIds: [],
      skipped: true,
      round
    });
  }

  static fromSnapshot(snapshot: MulliganSelectionSnapshot): MulliganSelection {
    return new MulliganSelection({...snapshot});
  }

  toSnapshot(): MulliganSelectionSnapshot {
    return {
      id: this.props.id!,
      playerId: this.props.playerId,
      matchId: this.props.matchId,
      selectedCardIds: [...this.props.selectedCardIds],
      skipped: this.props.skipped,
      round: this.props.round
    };
  }

  isComplete(): boolean {
    return true;
  }

  isSkipped(): boolean {
    return this.props.skipped;
  }

  getSelectedCardIds(): string[] {
    return [...this.props.selectedCardIds];
  }

  getRound(): number {
    return this.props.round;
  }
}