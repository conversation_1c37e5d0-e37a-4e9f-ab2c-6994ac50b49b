import {MatchmakingEvent} from "@/convex/dispatchers/matchmaking";
import {MatchCreatedEvent} from "@/src/server/Gaming/domain/Match/MatchCreatedEvent";

export class MatchMakingEventList {
  readonly events: MatchmakingEvent[];

  private constructor(events: MatchmakingEvent[]) {
    this.events = events;
  }

  static createFrom(events: MatchmakingEvent[]) {
    return new MatchMakingEventList(events);
  }

  findMatchCreatedForUser(userId: string): MatchCreatedEvent | undefined {
    const ev = this.events.find(event =>
      event.type === 'MatchCreated' && event.payload.players.includes(userId)
    );
    return ev as MatchCreatedEvent | undefined;
  }
}
