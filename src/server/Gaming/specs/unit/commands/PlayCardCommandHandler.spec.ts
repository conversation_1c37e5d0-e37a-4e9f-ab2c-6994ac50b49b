import {PlayCardCommandHandler} from '@/src/server/Gaming/application/commands/Match/PlayCard/PlayCardCommandHandler';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';

describe('PlayCardCommandHandler', () => {
  let matchRepository: InMemoryMatchRepository;
  let handler: PlayCardCommandHandler;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    handler = new PlayCardCommandHandler(matchRepository);
  });

  describe('When playing a card in active match', () => {
    it('should place card on board and update match state', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId,
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      expect(updatedMatch).toBeDefined();

      const boardState = updatedMatch!.getBoardState();
      expect(boardState).toBeDefined();
      expect(boardState!.player1Board.firstRow[2]).toEqual({
        cardId: 'card-789',
        placedAt: expect.any(String),
        slotIndex: 2,
        rowType: 'first'
      });
    });
  });

  describe('When playing card in non-active match', () => {
    it('should throw error for invalid match state', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'finished'
      });
      const matchId = await matchRepository.save(match);

      // Act

      // Assert
      await expect(handler.handle({
        matchId,
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      })).rejects.toThrow();
    });
  });

  describe('When player2 plays a card', () => {
    it('should place card on player2 board successfully', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['user1', 'user2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId,
        cardId: 'card-player2',
        rowType: 'first',
        slotIndex: 0,
        userId: 'user2'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      expect(updatedMatch).toBeDefined();

      const boardState = updatedMatch!.getBoardState();
      expect(boardState).toBeDefined();
      expect(boardState!.player2Board.firstRow[0]).toEqual({
        cardId: 'card-player2',
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: 'first'
      });
    });

    it('should allow both players to place cards in different slots', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['user1', 'user2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      // First, player1 places a card
      await handler.handle({
        matchId,
        cardId: 'card-player1',
        rowType: 'first',
        slotIndex: 0,
        userId: 'user1'
      });

      // Act - player2 places a card in a different slot
      await handler.handle({
        matchId,
        cardId: 'card-player2',
        rowType: 'first',
        slotIndex: 1,
        userId: 'user2'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      const boardState = updatedMatch!.getBoardState();

      // Player1's card should be on player1Board
      expect(boardState!.player1Board.firstRow[0]).toEqual({
        cardId: 'card-player1',
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: 'first'
      });

      // Player2's card should be on player2Board
      expect(boardState!.player2Board.firstRow[1]).toEqual({
        cardId: 'card-player2',
        placedAt: expect.any(String),
        slotIndex: 1,
        rowType: 'first'
      });
    });

    it('should reject player2 placing card in same slot as player1', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['user1', 'user2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      // First, player1 places a card
      await handler.handle({
        matchId,
        cardId: 'card-player1',
        rowType: 'first',
        slotIndex: 0,
        userId: 'user1'
      });

      // player2 tries to place a card in the same slot on their own board (should work)
      await expect(handler.handle({
        matchId,
        cardId: 'card-player2',
        rowType: 'first',
        slotIndex: 0,
        userId: 'user2'
      })).resolves.not.toThrow();

      // Assert
      // Verify both players have cards in slot 0 of their respective boards
      const updatedMatch = await matchRepository.findById(matchId);
      const boardState = updatedMatch!.getBoardState();

      expect(boardState!.player1Board.firstRow[0]).toEqual({
        cardId: 'card-player1',
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: 'first'
      });

      expect(boardState!.player2Board.firstRow[0]).toEqual({
        cardId: 'card-player2',
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: 'first'
      });
    });
  });
});