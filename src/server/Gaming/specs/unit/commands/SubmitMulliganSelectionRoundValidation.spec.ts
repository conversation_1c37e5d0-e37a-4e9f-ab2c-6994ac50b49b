import {SubmitMulliganSelectionCommandHandler} from '@/src/server/Gaming/application/commands/Mulligan/SubmitMulliganSelection/SubmitMulliganSelectionCommandHandler';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';
import {InMemoryGameDeckRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckRepository';
import {InMemoryGameDeckReadRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckReadRepository';
import {InMemoryMulliganSelectionRepository} from '@/src/server/Gaming/infrastructure/repositories/MulliganSelection/InMemoryMulliganSelectionRepository';
import {InMemoryGameSettingsRepository} from '@/src/server/Gaming/infrastructure/repositories/GameSettings/InMemoryGameSettingsRepository';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';

describe('SubmitMulliganSelectionCommandHandler - Round Validation', () => {
  let handler: SubmitMulliganSelectionCommandHandler;
  let matchRepository: InMemoryMatchRepository;
  let deckRepository: InMemoryGameDeckRepository;
  let deckReadRepository: InMemoryGameDeckReadRepository;
  let mulliganRepository: InMemoryMulliganSelectionRepository;
  let gameSettingsRepository: InMemoryGameSettingsRepository;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    deckRepository = new InMemoryGameDeckRepository();
    deckReadRepository = new InMemoryGameDeckReadRepository();
    mulliganRepository = new InMemoryMulliganSelectionRepository();
    gameSettingsRepository = new InMemoryGameSettingsRepository();

    handler = new SubmitMulliganSelectionCommandHandler(
      matchRepository,
      deckRepository,
      deckReadRepository,
      mulliganRepository,
      gameSettingsRepository
    );
  });

  describe('When validating rounds per player', () => {
    it('should allow player to submit round 2 after completing round 1', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan',
        currentMulliganRound: 1
      });
      await matchRepository.save(match);

      gameSettingsRepository.addSettingsForGame('g1', {
        maxCardsInDeck: 30,
        mulliganCount: 2
      });

      const cards = [
        GameCard.fromSnapshot({id: 'c1', catalogCardId: 'card1', data: {}}),
        GameCard.fromSnapshot({id: 'c2', catalogCardId: 'card2', data: {}}),
        GameCard.fromSnapshot({id: 'c3', catalogCardId: 'card3', data: {}})
      ];
      const deck = GameDeck.create({
        id: 'd1',
        gameId: 'g1',
        playerId: 'player1',
        cards
      });
      await deckRepository.save(deck);
      deckReadRepository.addDeck(deck.toSnapshot());

      const round1Selection = MulliganSelection.create({
        playerId: 'player1',
        matchId: 'm1',
        selectedCardIds: ['c1'],
        skipped: false,
        round: 1
      });
      await mulliganRepository.save(round1Selection);

      // Act
      const mulligan = handler.handle({
        playerId: 'player1',
        matchId: 'm1',
        selectedCardIds: ['card2'],
        skipped: false,
        round: 2
      });

      // Assert
      await expect(mulligan).resolves.not.toThrow();
    });

    it('should reject round 3 when max rounds is 2', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan',
        currentMulliganRound: 1
      });
      await matchRepository.save(match);

      gameSettingsRepository.addSettingsForGame('g1', {
        maxCardsInDeck: 30,
        mulliganCount: 2
      });

      const selections = [
        MulliganSelection.create({
          playerId: 'player1',
          matchId: 'm1',
          selectedCardIds: ['card1'],
          skipped: false,
          round: 1
        }),

        MulliganSelection.create({
          playerId: 'player1',
          matchId: 'm1',
          selectedCardIds: ['card2'],
          skipped: false,
          round: 2
        })
      ];

      for (const selection of selections) {
        await mulliganRepository.save(selection);
      }

      // Act
      const mulligan = handler.handle({
        playerId: 'player1',
        matchId: 'm1',
        selectedCardIds: ['card3'],
        skipped: false,
        round: 3
      });

      // Assert
      await expect(mulligan).rejects.toThrow('Player has already completed all 2 mulligan rounds');
    });

    it('should reject round 2 when player has not completed round 1', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan',
        currentMulliganRound: 1
      });
      await matchRepository.save(match);

      gameSettingsRepository.addSettingsForGame('g1', {
        maxCardsInDeck: 30,
        mulliganCount: 2
      });

      // Act
      const mulligan = handler.handle({
        playerId: 'player1',
        matchId: 'm1',
        selectedCardIds: ['card2'],
        skipped: false,
        round: 2
      });

      // Assert
      await expect(mulligan).rejects.toThrow('Invalid mulligan round. Expected 1, received 2');
    });
  });
});