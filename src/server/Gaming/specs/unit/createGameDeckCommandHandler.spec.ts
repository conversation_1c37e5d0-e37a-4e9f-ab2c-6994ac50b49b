import {CreateGameDeckCommandHandler} from '@/src/server/Gaming/application/commands/GameDeck/CreateGameDeck/CreateGameDeckCommandHandler';
import {InMemoryDeckRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/Deck/InMemoryDeckRepository';
import {InMemoryCatalogCardListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/InMemoryCatalogCardListRepository';
import {InMemoryGameDeckRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckRepository';
import {Deck} from '@/src/server/DeckBuilding/domain/Deck/Deck';
import {CatalogCard} from '@/src/server/DeckBuilding/domain/Catalog/CatalogCard';

let deckRepository: InMemoryDeckRepository;
let catalogRepository: InMemoryCatalogCardListRepository;
let gameDeckRepository: InMemoryGameDeckRepository;
let handler: CreateGameDeckCommandHandler;

beforeEach(() => {
  deckRepository = new InMemoryDeckRepository();
  catalogRepository = new InMemoryCatalogCardListRepository();
  gameDeckRepository = new InMemoryGameDeckRepository();
  handler = new CreateGameDeckCommandHandler(
    deckRepository,
    catalogRepository,
    gameDeckRepository,
  );
});

describe('CreateGameDeckCommandHandler', () => {
  describe('When creating a game deck from an existing deck', () => {
    it('should persist a deck with card instances', async () => {
      // Arrange
      const catalogCard: CatalogCard = {
        id: 'cc1',
        name: 'A',
        image: 'img',
        minDeckQuantity: 1,
        maxDeckQuantity: 4,
        data: {attack: 1},
      };
      catalogRepository.addCardsForGame('g1', [catalogCard]);
      const deck = Deck.create({
        gameId: 'g1',
        playerId: 'p1',
        name: 'Deck',
        tags: [],
        cards: [{cardId: 'cc1', quantity: 2}],
      });
      const deckId = await deckRepository.save(deck);

      // Act
      const gameDeckId = await handler.handle({deckId});

      // Assert
      const saved = (
        gameDeckRepository as unknown as {
          items: Map<string, import('@/src/server/Gaming/domain/GameDeck/GameDeck').GameDeck>;
        }
      ).items.get(gameDeckId)!;
      const snapshot = saved.toSnapshot();
      expect(snapshot.id).toBe(gameDeckId);
      expect(snapshot.gameId).toBe('g1');
      expect(snapshot.playerId).toBe('p1');
      expect(snapshot.cards).toHaveLength(2);
      expect(snapshot.cards).toEqual(
        expect.arrayContaining([
          expect.objectContaining({catalogCardId: 'cc1', data: {attack: 1}}),
          expect.objectContaining({catalogCardId: 'cc1', data: {attack: 1}}),
        ])
      );
      expect(snapshot.cards[0].id).toMatch(/^gc\d+$/);
      expect(snapshot.cards[1].id).toMatch(/^gc\d+$/);
      expect(snapshot.cards[0].id).not.toBe(snapshot.cards[1].id);
    });

    it('should shuffle the deck cards for security', async () => {
      // Arrange
      const catalogCards: CatalogCard[] = [
        {id: 'cc1', name: 'Card1', image: 'img1', minDeckQuantity: 1, maxDeckQuantity: 4, data: {type: 'A'}},
        {id: 'cc2', name: 'Card2', image: 'img2', minDeckQuantity: 1, maxDeckQuantity: 4, data: {type: 'B'}},
        {id: 'cc3', name: 'Card3', image: 'img3', minDeckQuantity: 1, maxDeckQuantity: 4, data: {type: 'C'}},
      ];
      catalogRepository.addCardsForGame('g1', catalogCards);
      const deck = Deck.create({
        gameId: 'g1',
        playerId: 'p1',
        name: 'Test Deck',
        tags: [],
        cards: [
          {cardId: 'cc1', quantity: 3},
          {cardId: 'cc2', quantity: 3},
          {cardId: 'cc3', quantity: 3},
        ],
      });
      const deckId = await deckRepository.save(deck);

      // Act
      const results = [];
      for (let i = 0; i < 5; i++) {
        const gameDeckId = await handler.handle({deckId});
        const saved = (
          gameDeckRepository as unknown as {
            items: Map<string, import('@/src/server/Gaming/domain/GameDeck/GameDeck').GameDeck>;
          }
        ).items.get(gameDeckId)!;
        results.push(saved.toSnapshot().cards.map(c => c.catalogCardId));
      }

      // Assert
      const originalOrder = ['cc1', 'cc1', 'cc1', 'cc2', 'cc2', 'cc2', 'cc3', 'cc3', 'cc3'];
      const hasShuffled = results.some(order => JSON.stringify(order) !== JSON.stringify(originalOrder));
      expect(hasShuffled).toBe(true);

      results.forEach(cardOrder => {
        expect(cardOrder).toHaveLength(9);
        expect(cardOrder.filter(id => id === 'cc1')).toHaveLength(3);
        expect(cardOrder.filter(id => id === 'cc2')).toHaveLength(3);
        expect(cardOrder.filter(id => id === 'cc3')).toHaveLength(3);
      });
    });
  });

  describe('When the deck does not exist', () => {
    it('should throw an error', async () => {
      // Act
      const creating = handler.handle({deckId: 'unknown'});

      // Assert
      await expect(creating).rejects.toThrow('Deck not found');
    });
  });
});
