import {LoadMatchEndedEventQueryHandler} from '@/src/server/Gaming/application/queries/LoadMatchEndedEvent/LoadMatchEndedEventQueryHandler';
import {InMemoryMatchEndedEventRepository} from '@/src/server/Gaming/infrastructure/repositories/MatchEndedEvent/InMemoryMatchEndedEventRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadMatchEndedEventPresenter} from '@/src/server/Gaming/application/ports/LoadMatchEndedEventPresenter';
import {InMemoryFailingMatchEndedEventRepository} from '@/src/server/Gaming/infrastructure/repositories/MatchEndedEvent/InMemoryFailingMatchEndedEventRepository';

describe('LoadMatchEndedEventQueryHandler', () => {
  describe('When retrieving the event successfully', () => {
    let repository: InMemoryMatchEndedEventRepository;
    let presenter: Mock<PERSON>roxy<LoadMatchEndedEventPresenter>;
    let handler: LoadMatchEndedEventQueryHandler;

    beforeEach(() => {
      repository = new InMemoryMatchEndedEventRepository();
      presenter = mock<LoadMatchEndedEventPresenter>();
      handler = new LoadMatchEndedEventQueryHandler(repository);
    });

    describe('When the event exists', () => {
      it('should display it', async () => {
        // Arrange
        const matchId = 'm1';
        const event = {winner: 'u2', loser: 'u1', matchId};
        repository.addEvent(matchId, event);

        // Act
        await handler.handle({matchId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(event);
      });
    });

    describe('When the event does not exist', () => {
      it('should display null', async () => {
        // Arrange
        const matchId = 'm1';

        // Act
        await handler.handle({matchId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(null);
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingMatchEndedEventRepository();
      const presenter = mock<LoadMatchEndedEventPresenter>();
      const handler = new LoadMatchEndedEventQueryHandler(repository);

      // Act
      await handler.handle({matchId: 'm1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('m1'));
    });
  });
});
