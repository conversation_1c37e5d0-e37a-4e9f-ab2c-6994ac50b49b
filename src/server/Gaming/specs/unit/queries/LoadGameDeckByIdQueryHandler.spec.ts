import {LoadGameDeckByIdQueryHandler} from '@/src/server/Gaming/application/queries/LoadGameDeckById/LoadGameDeckByIdQueryHandler';
import {InMemoryGameDeckRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadGameDeckByIdPresenter} from '@/src/server/Gaming/application/ports/LoadGameDeckByIdPresenter';
import {InMemoryFailingGameDeckRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryFailingGameDeckRepository';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';

describe('LoadGameDeckByIdQueryHandler', () => {
  describe('When retrieving the game deck successfully', () => {
    let repository: InMemoryGameDeckRepository;
    let presenter: <PERSON><PERSON><PERSON>ro<PERSON><LoadGameDeckByIdPresenter>;
    let handler: LoadGameDeckByIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryGameDeckRepository();
      presenter = mock<LoadGameDeckByIdPresenter>();
      handler = new LoadGameDeckByIdQueryHandler(repository);
    });

    describe('When the game deck exists', () => {
      it('should display it', async () => {
        // Arrange
        const card = GameCard.fromSnapshot({id: 'c1', catalogCardId: 'cc1', data: {}});
        const deck = GameDeck.create({gameId: 'g1', playerId: 'p1', cards: [card]});
        const deckId = await repository.save(deck);

        // Act
        await handler.handle({deckId}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(deck);
      });
    });

    describe('When the game deck does not exist', () => {
      it('should display a not found error', async () => {
        // Arrange

        // Act
        await handler.handle({deckId: 'unknown'}, presenter);

        // Assert
        expect(presenter.displayError).toHaveBeenCalledWith(new Error('Deck not found'));
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingGameDeckRepository();
      const presenter = mock<LoadGameDeckByIdPresenter>();
      const handler = new LoadGameDeckByIdQueryHandler(repository);

      // Act
      await handler.handle({deckId: 'd1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('d1'));
    });
  });
});
