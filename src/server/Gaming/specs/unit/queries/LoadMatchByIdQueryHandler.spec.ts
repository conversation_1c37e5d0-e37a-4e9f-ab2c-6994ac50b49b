import {
  LoadMatchByIdQueryHandler
} from '@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQueryHandler';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadMatchByIdPresenter} from '@/src/server/Gaming/application/ports/LoadMatchByIdPresenter';
import {
  InMemoryFailingMatchRepository
} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryFailingMatchRepository';
import {Match} from '@/src/server/Gaming/domain/Match/Match';

describe('LoadMatchByIdQueryHandler', () => {
  describe('When retrieving the match successfully', () => {
    let repository: InMemoryMatchRepository;
    let presenter: <PERSON><PERSON><PERSON>roxy<LoadMatchByIdPresenter>;
    let handler: LoadMatchByIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryMatchRepository();
      presenter = mock<LoadMatchByIdPresenter>();
      handler = new LoadMatchByIdQueryHandler(repository);
    });

    describe('When the match exists', () => {
      it('should display it', async () => {
        // Arrange
        const match = Match.create({gameId: 'g1', players: ['u1', 'u2'], status: 'setup'});
        const matchId = await repository.save(match);

        // Act
        await handler.handle({matchId, userId: 'u1'}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(match, 'u1');
      });
    });

    describe('When the match does not exist', () => {
      it('should display a not found error', async () => {
        // Act
        await handler.handle({matchId: 'unknown', userId: 'u1'}, presenter);

        // Assert
        expect(presenter.displayError).toHaveBeenCalledWith(new Error('Match not found'));
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingMatchRepository();
      const presenter = mock<LoadMatchByIdPresenter>();
      const handler = new LoadMatchByIdQueryHandler(repository);

      // Act
      await handler.handle({matchId: 'm1', userId: 'u1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('m1'));
    });
  });
});
