import {LoadMatchDataQueryHandler} from '@/src/server/Gaming/application/queries/LoadMatchData/LoadMatchDataQueryHandler';
import {MatchReadRepository} from '@/src/server/Gaming/application/ports/MatchReadRepository';
import {GameDeckReadRepository} from '@/src/server/Gaming/application/ports/GameDeckReadRepository';
import {CatalogCardListRepository} from '@/src/server/DeckBuilding/application/ports/CatalogCardListRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadMatchDataPresenter} from '@/src/server/Gaming/application/ports/LoadMatchDataPresenter';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';
import {CatalogCardList} from '@/src/server/DeckBuilding/domain/Catalog/CatalogCardList';
import {CatalogCard} from '@/src/server/DeckBuilding/domain/Catalog/CatalogCard';

describe('LoadMatchDataQueryHandler', () => {
  let matchRepository: MockProxy<MatchReadRepository>;
  let deckRepository: MockProxy<GameDeckReadRepository>;
  let catalogRepository: MockProxy<CatalogCardListRepository>;
  let presenter: MockProxy<LoadMatchDataPresenter>;
  let handler: LoadMatchDataQueryHandler;

  beforeEach(() => {
    matchRepository = mock<MatchReadRepository>();
    deckRepository = mock<GameDeckReadRepository>();
    catalogRepository = mock<CatalogCardListRepository>();
    presenter = mock<LoadMatchDataPresenter>();
    handler = new LoadMatchDataQueryHandler(matchRepository, deckRepository, catalogRepository);
  });

  describe('When loading match data successfully', () => {
    it('should only expose current player cards and opponent card count', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });
      matchRepository.findById.mockResolvedValue(match);

      const player1Card1 = GameCard.fromSnapshot({id: 'gc1', catalogCardId: 'cc1', data: {attack: 5}});
      const player1Card2 = GameCard.fromSnapshot({id: 'gc2', catalogCardId: 'cc2', data: {defense: 3}});
      const player1Deck = GameDeck.create({gameId: 'g1', playerId: 'player1', cards: [player1Card1, player1Card2]});

      const player2Card1 = GameCard.fromSnapshot({id: 'gc3', catalogCardId: 'cc3', data: {attack: 10}});
      const player2Card2 = GameCard.fromSnapshot({id: 'gc4', catalogCardId: 'cc4', data: {defense: 8}});
      const player2Card3 = GameCard.fromSnapshot({id: 'gc5', catalogCardId: 'cc5', data: {cost: 2}});
      const player2Deck = GameDeck.create({gameId: 'g1', playerId: 'player2', cards: [player2Card1, player2Card2, player2Card3]});

      deckRepository.findLatestByGameIdAndPlayerId.mockImplementation(async (gameId, playerId) => {
        if (playerId === 'player1') return player1Deck;
        if (playerId === 'player2') return player2Deck;
        return null;
      });

      const catalogCards: CatalogCard[] = [
        {id: 'cc1', name: 'Card 1', image: 'card1.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
        {id: 'cc2', name: 'Card 2', image: 'card2.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
        {id: 'cc3', name: 'Card 3', image: 'card3.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
        {id: 'cc4', name: 'Card 4', image: 'card4.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
        {id: 'cc5', name: 'Card 5', image: 'card5.jpg', minDeckQuantity: 1, maxDeckQuantity: 4, data: {}},
      ];
      const catalogCardList = CatalogCardList.createFrom(catalogCards);
      catalogRepository.getByGameId.mockResolvedValue(catalogCardList);

      // Act
      await handler.handle({matchId: 'm1', userId: 'player1'}, presenter);

      // Assert
      expect(presenter.display).toHaveBeenCalledWith(
        ['/game-assets/cards/en/thumbnail/card1.jpg', '/game-assets/cards/en/thumbnail/card2.jpg'],
        3
      );
      expect(presenter.displayError).not.toHaveBeenCalled();
    });

    it('should handle empty player decks securely', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });
      matchRepository.findById.mockResolvedValue(match);
      deckRepository.findLatestByGameIdAndPlayerId.mockResolvedValue(null);
      const catalogCardList = CatalogCardList.createFrom([]);
      catalogRepository.getByGameId.mockResolvedValue(catalogCardList);

      // Act
      await handler.handle({matchId: 'm1', userId: 'player1'}, presenter);

      // Assert
      expect(presenter.display).toHaveBeenCalledWith([], 0);
      expect(presenter.displayError).not.toHaveBeenCalled();
    });
  });

  describe('When match does not exist', () => {
    it('should display error without exposing any card data', async () => {
      // Arrange
      matchRepository.findById.mockResolvedValue(null);

      // Act
      await handler.handle({matchId: 'nonexistent', userId: 'player1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('Match not found'));
      expect(presenter.display).not.toHaveBeenCalled();
    });
  });

  describe('When repository throws error', () => {
    it('should display error without exposing any card data', async () => {
      // Arrange
      matchRepository.findById.mockRejectedValue(new Error('Database error'));

      // Act
      await handler.handle({matchId: 'm1', userId: 'player1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('Database error'));
      expect(presenter.display).not.toHaveBeenCalled();
    });
  });
});