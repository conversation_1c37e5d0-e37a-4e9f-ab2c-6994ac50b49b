import {LoadMulliganDataQueryHandler} from '@/src/server/Gaming/application/queries/LoadMulliganData/LoadMulliganDataQueryHandler';
import {InMemoryMatchReadRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchReadRepository';
import {InMemoryGameDeckReadRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckReadRepository';
import {InMemoryCatalogCardListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/InMemoryCatalogCardListRepository';
import {InMemoryGameSettingsRepository} from '@/src/server/Gaming/infrastructure/repositories/GameSettings/InMemoryGameSettingsRepository';
import {InMemoryMulliganSelectionRepository} from '@/src/server/Gaming/infrastructure/repositories/MulliganSelection/InMemoryMulliganSelectionRepository';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';
import type {LoadMulliganDataResult, LoadMulliganDataPresenter} from '@/src/server/Gaming/application/ports/LoadMulliganDataPresenter';
import type {CatalogCardListRepository} from '@/src/server/DeckBuilding/application/ports/CatalogCardListRepository';

describe('LoadMulliganDataQueryHandler', () => {
  let matchRepository: InMemoryMatchReadRepository;
  let deckRepository: InMemoryGameDeckReadRepository;
  let catalogRepository: InMemoryCatalogCardListRepository;
  let gameSettingsRepository: InMemoryGameSettingsRepository;
  let mulliganRepository: InMemoryMulliganSelectionRepository;
  let handler: LoadMulliganDataQueryHandler;
  let displayCalls: LoadMulliganDataResult[];
  let errorCalls: Error[];
  let presenter: LoadMulliganDataPresenter;

  beforeEach(() => {
    matchRepository = new InMemoryMatchReadRepository();
    deckRepository = new InMemoryGameDeckReadRepository();
    catalogRepository = new InMemoryCatalogCardListRepository();
    gameSettingsRepository = new InMemoryGameSettingsRepository();
    mulliganRepository = new InMemoryMulliganSelectionRepository();
    handler = new LoadMulliganDataQueryHandler(matchRepository, deckRepository, catalogRepository, gameSettingsRepository, mulliganRepository);
    
    displayCalls = [];
    errorCalls = [];
    presenter = {
      display: (data: LoadMulliganDataResult) => displayCalls.push(data),
      displayError: (error: Error) => errorCalls.push(error)
    };
  });

  describe('When loading mulligan data', () => {
    it('should display hand cards with URLs when match and deck exist', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'setup'
      });
      match.startMulliganPhase();
      await matchRepository.save(match);
      matchRepository.addMatch(match.toSnapshot());

      const cards = [
        GameCard.fromSnapshot({id: 'c1', catalogCardId: 'card1', data: {}}),
        GameCard.fromSnapshot({id: 'c2', catalogCardId: 'card2', data: {}}),
        GameCard.fromSnapshot({id: 'c3', catalogCardId: 'card3', data: {}})
      ];
      const deck = GameDeck.create({
        id: 'd1',
        gameId: 'g1',
        playerId: 'player1',
        cards
      });
      await deckRepository.save(deck);
      deckRepository.addDeck(deck.toSnapshot());

      const catalogCards = [
        {id: 'card1', name: 'Card 1', image: 'card1.jpg', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}},
        {id: 'card2', name: 'Card 2', image: 'card2.jpg', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}},
        {id: 'card3', name: 'Card 3', image: 'card3.jpg', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}}
      ];
      catalogRepository.addCardsForGame('g1', catalogCards);

      gameSettingsRepository.addSettingsForGame('g1', {
        maxCardsInDeck: 30,
        mulliganCount: 2
      });

      // Act
      await handler.handle({matchId: 'm1', userId: 'player1'}, presenter);

      // Assert
      expect(displayCalls).toHaveLength(1);
      expect(displayCalls[0]).toEqual({
        matchId: 'm1',
        handCardIds: ['c1', 'c2', 'c3'],
        handCardUrls: [
          '/game-assets/cards/en/thumbnail/card1.jpg',
          '/game-assets/cards/en/thumbnail/card2.jpg',
          '/game-assets/cards/en/thumbnail/card3.jpg'
        ],
        finalHandCardIds: ['c1', 'c2', 'c3'],
        finalHandCardUrls: [
          '/game-assets/cards/en/thumbnail/card1.jpg',
          '/game-assets/cards/en/thumbnail/card2.jpg',
          '/game-assets/cards/en/thumbnail/card3.jpg'
        ],
        currentRound: 1,
        maxRounds: 2,
        hasCompletedAllRounds: false,
        previousSelections: []
      });
    });

    it('should display error when match not found', async () => {
      // Arrange

      // Act
      await handler.handle({matchId: 'nonexistent', userId: 'player1'}, presenter);

      // Assert
      expect(errorCalls).toHaveLength(1);
      expect(errorCalls[0]).toEqual(new Error('Match not found'));
    });

    it('should display error when match not in mulligan phase', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });
      await matchRepository.save(match);
      matchRepository.addMatch(match.toSnapshot());

      // Act
      await handler.handle({matchId: 'm1', userId: 'player1'}, presenter);

      // Assert
      expect(errorCalls).toHaveLength(1);
      expect(errorCalls[0]).toEqual(new Error('Match is not in mulligan phase'));
    });

    it('should display error when player deck not found', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan'
      });
      await matchRepository.save(match);
      matchRepository.addMatch(match.toSnapshot());

      // Act
      await handler.handle({matchId: 'm1', userId: 'player1'}, presenter);

      // Assert
      expect(errorCalls).toHaveLength(1);
      expect(errorCalls[0]).toEqual(new Error('Player deck not found'));
    });

    it('should handle catalog repository errors', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan'
      });
      await matchRepository.save(match);
      matchRepository.addMatch(match.toSnapshot());

      const cards = [GameCard.fromSnapshot({id: 'c1', catalogCardId: 'card1', data: {}})];
      const deck = GameDeck.create({id: 'd1', gameId: 'g1', playerId: 'player1', cards});
      await deckRepository.save(deck);
      deckRepository.addDeck(deck.toSnapshot());

      const failingCatalogRepository: CatalogCardListRepository = {
        getByGameId: () => Promise.reject(new Error('Catalog error'))
      };
      const failingHandler = new LoadMulliganDataQueryHandler(matchRepository, deckRepository, failingCatalogRepository, gameSettingsRepository, mulliganRepository);

      // Act
      await failingHandler.handle({matchId: 'm1', userId: 'player1'}, presenter);

      // Assert
      expect(errorCalls).toHaveLength(1);
      expect(errorCalls[0]).toEqual(new Error('Catalog error'));
    });
  });
});