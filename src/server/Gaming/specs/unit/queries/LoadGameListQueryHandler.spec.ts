import {LoadGameListQueryHandler} from '@/src/server/Gaming/application/queries/LoadGameList/LoadGameListQueryHandler';
import {InMemoryGameListRepository} from '@/src/server/Gaming/infrastructure/repositories/GameList/InMemoryGameListRepository';
import {InMemoryFailingGameListRepository} from '@/src/server/Gaming/infrastructure/repositories/GameList/InMemoryFailingGameListRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadGameListPresenter} from '@/src/server/Gaming/application/ports/LoadGameListPresenter';
import {Game} from '@/src/server/Gaming/domain/Game/Game';

describe('LoadGameListQueryHandler', () => {
  describe('When retrieving games successfully', () => {
    let repository: InMemoryGameListRepository;
    let presenter: <PERSON><PERSON><PERSON>roxy<LoadGameListPresenter>;
    let handler: LoadGameListQueryHandler;

    beforeEach(() => {
      repository = new InMemoryGameListRepository();
      presenter = mock<LoadGameListPresenter>();
      handler = new LoadGameListQueryHandler(repository);
    });

    describe('When games exist', () => {
      it('should display them', async () => {
        // Arrange
        repository.addGame({id: 'g1', name: 'Game 1'});
        repository.addGame({id: 'g2', name: 'Game 2'});

        // Act
        await handler.handle({}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({
          games: [
            Game.fromSnapshot({id: 'g1', name: 'Game 1'}),
            Game.fromSnapshot({id: 'g2', name: 'Game 2'}),
          ],
        });
      });
    });

    describe('When no games exist', () => {
      it('should return an empty list', async () => {
        // Arrange

        // Act
        await handler.handle({}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith({games: []});
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingGameListRepository();
      const presenter = mock<LoadGameListPresenter>();
      const handler = new LoadGameListQueryHandler(repository);

      // Act
      await handler.handle({}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('load-error'));
    });
  });
});
