import {LoadGameByIdQueryHandler} from '@/src/server/Gaming/application/queries/LoadGameById/LoadGameByIdQueryHandler';
import {InMemoryGameRepository} from '@/src/server/Gaming/infrastructure/repositories/Game/InMemoryGameRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadGameByIdPresenter} from '@/src/server/Gaming/application/ports/LoadGameByIdPresenter';
import {InMemoryFailingGameRepository} from '@/src/server/Gaming/infrastructure/repositories/Game/InMemoryFailingGameRepository';
import {Game} from '@/src/server/Gaming/domain/Game/Game';

describe('LoadGameByIdQueryHandler', () => {
  describe('When retrieving the game successfully', () => {
    let repository: InMemoryGameRepository;
    let presenter: <PERSON><PERSON><PERSON>ro<PERSON><LoadGameByIdPresenter>;
    let handler: <PERSON>adGameByIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryGameRepository();
      presenter = mock<LoadGameByIdPresenter>();
      handler = new LoadGameByIdQueryHandler(repository);
    });

    describe('When the game exists', () => {
      it('should display it', async () => {
        // Arrange
        repository.addGame({id: 'g1', name: 'Lorcana'});

        // Act
        await handler.handle({gameId: 'g1'}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(Game.fromSnapshot({id: 'g1', name: 'Lorcana'}));
      });
    });

    describe('When the game does not exist', () => {
      it('should display a not found error', async () => {
        // Act
        await handler.handle({gameId: 'unknown'}, presenter);

        // Assert
        expect(presenter.displayError).toHaveBeenCalledWith(new Error('Game not found'));
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingGameRepository();
      const presenter = mock<LoadGameByIdPresenter>();
      const handler = new LoadGameByIdQueryHandler(repository);

      // Act
      await handler.handle({gameId: 'g1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('g1'));
    });
  });
});
