import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';

describe('MulliganSelection', () => {
  describe('When creating a mulligan selection', () => {
    it('should create with selected card IDs', () => {
      // Arrange
      const cardIds = ['card1', 'card2', 'card3'];

      // Act
      const selection = MulliganSelection.create({
        playerId: 'player1',
        matchId: 'match1',
        selectedCardIds: cardIds,
        skipped: false,
        round: 1
      });

      // Assert
      const snapshot = selection.toSnapshot();
      expect(snapshot.playerId).toBe('player1');
      expect(snapshot.matchId).toBe('match1');
      expect(snapshot.selectedCardIds).toEqual(cardIds);
      expect(snapshot.skipped).toBe(false);
    });

    it('should create skipped selection', () => {
      // Arrange

      // Act
      const selection = MulliganSelection.createSkipped('player1', 'match1', 1);

      // Assert
      const snapshot = selection.toSnapshot();
      expect(snapshot.playerId).toBe('player1');
      expect(snapshot.matchId).toBe('match1');
      expect(snapshot.selectedCardIds).toEqual([]);
      expect(snapshot.skipped).toBe(true);
    });

    it('should validate maximum selection count', () => {
      // Arrange
      const tooManyCards = Array.from({length: 8}, (_, i) => `card${i + 1}`);

      // Act
      const act = () => {
        MulliganSelection.create({
          playerId: 'player1',
          matchId: 'match1',
          selectedCardIds: tooManyCards,
          skipped: false,
          round: 1
        });
      };

      // Assert
      expect(act).toThrow('Cannot select more than 7 cards for mulligan');
    });

    it('should check if selection is complete', () => {
      // Arrange
      const withCards = MulliganSelection.create({
        playerId: 'player1',
        matchId: 'match1',
        selectedCardIds: ['card1'],
        skipped: false,
        round: 1
      });
      const skipped = MulliganSelection.createSkipped('player1', 'match1', 1);

      // Act
      const withCardsComplete = withCards.isComplete();
      const skippedComplete = skipped.isComplete();

      // Assert
      expect(withCardsComplete).toBe(true);
      expect(skippedComplete).toBe(true);
    });
  });
});