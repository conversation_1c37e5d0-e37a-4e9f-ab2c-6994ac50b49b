import {LoadGameListWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadGameListWebPresenter";
import {Game} from "@/src/server/Gaming/domain/Game/Game";
import {GameList} from "@/src/server/Gaming/domain/Game/GameList";

describe('LoadGameListWebPresenter', () => {
  let presenter: LoadGameListWebPresenter;

  beforeEach(() => {
    presenter = new LoadGameListWebPresenter();
  });

  describe('When displaying games', () => {
    it('should set game data', () => {
      // Arrange
      const games = [
        Game.fromSnapshot({id: 'g1', name: '<PERSON><PERSON><PERSON>'}),
        Game.fromSnapshot({id: 'g2', name: '<PERSON><PERSON><PERSON> des bois'}),
      ];
      const gameList = GameList.createFrom(games);

      // Act
      presenter.display(gameList);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual([
        {id: 'g1', name: '<PERSON><PERSON><PERSON>'},
        {id: 'g2', name: '<PERSON><PERSON><PERSON> des bois'},
      ]);
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
