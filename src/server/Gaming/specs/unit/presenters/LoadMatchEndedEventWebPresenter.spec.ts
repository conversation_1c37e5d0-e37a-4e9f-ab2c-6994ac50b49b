import {LoadMatchEndedEventWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchEndedEventWebPresenter";

describe('LoadMatchEndedEventWebPresenter', () => {
  let presenter: LoadMatchEndedEventWebPresenter;

  beforeEach(() => {
    presenter = new LoadMatchEndedEventWebPresenter();
  });

  describe('When displaying an event', () => {
    it('should set event data', () => {
      // Arrange
      const event = {winner: 'u2', loser: 'u1', matchId: 'm1'};

      // Act
      presenter.display(event);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual(event);
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
