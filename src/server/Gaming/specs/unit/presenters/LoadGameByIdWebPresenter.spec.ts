import {LoadGameByIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadGameByIdWebPresenter";
import {Game} from "@/src/server/Gaming/domain/Game/Game";

describe('LoadGameByIdWebPresenter', () => {
  let presenter: LoadGameByIdWebPresenter;

  beforeEach(() => {
    presenter = new LoadGameByIdWebPresenter();
  });

  describe('When displaying a game', () => {
    it('should set game data', () => {
      // Arrange
      const game = Game.fromSnapshot({id: 'g1', name: '<PERSON><PERSON><PERSON>'});

      // Act
      presenter.display(game);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({id: 'g1', name: '<PERSON><PERSON><PERSON>'});
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
