import {
  LoadGameFilterListWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadGameFilterListWebPresenter";
import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";
import {GameFilter} from "@/src/server/Gaming/domain/GameFilter/GameFilter";

describe('LoadAvailableFiltersWebPresenter', () => {
  let presenter: LoadGameFilterListWebPresenter;

  beforeEach(() => {
    presenter = new LoadGameFilterListWebPresenter();
  });

  describe('When displaying filters', () => {
    it('should group filters by data property', () => {
      // Arrange
      const filters: GameFilter[] = [
        {
          id: 'filter-1',
          text: 'Amber',
          name: 'AMBER',
          dataProperty: 'color',
          dataType: 'string',
          value: 'AMBER',
          order: 1,
        },
        {
          id: 'filter-2',
          text: '<PERSON>',
          name: 'RUB<PERSON>',
          dataProperty: 'color',
          dataType: 'string',
          value: 'RUBY',
          order: 2,
        },
        {
          id: 'filter-3',
          text: 'Inkable',
          name: 'INKABLE',
          dataProperty: 'inkable',
          dataType: 'boolean',
          value: true,
          order: 3,
        },
      ];
      const gameFilterList = GameFilterList.createFrom(filters);

      // Act
      presenter.display(gameFilterList);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data!.groupedFilters).toEqual({
        color: {
          name: 'color', filters: [
            {
              id: 'filter-1',
              text: 'Amber',
              name: 'AMBER',
              dataProperty: 'color',
              dataType: 'string',
              value: 'AMBER',
              order: 1
            },
            {
              id: 'filter-2',
              text: 'Ruby',
              name: 'RUBY',
              dataProperty: 'color',
              dataType: 'string',
              value: 'RUBY',
              order: 2
            },
          ]
        },
        inkable: {
          name: 'inkable', filters: [
            {
              id: 'filter-3',
              text: 'Inkable',
              name: 'INKABLE',
              dataProperty: 'inkable',
              dataType: 'boolean',
              value: true,
              order: 3
            },
          ]
        },
      });
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});


