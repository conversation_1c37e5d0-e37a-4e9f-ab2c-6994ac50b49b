import {LoadMatchEventsWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchEventsWebPresenter";
import {MatchEventList} from "@/src/server/Gaming/domain/MatchEvent/MatchEventList";
import {MatchEvent} from "@/src/server/Gaming/domain/MatchEvent/MatchEvent";

describe('LoadMatchEventsWebPresenter', () => {
  let presenter: LoadMatchEventsWebPresenter;

  beforeEach(() => {
    presenter = new LoadMatchEventsWebPresenter();
  });

  describe('When displaying events', () => {
    it('should set events data', () => {
      // Arrange
      const events: MatchEvent[] = [
        {id: '1', type: 'Started', payload: {}, occurredAt: 1},
        {id: '2', type: 'Ended', payload: {}, occurredAt: 2},
      ];
      const list = MatchEventList.createFrom(events);

      // Act
      presenter.display(list);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.data).toEqual({events});
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel.error).toBe(error.message);
    });
  });
});
