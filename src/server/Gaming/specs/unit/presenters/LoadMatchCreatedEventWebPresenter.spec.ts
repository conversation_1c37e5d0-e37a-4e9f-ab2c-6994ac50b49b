import {LoadMatchCreatedEventWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchCreatedEventWebPresenter";
import {MatchCreatedEvent} from "@/src/server/Gaming/domain/Match/MatchCreatedEvent";

describe('LoadMatchCreatedEventWebPresenter', () => {
  let presenter: LoadMatchCreatedEventWebPresenter;

  beforeEach(() => {
    presenter = new LoadMatchCreatedEventWebPresenter();
  });

  describe('When displaying an event', () => {
    it('should set event data', () => {
      // Arrange
      const event: MatchCreatedEvent = {type: 'MatchCreated', occurredAt: Date.now(), payload: {matchId: 'm1', players: ['u1', 'u2']}};

      // Act
      presenter.display(event);

      // Assert
      expect(presenter.getViewModel().data).toEqual(event);
    });
  });

  describe('When notifying no match for the user', () => {
    it('should set data to null', () => {
      // Act
      presenter.notifyNoMatchFoundForUser();

      // Assert
      expect(presenter.getViewModel().data).toBeNull();
    });
  });

  describe('When notifying the match is already finished', () => {
    it('should set data to null', () => {
      // Act
      presenter.notifyMatchAlreadyFinished();

      // Assert
      expect(presenter.getViewModel().data).toBeNull();
    });
  });

  describe('When displaying an error', () => {
    it('should set the error message', () => {
      // Arrange
      const error = new Error('Repository error');

      // Act
      presenter.displayError(error);

      // Assert
      expect(presenter.getViewModel().error).toBe(error.message);
    });
  });
});
