import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe('When loading match data', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it('should return my card image urls and opponent card count only', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {name: 'G', ownerId: 'owner'})) as Id<'games'>;
    const c1 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 1', image: '1.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;
    const c2 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 2', image: '2.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;
    const c3 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 3', image: '3.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;
    const matchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'setup',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 'gc1', catalogCardId: c1 as string, data: {a: 1}},
        {id: 'gc2', catalogCardId: c2 as string, data: {a: 2}},
      ],
    }));
    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: 'gc3', catalogCardId: c3 as string, data: {a: 3}},
      ],
    }));

    // Act
    const result = await sophie.query(api.queries.match.loadMatchData, {matchId});

    // Assert
    expect(result.error).toBeNull();
    expect(result.data?.myCardIds).toEqual([
      '/game-assets/cards/en/thumbnail/1.jpg',
      '/game-assets/cards/en/thumbnail/2.jpg',
    ]);
    expect(result.data?.opponentCardCount).toBe(1);
  });

  it('should return an error when match does not exist', async () => {
    // Act
    const result = await john.query(api.queries.match.loadMatchData, {matchId: 'missing'});

    // Assert
    expect(result.error).toBe('Match not found');
    expect(result.data).toBeNull();
  });
});
