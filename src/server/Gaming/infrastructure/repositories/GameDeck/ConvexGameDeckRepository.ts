import {GenericMutationCtx} from "convex/server";
import {DataModel, Doc, Id} from "@/convex/_generated/dataModel";
import {GameDeckRepository} from "@/src/server/Gaming/application/ports/GameDeckRepository";
import {GameDeck, GameDeckProps} from "@/src/server/Gaming/domain/GameDeck/GameDeck";

export class ConvexGameDeckRepository implements GameDeckRepository {
  private readonly ctx: GenericMutationCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel>) {
    this.ctx = ctx;
  }

  async save(deck: GameDeck): Promise<string> {
    const props = (deck as unknown as { props: GameDeckProps }).props;

    if (props.id) {
      const cards = props.cards.map((c) => (c as unknown as { toSnapshot: () => Doc<'gameDecks'>['cards'][number] }).toSnapshot());
      await this.ctx.db.patch(props.id as Id<'gameDecks'>, {
        playerId: props.playerId,
        cards,
      } as Partial<Doc<'gameDecks'>>);
      return props.id;
    }

    const id = await this.ctx.db.insert(
      'gameDecks',
      {
        gameId: props.gameId as Id<'games'>,
        playerId: props.playerId,
        cards: props.cards.map((c) => (c as unknown as { toSnapshot: () => Doc<'gameDecks'>['cards'][number] }).toSnapshot()),
      } as unknown as Omit<Doc<'gameDecks'>, '_id' | '_creationTime'>,
    );
    props.id = id as string;
    return id as unknown as string;
  }

  async findById(id: string): Promise<GameDeck | null> {
    const doc = await this.ctx.db.get(id as Id<'gameDecks'>);
    return doc
      ? GameDeck.fromSnapshot({
          id: doc._id,
          gameId: doc.gameId as unknown as string,
          playerId: doc.playerId,
          cards: doc.cards,
        })
      : null;
  }
}

