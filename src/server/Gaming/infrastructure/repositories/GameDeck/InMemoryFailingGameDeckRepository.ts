import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameDeckRepository} from '@/src/server/Gaming/application/ports/GameDeckRepository';

export class InMemoryFailingGameDeckRepository implements GameDeckRepository {
  async save(): Promise<string> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<GameDeck | null> {
    throw new Error(id);
  }
}
