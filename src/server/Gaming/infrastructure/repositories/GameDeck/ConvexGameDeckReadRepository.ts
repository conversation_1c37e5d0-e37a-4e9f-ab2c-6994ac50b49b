import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {GameDeckReadRepository} from "@/src/server/Gaming/application/ports/GameDeckReadRepository";
import {GameDeck} from "@/src/server/Gaming/domain/GameDeck/GameDeck";

export class ConvexGameDeckReadRepository implements GameDeckReadRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async findLatestByGameIdAndPlayerId(gameId: string, playerId: string): Promise<GameDeck | null> {
    const docs = await this.ctx.db
      .query('gameDecks')
      .withIndex('by_gameId', q => q.eq('gameId', gameId as Id<'games'>))
      .collect();

    const filtered = docs.filter(d => d.playerId === playerId);
    if (filtered.length === 0) return null;
    const latest = filtered.sort((a, b) => b._creationTime - a._creationTime)[0];
    return GameDeck.fromSnapshot({
      id: latest._id,
      gameId: latest.gameId as unknown as string,
      playerId: latest.playerId,
      cards: latest.cards,
    });
  }
}

