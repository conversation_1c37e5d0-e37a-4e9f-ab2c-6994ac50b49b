import {GameDeckRepository} from '@/src/server/Gaming/application/ports/GameDeckRepository';
import {GameDeck, GameDeckProps, GameDeckSnapshot} from '@/src/server/Gaming/domain/GameDeck/GameDeck';

export class InMemoryGameDeckRepository implements GameDeckRepository {
  private items = new Map<string, GameDeck>();
  private counter = 0;

  async save(deck: GameDeck) {
    const props = (deck as unknown as {props: GameDeckProps}).props;
    if (!props.id) {
      props.id = `gd${++this.counter}`;
    }
    this.items.set(props.id, deck);
    return props.id;
  }

  async findById(id: string): Promise<GameDeck | null> {
    return this.items.get(id) || null;
  }

  addDeck(snapshot: GameDeckSnapshot): void {
    this.items.set(snapshot.id, GameDeck.fromSnapshot(snapshot));
  }

  clear(): void {
    this.items.clear();
  }
}
