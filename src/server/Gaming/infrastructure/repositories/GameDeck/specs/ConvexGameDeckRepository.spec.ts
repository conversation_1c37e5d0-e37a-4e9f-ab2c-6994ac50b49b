import {ConvexGameDeckRepository} from '../ConvexGameDeckRepository';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';
import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';

describe('ConvexGameDeckRepository', () => {
  let testConvex: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    testConvex = convexTest(schema);
  });

  describe('When saving a new game deck', () => {
    it('should insert the deck and return the generated id', async () => {
      // Arrange
      const gameId = await testConvex.run(ctx => ctx.db.insert('games', {name: 'Test Game', ownerId: 'owner'})) as Id<'games'>;
      const card = GameCard.fromSnapshot({id: 'c1', catalogCardId: 'cc1', data: {attack: 5}});
      const deck = GameDeck.create({gameId: gameId as unknown as string, playerId: 'p1', cards: [card]});

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexGameDeckRepository(ctx);
        return await repository.save(deck);
      });

      // Assert
      const savedDeck = await testConvex.run(ctx => ctx.db.get(result as Id<'gameDecks'>));
      expect(savedDeck).not.toBeNull();
      expect(savedDeck!.gameId).toBe(gameId);
      expect(savedDeck!.playerId).toBe('p1');
      expect(savedDeck!.cards).toEqual([{id: 'c1', catalogCardId: 'cc1', data: {attack: 5}}]);
    });
  });

  describe('When saving an existing game deck', () => {
    it('should update the deck and return the existing id', async () => {
      // Arrange
      const gameId = await testConvex.run(ctx => ctx.db.insert('games', {name: 'Test Game', ownerId: 'owner'})) as Id<'games'>;
      const deckId = await testConvex.run(ctx => ctx.db.insert('gameDecks', {
        gameId,
        playerId: 'p1',
        cards: [{id: 'c1', catalogCardId: 'cc1', data: {attack: 1}}],
      })) as Id<'gameDecks'>;
      
      const updatedCard = GameCard.fromSnapshot({id: 'c1', catalogCardId: 'cc1', data: {attack: 3}});
      const deck = GameDeck.fromSnapshot({id: deckId as unknown as string, gameId: gameId as unknown as string, playerId: 'p1', cards: [updatedCard.toSnapshot()]});

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexGameDeckRepository(ctx);
        return await repository.save(deck);
      });

      // Assert
      expect(result).toBe(deckId);
      const updatedDeck = await testConvex.run(ctx => ctx.db.get(deckId));
      expect(updatedDeck!.cards).toEqual([{id: 'c1', catalogCardId: 'cc1', data: {attack: 3}}]);
    });
  });

  describe('When finding a deck by id', () => {
    it('should return the deck when it exists', async () => {
      // Arrange
      const gameId = await testConvex.run(ctx => ctx.db.insert('games', {name: 'Test Game', ownerId: 'owner'})) as Id<'games'>;
      const deckId = await testConvex.run(ctx => ctx.db.insert('gameDecks', {
        gameId,
        playerId: 'p1',
        cards: [{id: 'c1', catalogCardId: 'cc1', data: {attack: 7}}],
      })) as Id<'gameDecks'>;

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexGameDeckRepository(ctx);
        const deck = await repository.findById(deckId as unknown as string);
        return deck ? deck.toSnapshot() : null;
      });

      // Assert
      expect(result).not.toBeNull();
      expect(result).toEqual({
        id: deckId,
        gameId: gameId as unknown as string,
        playerId: 'p1',
        cards: [{id: 'c1', catalogCardId: 'cc1', data: {attack: 7}}],
      });
    });

    it('should return null when deck does not exist', async () => {
      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexGameDeckRepository(ctx);
        return await repository.findById('unknown');
      });

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('When saving a deck with multiple cards', () => {
    it('should properly serialize all cards', async () => {
      // Arrange
      const gameId = await testConvex.run(ctx => ctx.db.insert('games', {name: 'Test Game', ownerId: 'owner'})) as Id<'games'>;
      const card1 = GameCard.fromSnapshot({id: 'c1', catalogCardId: 'cc1', data: {attack: 1}});
      const card2 = GameCard.fromSnapshot({id: 'c2', catalogCardId: 'cc2', data: {defense: 2}});
      const deck = GameDeck.create({gameId: gameId as unknown as string, playerId: 'p1', cards: [card1, card2]});

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexGameDeckRepository(ctx);
        return await repository.save(deck);
      });

      // Assert
      const savedDeck = await testConvex.run(ctx => ctx.db.get(result as Id<'gameDecks'>));
      expect(savedDeck!.cards).toEqual([
        {id: 'c1', catalogCardId: 'cc1', data: {attack: 1}},
        {id: 'c2', catalogCardId: 'cc2', data: {defense: 2}},
      ]);
    });
  });

  describe('When updating a deck with modified cards', () => {
    it('should update with the new card data', async () => {
      // Arrange
      const gameId = await testConvex.run(ctx => ctx.db.insert('games', {name: 'Test Game', ownerId: 'owner'})) as Id<'games'>;
      const deckId = await testConvex.run(ctx => ctx.db.insert('gameDecks', {
        gameId,
        playerId: 'p1',
        cards: [{id: 'c1', catalogCardId: 'cc1', data: {attack: 1}}],
      })) as Id<'gameDecks'>;
      
      const updatedCard = GameCard.fromSnapshot({id: 'c1', catalogCardId: 'cc1', data: {attack: 10}});
      const deck = GameDeck.fromSnapshot({id: deckId as unknown as string, gameId: gameId as unknown as string, playerId: 'p1', cards: [updatedCard.toSnapshot()]});

      // Act
      await testConvex.run(async (ctx) => {
        const repository = new ConvexGameDeckRepository(ctx);
        return await repository.save(deck);
      });

      // Assert
      const updatedDeck = await testConvex.run(ctx => ctx.db.get(deckId));
      expect(updatedDeck!.cards).toEqual([{id: 'c1', catalogCardId: 'cc1', data: {attack: 10}}]);
    });
  });

  describe('When finding deck with complex card data', () => {
    it('should properly deserialize complex card structures', async () => {
      // Arrange
      const gameId = await testConvex.run(ctx => ctx.db.insert('games', {name: 'Test Game', ownerId: 'owner'})) as Id<'games'>;
      const deckId = await testConvex.run(ctx => ctx.db.insert('gameDecks', {
        gameId,
        playerId: 'p1',
        cards: [
          {id: 'c1', catalogCardId: 'cc1', data: {attack: 5, defense: 3, abilities: ['fly', 'haste']}},
          {id: 'c2', catalogCardId: 'cc2', data: {cost: 2, type: 'spell', effect: 'damage'}},
        ],
      })) as Id<'gameDecks'>;

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexGameDeckRepository(ctx);
        const deck = await repository.findById(deckId as unknown as string);
        return deck ? deck.toSnapshot() : null;
      });

      // Assert
      expect(result).toEqual({
        id: deckId,
        gameId: gameId as unknown as string,
        playerId: 'p1',
        cards: [
          {id: 'c1', catalogCardId: 'cc1', data: {attack: 5, defense: 3, abilities: ['fly', 'haste']}},
          {id: 'c2', catalogCardId: 'cc2', data: {cost: 2, type: 'spell', effect: 'damage'}},
        ],
      });
    });
  });
});