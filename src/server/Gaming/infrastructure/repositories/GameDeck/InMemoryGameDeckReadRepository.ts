import {GameDeckReadRepository} from '@/src/server/Gaming/application/ports/GameDeckReadRepository';
import {GameDeck, GameDeckSnapshot} from '@/src/server/Gaming/domain/GameDeck/GameDeck';

export class InMemoryGameDeckReadRepository implements GameDeckReadRepository {
  private items = new Map<string, GameDeck>();

  async findLatestByGameIdAndPlayerId(gameId: string, playerId: string): Promise<GameDeck | null> {
    const decks = Array.from(this.items.values())
      .filter(deck => {
        const snapshot = deck.toSnapshot();
        return snapshot.gameId === gameId && snapshot.playerId === playerId;
      })
      .sort((a, b) => {
        const snapshotA = a.toSnapshot();
        const snapshotB = b.toSnapshot();
        return snapshotB.id.localeCompare(snapshotA.id);
      });

    return decks.length > 0 ? decks[0] : null;
  }

  addDeck(snapshot: GameDeckSnapshot): void {
    this.items.set(snapshot.id, GameDeck.fromSnapshot(snapshot));
  }

  async save(deck: GameDeck): Promise<string> {
    const snapshot = deck.toSnapshot();
    this.items.set(snapshot.id, deck);
    return snapshot.id;
  }

  clear(): void {
    this.items.clear();
  }
}