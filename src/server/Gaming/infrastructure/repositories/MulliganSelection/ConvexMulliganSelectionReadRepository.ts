import {GenericQueryCtx} from 'convex/server';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';
import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';

export class ConvexMulliganSelectionReadRepository implements MulliganSelectionRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async save(selection: MulliganSelection): Promise<string> {
    void selection;
    throw new Error('Read-only repository cannot save');
  }

  async findByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection | null> {
    const docs = await this.ctx.db
      .query('mulliganSelections')
      .withIndex('by_matchId_and_playerId', q => q.eq('matchId', matchId as Id<'matches'>).eq('playerId', playerId))
      .collect();
    
    if (docs.length === 0) return null;
    
    const latestDoc = docs.sort((a, b) => (b.round || 1) - (a.round || 1))[0];
    return MulliganSelection.fromSnapshot({id: latestDoc._id, ...latestDoc, round: latestDoc.round || 1});
  }

  async findByMatchIdPlayerIdAndRound(matchId: string, playerId: string, round: number): Promise<MulliganSelection | null> {
    const doc = await this.ctx.db
      .query('mulliganSelections')
      .withIndex('by_matchId_playerId_round', q => q.eq('matchId', matchId as Id<'matches'>).eq('playerId', playerId).eq('round', round))
      .first();
    
    return doc ? MulliganSelection.fromSnapshot({id: doc._id, ...doc, round: doc.round || 1}) : null;
  }

  async findAllByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection[]> {
    const docs = await this.ctx.db
      .query('mulliganSelections')
      .withIndex('by_matchId_and_playerId', q => q.eq('matchId', matchId as Id<'matches'>).eq('playerId', playerId))
      .collect();
    
    return docs.map(doc => MulliganSelection.fromSnapshot({id: doc._id, ...doc, round: doc.round || 1}));
  }
}