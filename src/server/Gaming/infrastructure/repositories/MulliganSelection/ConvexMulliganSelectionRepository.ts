import {GenericMutationCtx} from 'convex/server';
import {DataModel, Doc, Id} from '@/convex/_generated/dataModel';
import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';
import {MulliganSelection, MulliganSelectionProps} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';

export class ConvexMulliganSelectionRepository implements MulliganSelectionRepository {
  private readonly ctx: GenericMutationCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel>) {
    this.ctx = ctx;
  }

  async save(selection: MulliganSelection): Promise<string> {
    const props = (selection as unknown as {props: MulliganSelectionProps}).props;
    const snapshot = selection.toSnapshot();
    
    if (props.id) {
      await this.ctx.db.patch(props.id as Id<'mulliganSelections'>, {
        selectedCardIds: snapshot.selectedCardIds,
        skipped: snapshot.skipped,
        round: snapshot.round,
      } as Partial<Doc<'mulliganSelections'>>);
      return props.id;
    }

    const id = await this.ctx.db.insert(
      'mulliganSelections',
      {
        matchId: snapshot.matchId as Id<'matches'>,
        playerId: snapshot.playerId,
        selectedCardIds: snapshot.selectedCardIds,
        skipped: snapshot.skipped,
        round: snapshot.round,
      } as unknown as Omit<Doc<'mulliganSelections'>, '_id' | '_creationTime'>
    );
    props.id = id as string;
    return id;
  }

  async findByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection | null> {
    const docs = await this.ctx.db
      .query('mulliganSelections')
      .withIndex('by_matchId_and_playerId', q => q.eq('matchId', matchId as Id<'matches'>).eq('playerId', playerId))
      .collect();
    
    if (docs.length === 0) return null;
    
    const latestDoc = docs.sort((a, b) => (b.round || 1) - (a.round || 1))[0];
    return MulliganSelection.fromSnapshot({id: latestDoc._id, ...latestDoc, round: latestDoc.round || 1});
  }

  async findByMatchIdPlayerIdAndRound(matchId: string, playerId: string, round: number): Promise<MulliganSelection | null> {
    const doc = await this.ctx.db
      .query('mulliganSelections')
      .withIndex('by_matchId_playerId_round', q => q.eq('matchId', matchId as Id<'matches'>).eq('playerId', playerId).eq('round', round))
      .first();
    
    return doc ? MulliganSelection.fromSnapshot({id: doc._id, ...doc, round: doc.round || 1}) : null;
  }

  async findAllByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection[]> {
    const docs = await this.ctx.db
      .query('mulliganSelections')
      .withIndex('by_matchId_and_playerId', q => q.eq('matchId', matchId as Id<'matches'>).eq('playerId', playerId))
      .collect();
    
    return docs.map(doc => MulliganSelection.fromSnapshot({id: doc._id, ...doc, round: doc.round || 1}));
  }
}