import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';
import {MulliganSelection, MulliganSelectionProps} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';

export class InMemoryMulliganSelectionRepository implements MulliganSelectionRepository {
  private items = new Map<string, MulliganSelection>();
  private counter = 0;

  async save(selection: MulliganSelection): Promise<string> {
    const props = (selection as unknown as {props: MulliganSelectionProps}).props;
    if (props.id && this.items.has(props.id)) {
      this.items.set(props.id, selection);
      return props.id;
    }

    const id = `ms${++this.counter}`;
    props.id = id;
    this.items.set(id, selection);
    return id;
  }

  async findByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection | null> {
    const selections = [];
    for (const selection of this.items.values()) {
      const snapshot = selection.toSnapshot();
      if (snapshot.matchId === matchId && snapshot.playerId === playerId) {
        selections.push(selection);
      }
    }
    
    if (selections.length === 0) return null;
    
    return selections.sort((a, b) => b.getRound() - a.getRound())[0];
  }

  async findByMatchIdPlayerIdAndRound(matchId: string, playerId: string, round: number): Promise<MulliganSelection | null> {
    for (const selection of this.items.values()) {
      const snapshot = selection.toSnapshot();
      if (snapshot.matchId === matchId && snapshot.playerId === playerId && snapshot.round === round) {
        return selection;
      }
    }
    return null;
  }

  async findAllByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection[]> {
    const selections = [];
    for (const selection of this.items.values()) {
      const snapshot = selection.toSnapshot();
      if (snapshot.matchId === matchId && snapshot.playerId === playerId) {
        selections.push(selection);
      }
    }
    return selections;
  }
}