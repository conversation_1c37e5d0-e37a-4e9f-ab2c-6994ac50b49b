import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';
import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';

export class InMemoryFailingMulliganSelectionRepository implements MulliganSelectionRepository {
  async save(selection: MulliganSelection): Promise<string> {
    void selection;
    throw new Error('Repository failed to save mulligan selection');
  }

  async findByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection | null> {
    void matchId;
    void playerId;
    throw new Error('Repository failed to find mulligan selection');
  }

  async findByMatchIdPlayerIdAndRound(matchId: string, playerId: string, round: number): Promise<MulliganSelection | null> {
    void matchId;
    void playerId;
    void round;
    throw new Error('Repository failed to find mulligan selection');
  }

  async findAllByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection[]> {
    void matchId;
    void playerId;
    throw new Error('Repository failed to find mulligan selections');
  }
}