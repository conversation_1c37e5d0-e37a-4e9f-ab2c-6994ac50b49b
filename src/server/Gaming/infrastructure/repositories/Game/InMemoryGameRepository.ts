import {GameRepository} from "@/src/server/Gaming/application/ports/GameRepository";
import {Game, GameSnapshot} from "@/src/server/Gaming/domain/Game/Game";

export class InMemoryGameRepository implements GameRepository {
  private games = new Map<string, Game>();

  async getById(gameId: string): Promise<Game | undefined> {
    return this.games.get(gameId);
  }

  addGame(snapshot: GameSnapshot): void {
    this.games.set(snapshot.id, Game.fromSnapshot(snapshot));
  }

  clear(): void {
    this.games.clear();
  }
}
