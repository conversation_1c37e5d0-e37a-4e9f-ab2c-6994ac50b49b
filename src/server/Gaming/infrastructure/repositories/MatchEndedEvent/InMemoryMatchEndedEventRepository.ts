import {MatchEndedEventRepository} from "@/src/server/Gaming/application/ports/MatchEndedEventRepository";
import {MatchEndedEvent} from "@/src/server/Gaming/domain/MatchEvent/MatchEndedEvent";

export class InMemoryMatchEndedEventRepository implements MatchEndedEventRepository {
  private events = new Map<string, MatchEndedEvent>();

  async getByMatchId(matchId: string): Promise<MatchEndedEvent | null> {
    return this.events.get(matchId) || null;
  }

  addEvent(matchId: string, event: MatchEndedEvent): void {
    this.events.set(matchId, event);
  }

  clear(): void {
    this.events.clear();
  }
}
