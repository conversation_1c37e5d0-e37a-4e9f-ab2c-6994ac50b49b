import {GenericQueryCtx} from "convex/server";
import {DataModel} from "@/convex/_generated/dataModel";
import {GameListRepository} from "@/src/server/Gaming/application/ports/GameListRepository";
import {GameList} from "@/src/server/Gaming/domain/Game/GameList";
import {Game} from "@/src/server/Gaming/domain/Game/Game";

export class ConvexGameListRepository implements GameListRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getAll(): Promise<GameList> {
    const games = await this.ctx.db.query('games').collect();
    return GameList.createFrom(
      games.map(game => Game.fromSnapshot({id: game._id, name: game.name}))
    );
  }
}
