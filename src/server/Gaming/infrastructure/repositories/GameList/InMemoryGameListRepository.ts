import {GameListRepository} from "@/src/server/Gaming/application/ports/GameListRepository";
import {Game, GameSnapshot} from "@/src/server/Gaming/domain/Game/Game";
import {GameList} from "@/src/server/Gaming/domain/Game/GameList";

export class InMemoryGameListRepository implements GameListRepository {
  private games: Game[] = [];

  async getAll(): Promise<GameList> {
    return GameList.createFrom([...this.games]);
  }

  addGame(snapshot: GameSnapshot): void {
    this.games.push(Game.fromSnapshot(snapshot));
  }

  clear(): void {
    this.games = [];
  }
}
