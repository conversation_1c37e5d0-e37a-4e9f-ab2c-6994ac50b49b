import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {MatchEventListRepository} from "@/src/server/Gaming/application/ports/MatchEventListRepository";
import {MatchEventList} from "@/src/server/Gaming/domain/MatchEvent/MatchEventList";

export class ConvexMatchEventListRepository implements MatchEventListRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getByMatchId(matchId: string): Promise<MatchEventList> {
    const events = await this.ctx.db
      .query("matchEvents")
      .withIndex("by_matchId", q => q.eq("matchId", matchId as Id<"matches">))
      .collect();

    const mapped = events.map(doc => ({
      id: doc._id,
      type: doc.type,
      payload: doc.payload,
      occurredAt: doc.occurredAt,
    }));

    return MatchEventList.createFrom(mapped);
  }
}
