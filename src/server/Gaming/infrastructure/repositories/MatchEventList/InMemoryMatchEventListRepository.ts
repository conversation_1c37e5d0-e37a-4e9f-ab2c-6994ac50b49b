import {MatchEventListRepository} from "@/src/server/Gaming/application/ports/MatchEventListRepository";
import {MatchEventList} from "@/src/server/Gaming/domain/MatchEvent/MatchEventList";
import {MatchEvent} from "@/src/server/Gaming/domain/MatchEvent/MatchEvent";

export class InMemoryMatchEventListRepository implements MatchEventListRepository {
  private events: Map<string, MatchEvent[]> = new Map();

  async getByMatchId(matchId: string): Promise<MatchEventList> {
    const matchEvents = this.events.get(matchId) || [];
    return MatchEventList.createFrom(matchEvents);
  }

  addEventsForMatch(matchId: string, events: MatchEvent[]): void {
    this.events.set(matchId, events);
  }

  clear(): void {
    this.events.clear();
  }
}
