import {GameFilterList} from "@/src/server/Gaming/domain/GameFilter/GameFilterList";
import {GameFilter} from "@/src/server/Gaming/domain/GameFilter/GameFilter";
import {GameFilterListRepository} from "@/src/server/Gaming/application/ports/GameFilterListRepository";

export class InMemoryGameFilterListRepository implements GameFilterListRepository {
  private filters: Map<string, GameFilter[]> = new Map();

  async getByGameId(gameId: string): Promise<GameFilterList> {
    const gameFilters = this.filters.get(gameId) || [];
    return GameFilterList.createFrom(gameFilters);
  }

  addFiltersForGame(gameId: string, filters: GameFilter[]): void {
    this.filters.set(gameId, filters);
  }

  clear(): void {
    this.filters.clear();
  }
}
