import {MatchReadRepository} from '@/src/server/Gaming/application/ports/MatchReadRepository';
import {Match, MatchSnapshot} from '@/src/server/Gaming/domain/Match/Match';

export class InMemoryMatchReadRepository implements MatchReadRepository {
  private items = new Map<string, Match>();

  async findById(id: string): Promise<Match | null> {
    return this.items.get(id) || null;
  }

  addMatch(snapshot: MatchSnapshot): void {
    this.items.set(snapshot.id, Match.fromSnapshot(snapshot));
  }

  async save(match: Match): Promise<string> {
    const snapshot = match.toSnapshot();
    this.items.set(snapshot.id, match);
    return snapshot.id;
  }

  clear(): void {
    this.items.clear();
  }
}