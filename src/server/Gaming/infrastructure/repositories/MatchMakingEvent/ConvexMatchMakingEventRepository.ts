import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {MatchMakingEventRepository} from "@/src/server/Gaming/application/ports/MatchMakingEventRepository";
import {MatchmakingEvent} from "@/convex/dispatchers/matchmaking";
import {MatchMakingEventList} from "@/src/server/Gaming/domain/MatchMakingEvent/MatchMakingEventList";

export class ConvexMatchMakingEventRepository implements MatchMakingEventRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getByGameId(gameId: string, type?: MatchmakingEvent["type"]): Promise<MatchMakingEventList> {
    let docs;
    if (type) {
      docs = await this.ctx.db
        .query("matchmakingEvents")
        .withIndex('by_gameId_and_type', q =>
          q.eq("gameId", gameId as Id<'games'>).eq("type", type)
        )
        .order('desc')
        .collect();
    } else {
      docs = await this.ctx.db
        .query("matchmakingEvents")
        .withIndex("by_gameId", q => q.eq("gameId", gameId as Id<'games'>))
        .order('desc')
        .collect();
    }
    return MatchMakingEventList.createFrom(docs as unknown as MatchmakingEvent[]);
  }
}
