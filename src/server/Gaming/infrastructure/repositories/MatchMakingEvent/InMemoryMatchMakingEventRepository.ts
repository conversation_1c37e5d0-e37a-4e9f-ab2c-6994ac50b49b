import {MatchMakingEventRepository} from "@/src/server/Gaming/application/ports/MatchMakingEventRepository";
import {MatchMakingEventList} from "@/src/server/Gaming/domain/MatchMakingEvent/MatchMakingEventList";
import {MatchmakingEvent} from "@/convex/dispatchers/matchmaking";

export class InMemoryMatchMakingEventRepository implements MatchMakingEventRepository {
  private events: Map<string, MatchmakingEvent[]> = new Map();

  async getByGameId(gameId: string, type?: MatchmakingEvent["type"]): Promise<MatchMakingEventList> {
    const list = this.events.get(gameId) || [];
    if (type) {
      return MatchMakingEventList.createFrom(list.filter(ev => ev.type === type));
    }
    return MatchMakingEventList.createFrom(list);
  }

  addEvent(gameId: string, event: MatchmakingEvent) {
    const list = this.events.get(gameId) || [];
    list.push(event);
    this.events.set(gameId, list);
  }

  clear() {
    this.events.clear();
  }
}
