import {LoadGameByIdQuery} from "@/src/server/Gaming/application/queries/LoadGameById/LoadGameByIdQuery";
import {GameRepository} from "@/src/server/Gaming/application/ports/GameRepository";
import {LoadGameByIdPresenter} from "@/src/server/Gaming/application/ports/LoadGameByIdPresenter";

export class LoadGameByIdQueryHandler {
  private readonly repository: GameRepository;

  constructor(repository: GameRepository) {
    this.repository = repository;
  }

  async handle({gameId}: LoadGameByIdQuery, presenter: LoadGameByIdPresenter) {
    try {
      const game = await this.repository.getById(gameId);
      if (!game) {
        presenter.displayError(new Error('Game not found'));
        return;
      }
      presenter.display(game);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
