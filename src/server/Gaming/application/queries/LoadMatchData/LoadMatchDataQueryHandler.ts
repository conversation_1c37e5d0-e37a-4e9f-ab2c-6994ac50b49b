import {LoadMatchDataQuery} from "@/src/server/Gaming/application/queries/LoadMatchData/LoadMatchDataQuery";
import {MatchReadRepository} from "@/src/server/Gaming/application/ports/MatchReadRepository";
import {GameDeckReadRepository} from "@/src/server/Gaming/application/ports/GameDeckReadRepository";
import {LoadMatchDataPresenter} from "@/src/server/Gaming/application/ports/LoadMatchDataPresenter";
import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";

export class LoadMatchDataQueryHandler {
  private readonly matchRepository: MatchReadRepository;
  private readonly deckRepository: GameDeckReadRepository;
  private readonly catalogRepository: CatalogCardListRepository;

  constructor(matchRepository: MatchReadRepository, deckRepository: GameDeckReadRepository, catalogRepository: CatalogCardListRepository) {
    this.matchRepository = matchRepository;
    this.deckRepository = deckRepository;
    this.catalogRepository = catalogRepository;
  }

  async handle({matchId, userId}: LoadMatchDataQuery, presenter: LoadMatchDataPresenter) {
    try {
      const match = await this.matchRepository.findById(matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      const gameId = match.getGameId();
      const myId = userId;
      const opponentId = match.getOpponentOf(myId);

      const [myDeck, opponentDeck, catalog] = await Promise.all([
        this.deckRepository.findLatestByGameIdAndPlayerId(gameId, myId),
        this.deckRepository.findLatestByGameIdAndPlayerId(gameId, opponentId),
        this.catalogRepository.getByGameId(gameId),
      ]);

      const toImageUrls = (ids: string[]) => ids
        .map((catalogCardId) => catalog.findCardById(catalogCardId))
        .filter((c): c is NonNullable<typeof c> => Boolean(c))
        .map((c) => `/game-assets/cards/en/thumbnail/${c.image}`);

      const myCardUrls = myDeck ? toImageUrls(myDeck.toSnapshot().cards.map(c => c.catalogCardId)) : [];
      const opponentCardCount = opponentDeck ? opponentDeck.toSnapshot().cards.length : 0;

      presenter.display(myCardUrls, opponentCardCount);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
