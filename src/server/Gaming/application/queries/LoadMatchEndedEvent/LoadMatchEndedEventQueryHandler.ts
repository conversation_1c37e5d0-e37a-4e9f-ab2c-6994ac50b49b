import {LoadMatchEndedEventQuery} from "@/src/server/Gaming/application/queries/LoadMatchEndedEvent/LoadMatchEndedEventQuery";
import {LoadMatchEndedEventPresenter} from "@/src/server/Gaming/application/ports/LoadMatchEndedEventPresenter";
import {MatchEndedEventRepository} from "@/src/server/Gaming/application/ports/MatchEndedEventRepository";

export class LoadMatchEndedEventQueryHandler {
  private readonly repository: MatchEndedEventRepository;

  constructor(repository: MatchEndedEventRepository) {
    this.repository = repository;
  }

  async handle({matchId}: LoadMatchEndedEventQuery, presenter: LoadMatchEndedEventPresenter) {
    try {
      const event = await this.repository.getByMatchId(matchId);
      presenter.display(event);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
