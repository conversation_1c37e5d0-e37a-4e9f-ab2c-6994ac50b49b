import {LoadMatchByIdQuery} from "@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQuery";
import {MatchReadRepository} from "@/src/server/Gaming/application/ports/MatchReadRepository";
import {LoadMatchByIdPresenter} from "@/src/server/Gaming/application/ports/LoadMatchByIdPresenter";

export class LoadMatchByIdQueryHandler {
  private readonly repository: MatchReadRepository;

  constructor(repository: MatchReadRepository) {
    this.repository = repository;
  }

  async handle({matchId, userId}: Load<PERSON>atch<PERSON>yIdQuery, presenter: LoadMatchByIdPresenter) {
    try {
      const match = await this.repository.findById(matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }
      presenter.display(match, userId);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
