import {LoadGameDeckByIdQuery} from './LoadGameDeckByIdQuery';
import {GameDeckRepository} from '@/src/server/Gaming/application/ports/GameDeckRepository';
import {LoadGameDeckByIdPresenter} from '@/src/server/Gaming/application/ports/LoadGameDeckByIdPresenter';

export class LoadGameDeckByIdQueryHandler {
  private readonly repository: GameDeckRepository;

  constructor(repository: GameDeckRepository) {
    this.repository = repository;
  }

  async handle({deckId}: LoadGameDeckByIdQuery, presenter: LoadGameDeckByIdPresenter) {
    try {
      const deck = await this.repository.findById(deckId);
      if (!deck) {
        presenter.displayError(new Error('Deck not found'));
        return;
      }
      presenter.display(deck);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
