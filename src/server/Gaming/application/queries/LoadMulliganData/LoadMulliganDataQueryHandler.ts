import {LoadMulliganDataQuery} from './LoadMulliganDataQuery';
import {MatchReadRepository} from '@/src/server/Gaming/application/ports/MatchReadRepository';
import {GameDeckReadRepository} from '@/src/server/Gaming/application/ports/GameDeckReadRepository';
import {LoadMulliganDataPresenter} from '@/src/server/Gaming/application/ports/LoadMulliganDataPresenter';
import {CatalogCardListRepository} from '@/src/server/DeckBuilding/application/ports/CatalogCardListRepository';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';

export class LoadMulliganDataQueryHandler {
  private readonly matchRepository: MatchReadRepository;
  private readonly deckRepository: GameDeckReadRepository;
  private readonly catalogRepository: CatalogCardListRepository;
  private readonly gameSettingsRepository: GameSettingsRepository;
  private readonly mulliganRepository: MulliganSelectionRepository;

  constructor(
    matchRepository: MatchReadRepository,
    deckRepository: GameDeckReadRepository,
    catalogRepository: CatalogCardListRepository,
    gameSettingsRepository: GameSettingsRepository,
    mulliganRepository: MulliganSelectionRepository
  ) {
    this.matchRepository = matchRepository;
    this.deckRepository = deckRepository;
    this.catalogRepository = catalogRepository;
    this.gameSettingsRepository = gameSettingsRepository;
    this.mulliganRepository = mulliganRepository;
  }

  async handle(query: LoadMulliganDataQuery, presenter: LoadMulliganDataPresenter): Promise<void> {
    try {
      const match = await this.matchRepository.findById(query.matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      if (!match.isWaitingForMulligan()) {
        presenter.displayError(new Error('Match is not in mulligan phase'));
        return;
      }

      const gameId = match.getGameId();
      const [playerDeck, catalog] = await Promise.all([
        this.deckRepository.findLatestByGameIdAndPlayerId(gameId, query.userId),
        this.catalogRepository.getByGameId(gameId)
      ]);

      if (!playerDeck) {
        presenter.displayError(new Error('Player deck not found'));
        return;
      }

      const deckSnapshot = playerDeck.toSnapshot();
      const handCards = deckSnapshot.cards.slice(0, 7);
      const cardUrls = handCards.map(card => {
        const catalogCard = catalog.findCardById(card.catalogCardId);
        return catalogCard ? `/game-assets/cards/en/thumbnail/${catalogCard.image}` : '';
      });

      const gameSettings = await this.gameSettingsRepository.getByGameId(match.getGameId());
      if (!gameSettings) {
        presenter.displayError(new Error('Game settings not found'));
        return;
      }

      const previousSelections = await this.mulliganRepository.findAllByMatchIdAndPlayerId(query.matchId, query.userId);
      const previousSelectionsData = previousSelections.map(selection => ({
        round: selection.getRound(),
        selectedCardIds: selection.getSelectedCardIds(),
        skipped: selection.isSkipped()
      }));

      const playerCurrentRound = previousSelections.length + 1;
      const maxRounds = gameSettings.getMulliganCount();
      const hasCompletedAllRounds = playerCurrentRound > maxRounds;

      let finalHandCardIds = handCards.map(c => c.id);
      let finalHandCardUrls = cardUrls;

      if (hasCompletedAllRounds) {
        previousSelectionsData
          .sort((a, b) => a.round - b.round)
          .forEach(selection => {
            if (!selection.skipped) {
              finalHandCardIds = finalHandCardIds.filter(cardId => !selection.selectedCardIds.includes(cardId));
            }
          });
        
        finalHandCardUrls = finalHandCardIds.map(cardId => {
          const cardIndex = handCards.findIndex(c => c.id === cardId);
          return cardIndex >= 0 ? cardUrls[cardIndex] : '';
        });
      }

      presenter.display({
        matchId: query.matchId,
        handCardIds: handCards.map(c => c.id),
        handCardUrls: cardUrls,
        finalHandCardIds: finalHandCardIds,
        finalHandCardUrls: finalHandCardUrls,
        currentRound: hasCompletedAllRounds ? maxRounds : playerCurrentRound,
        maxRounds: maxRounds,
        previousSelections: previousSelectionsData,
        hasCompletedAllRounds: hasCompletedAllRounds
      });
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}