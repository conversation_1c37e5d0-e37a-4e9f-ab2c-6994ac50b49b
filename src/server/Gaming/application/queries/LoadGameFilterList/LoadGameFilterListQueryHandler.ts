import {
  LoadGameFilterListQuery
} from "@/src/server/Gaming/application/queries/LoadGameFilterList/LoadGameFilterListQuery";
import {LoadGameFilterListPresenter} from "@/src/server/Gaming/application/ports/LoadGameFilterListPresenter";
import {GameFilterListRepository} from "@/src/server/Gaming/application/ports/GameFilterListRepository";

export class LoadGameFilterListQueryHandler {
  private readonly repository: GameFilterListRepository;

  constructor(repository: GameFilterListRepository) {
    this.repository = repository;
  }

  async handle({gameId}: LoadGameFilter<PERSON>ist<PERSON><PERSON><PERSON>, presenter: LoadGameFilterListPresenter) {
    try {
      const gameFilterList = await this.repository.getByGameId(gameId);
      gameFilterList.sortByOrderId();
      presenter.display(gameFilterList);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
