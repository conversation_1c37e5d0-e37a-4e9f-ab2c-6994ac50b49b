import {LoadGameListQuery} from "@/src/server/Gaming/application/queries/LoadGameList/LoadGameListQuery";
import {GameListRepository} from "@/src/server/Gaming/application/ports/GameListRepository";
import {LoadGameListPresenter} from "@/src/server/Gaming/application/ports/LoadGameListPresenter";

export class LoadGameListQueryHandler {
  private readonly repository: GameListRepository;

  constructor(repository: GameListRepository) {
    this.repository = repository;
  }

  async handle(_: LoadGameListQuery, presenter: LoadGameListPresenter) {
    try {
      const gameList = await this.repository.getAll();
      presenter.display(gameList);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
