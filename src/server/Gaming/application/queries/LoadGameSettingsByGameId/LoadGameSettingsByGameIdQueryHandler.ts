import {LoadGameSettingsByGameIdQuery} from '@/src/server/Gaming/application/queries/LoadGameSettingsByGameId/LoadGameSettingsByGameIdQuery';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {LoadGameSettingsByGameIdPresenter} from '@/src/server/Gaming/application/ports/LoadGameSettingsByGameIdPresenter';

export class LoadGameSettingsByGameIdQueryHandler {
  private readonly repository: GameSettingsRepository;

  constructor(repository: GameSettingsRepository) {
    this.repository = repository;
  }

  async handle({gameId}: LoadGameSettingsByGameIdQuery, presenter: LoadGameSettingsByGameIdPresenter) {
    try {
      const settings = await this.repository.getByGameId(gameId);
      presenter.display(settings);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
