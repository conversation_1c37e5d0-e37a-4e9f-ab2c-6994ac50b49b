import {CreateGameDeckCommand} from './CreateGameDeckCommand';
import {DeckRepository} from '@/src/server/DeckBuilding/application/ports/DeckRepository';
import {CatalogCardListRepository} from '@/src/server/DeckBuilding/application/ports/CatalogCardListRepository';
import {GameDeckRepository} from '@/src/server/Gaming/application/ports/GameDeckRepository';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';

export class CreateGameDeckCommandHandler {
  private readonly deckRepository: DeckRepository;
  private readonly catalogRepository: CatalogCardListRepository;
  private readonly gameDeckRepository: GameDeckRepository;

  constructor(
    deckRepository: DeckRepository,
    catalogRepository: CatalogCardListRepository,
    gameDeckRepository: GameDeckRepository,
  ) {
    this.deckRepository = deckRepository;
    this.catalogRepository = catalogRepository;
    this.gameDeckRepository = gameDeckRepository;
  }

  private shuffleCards<T>(cards: T[]): T[] {
    const shuffled = [...cards];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  async handle({deckId}: CreateGameDeckCommand): Promise<string> {
    const deck = await this.deckRepository.findById(deckId);
    if (!deck) {
      throw new Error('Deck not found');
    }
    const snapshot = deck.toSnapshot();
    const catalog = await this.catalogRepository.getByGameId(snapshot.gameId);
    let counter = 0;
    const cards: GameCard[] = [];
    for (const {cardId, quantity} of snapshot.cards) {
      const card = catalog.findCardById(cardId);
      if (!card) continue;
      for (let i = 0; i < quantity; i++) {
        cards.push(
          GameCard.fromSnapshot({
            id: `gc${++counter}`,
            catalogCardId: card.id,
            data: {...card.data},
          }),
        );
      }
    }
    const shuffledCards = this.shuffleCards(cards);
    const gameDeck = GameDeck.create({
      gameId: snapshot.gameId,
      playerId: snapshot.playerId,
      cards: shuffledCards,
    });
    return this.gameDeckRepository.save(gameDeck);
  }
}
