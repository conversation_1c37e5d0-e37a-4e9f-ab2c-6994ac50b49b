import {InitializeMatchCommand} from './InitializeMatchCommand';
import {MatchRepository} from '@/src/server/Gaming/application/ports/MatchRepository';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {StartMulliganPhaseCommandHandler} from '@/src/server/Gaming/application/commands/Mulligan/StartMulliganPhase/StartMulliganPhaseCommandHandler';

export class InitializeMatchCommandHandler {
  private readonly matchRepository: MatchRepository;
  private readonly gameSettingsRepository: GameSettingsRepository;
  private readonly startMulliganPhaseHandler: StartMulliganPhaseCommandHandler;

  constructor(
    matchRepository: MatchRepository,
    gameSettingsRepository: GameSettingsRepository,
    startMulliganPhaseHandler: StartMulliganPhaseCommandHandler
  ) {
    this.matchRepository = matchRepository;
    this.gameSettingsRepository = gameSettingsRepository;
    this.startMulliganPhaseHandler = startMulliganPhaseHandler;
  }

  async handle(command: InitializeMatchCommand): Promise<void> {
    const match = await this.matchRepository.findById(command.matchId);
    if (!match) {
      throw new Error('Match not found');
    }

    if (match.getStatus() !== 'setup') {
      throw new Error('Match is not in setup phase');
    }

    const gameSettings = await this.gameSettingsRepository.getByGameId(match.getGameId());
    if (!gameSettings) {
      throw new Error('Game settings not found');
    }

    if (gameSettings.isMulliganEnabled()) {
      // Start mulligan phase
      await this.startMulliganPhaseHandler.handle({matchId: command.matchId});
    } else {
      // Skip mulligan and go directly to active
      match.startDirectly();
      await this.matchRepository.save(match);
    }
  }
}