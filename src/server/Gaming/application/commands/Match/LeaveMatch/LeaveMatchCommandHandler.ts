import {LeaveMatchCommand} from "./LeaveMatchCommand";
import {EventBus} from "@/src/server/Shared/application/ports/EventBus";
import {MatchAlreadyFinishedError} from "@/src/server/Gaming/domain/Match/errors/MatchAlreadyFinishedError";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {MatchNotFoundError} from "@/src/server/Gaming/domain/Match/errors/MatchNotFoundError";
export class LeaveMatchCommandHandler {
  private readonly eventBus: EventBus;
  private readonly matchRepository: MatchRepository;
  constructor(eventBus: EventBus, matchRepository: MatchRepository) {
    this.eventBus = eventBus;
    this.matchRepository = matchRepository;
  }

  async handle({matchId, userId}: LeaveMatchCommand) {
    const match = await this.matchRepository.findById(matchId);
    if (!match) {
      throw new MatchNotFoundError();
    }
    if (match.isFinished()) {
      throw new MatchAlreadyFinishedError();
    }

    const winner = match.getOpponentOf(userId);
    match.finishMatchWithWinner(winner);
    await this.matchRepository.save(match);

    await this.eventBus.dispatchMatchEvent(match.getGameId(), match.getId(), {
      type: "MatchEnded",
      payload: {
        winner,
        loser: userId,
        matchId: match.getId(),
      },
    });
  }
}
