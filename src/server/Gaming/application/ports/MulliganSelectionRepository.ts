import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';

export interface MulliganSelectionRepository {
  save(selection: MulliganSelection): Promise<string>;
  findByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection | null>;
  findByMatchIdPlayerIdAndRound(matchId: string, playerId: string, round: number): Promise<MulliganSelection | null>;
  findAllByMatchIdAndPlayerId(matchId: string, playerId: string): Promise<MulliganSelection[]>;
}