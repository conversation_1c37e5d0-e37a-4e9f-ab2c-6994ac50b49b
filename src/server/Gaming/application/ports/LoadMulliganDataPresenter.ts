export type LoadMulliganDataResult = {
  matchId: string;
  handCardIds: string[];
  handCardUrls: string[];
  finalHandCardIds: string[];
  finalHandCardUrls: string[];
  currentRound: number;
  maxRounds: number;
  hasCompletedAllRounds: boolean;
  previousSelections: Array<{
    round: number;
    selectedCardIds: string[];
    skipped: boolean;
  }>;
};

export interface LoadMulliganDataPresenter {
  display(result: LoadMulliganDataResult): void;
  displayError(error: Error): void;
}