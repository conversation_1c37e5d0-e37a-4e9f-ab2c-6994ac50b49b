import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {
  LoadMatchmakingEventsForGameQuery
} from "@/src/server/Shared/application/queries/Debug/LoadMatchmakingEventsForGameQuery";

export class LoadMatchmakingEventsForGameQueryHandler {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async handle({gameId, limit = 20}: LoadMatchmakingEventsForGameQuery) {
    return this.ctx.db
      .query("matchmakingEvents")
      .withIndex("by_gameId", (q) => q.eq("gameId", gameId as Id<"games">))
      .order('desc')
      .take(limit);
  }
}
