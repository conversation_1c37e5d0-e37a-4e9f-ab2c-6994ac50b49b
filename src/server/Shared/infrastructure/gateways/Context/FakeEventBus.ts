import {EventBus, DomainEvent} from "@/src/server/Shared/application/ports/EventBus";
export interface FakeEventBus extends EventBus {
  recorded: {type: 'match' | 'matchmaking'; gameId: string; id: string; event: DomainEvent}[];
}

export function createFakeEventBus(): FakeEventBus {
  const recorded: FakeEventBus['recorded'] = [];
  return {
    recorded,
    async dispatchMatchEvent(gameId: string, matchId: string, event: DomainEvent) {
      recorded.push({type: 'match', gameId, id: matchId, event});
    },
    async dispatchMatchmakingEvent(gameId: string, aggregateId: string, event: DomainEvent) {
      recorded.push({type: 'matchmaking', gameId, id: aggregateId, event});
    }
  };
}
