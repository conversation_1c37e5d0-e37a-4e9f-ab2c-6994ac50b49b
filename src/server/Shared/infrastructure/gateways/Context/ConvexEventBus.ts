import {MutationCtx} from "@/convex/_generated/server";
import {EventBus, DomainEvent} from "@/src/server/Shared/application/ports/EventBus";
import {dispatchEvent as dispatchMatchEvent, MatchEvent} from "@/convex/dispatchers/match";
import {dispatchEvent as dispatchMatchmakingEvent, MatchmakingEvent} from "@/convex/dispatchers/matchmaking";

export class ConvexEventBus implements EventBus {
  private readonly ctx: MutationCtx;

  constructor(ctx: MutationCtx) {
    this.ctx = ctx;
  }

  dispatchMatchEvent(gameId: string, matchId: string, event: DomainEvent) {
    return dispatchMatchEvent(this.ctx, gameId, matchId, event as MatchEvent);
  }

  dispatchMatchmakingEvent(gameId: string, aggregateId: string, event: DomainEvent) {
    return dispatchMatchmakingEvent(this.ctx, gameId, aggregateId, event as MatchmakingEvent);
  }
}
