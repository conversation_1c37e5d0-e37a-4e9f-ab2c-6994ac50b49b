import {IdentityProvider} from "@/src/server/Shared/application/ports/IdentityProvider";

export interface FakeIdentityProvider extends IdentityProvider {
    setNextValue(id: string): void;
}

export const FakeIdentityProvider = (): FakeIdentityProvider => {
    let id = "11111111-1111-1111-1111-111111111111";

    return {
        generateId(): string {
            return id;
        },
        setNextValue(nextId: string): void {
            id = nextId;
        }
    };
}