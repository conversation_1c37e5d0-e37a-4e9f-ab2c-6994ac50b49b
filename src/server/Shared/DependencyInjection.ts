import {createContainer, InjectionTokens} from "@evyweb/ioctopus";

export const DI_SYMBOLS: InjectionTokens = {
  DUMMY: 'DUMMY'
}

type DI_RETURN_TYPES = {
  DUMMY: string,
}

const container = createContainer();
container.bind(DI_SYMBOLS.DUMMY).toValue('DUMMY_VALUE');

export function inject<K extends keyof typeof DI_SYMBOLS>(
  symbol: K
): K extends keyof DI_RETURN_TYPES ? DI_RETURN_TYPES[K] : never {
  return container.get(DI_SYMBOLS[symbol]);
}

export {container};