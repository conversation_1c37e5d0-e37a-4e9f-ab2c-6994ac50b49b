import {createReducer} from '@reduxjs/toolkit';
import {
  deckLoadedEvent,
  deckLoadingFailedEvent,
  deckLoadingStartedEvent,
  deckNotFoundEvent,
} from './deckEvents';
import {Deck} from './Deck';

export interface DeckState {
  deck: Deck | null;
  status: 'loading' | 'success' | 'notFound' | 'error';
  error: string | null;
}

export const initialDeckState: DeckState = {
  deck: null,
  status: 'loading',
  error: null,
};

export const deckReducer = createReducer(initialDeckState, builder =>
  builder
    .addCase(deckLoadingStartedEvent, state => {
      state.status = 'loading';
      state.error = null;
    })
    .addCase(deckNotFoundEvent, state => {
      state.status = 'notFound';
      state.deck = null;
    })
    .addCase(deckLoadedEvent, (state, {payload}) => {
      state.status = 'success';
      state.deck = payload.deck;
    })
    .addCase(deckLoadingFailedEvent, (state, {payload}) => {
      state.status = 'error';
      state.error = payload.error;
    })
);
