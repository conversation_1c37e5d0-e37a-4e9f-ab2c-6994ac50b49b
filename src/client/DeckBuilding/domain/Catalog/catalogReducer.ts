import {createReducer} from "@reduxjs/toolkit";
import {catalogCardsLoadingStartedEvent, catalogCardsLoadedEvent, catalogCardsLoadingFailedEvent} from "./catalogEvents";
import {CatalogCard} from "@/src/client/DeckBuilding/domain/Catalog/CatalogCard";

export interface CatalogState {
  cards: Record<string, CatalogCard>;
  status: "loading" | "success" | "error";
  error: string | null;
}

export const initialCatalogState: CatalogState = {
  cards: {},
  status: "loading",
  error: null,
};

export const catalogReducer = createReducer(initialCatalogState, (builder) =>
  builder
    .addCase(catalogCardsLoadingStartedEvent, (state) => {
      state.status = "loading";
      state.error = null;
    })
    .addCase(catalogCardsLoadedEvent, (state, action) => {
      state.status = "success";
      state.cards = Object.fromEntries(action.payload.cards.map((card) => [card.id, card]));
    })
    .addCase(catalogCardsLoadingFailedEvent, (state, action) => {
      state.status = "error";
      state.error = action.payload.error;
    }),
);
