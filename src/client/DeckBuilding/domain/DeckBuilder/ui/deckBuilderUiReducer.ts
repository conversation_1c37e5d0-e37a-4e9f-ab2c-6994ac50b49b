import {createReducer} from '@reduxjs/toolkit';
import {
  deckBuilderIdleEvent,
  deckBuilderLoadingStartedEvent,
} from './deckBuilderUiEvents';

export interface DeckBuilderUiState {
  status: 'idle' | 'loading';
}

export const initialDeckBuilderUiState: DeckBuilderUiState = {
  status: 'idle',
};

export const deckBuilderUiReducer = createReducer(initialDeckBuilderUiState, builder =>
  builder
    .addCase(deckBuilderLoadingStartedEvent, state => { state.status = 'loading'; })
    .addCase(deckBuilderIdleEvent, state => { state.status = 'idle'; })
);
