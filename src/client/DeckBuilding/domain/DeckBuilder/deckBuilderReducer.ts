import {createReducer} from '@reduxjs/toolkit'
import {DeckBuilderCard} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard";
import {
  cardAddedToDeckEvent,
  cardDetailsDisplayedEvent,
  cardDetailsHiddenEvent,
  cardQuantityDecreasedEvent,
  cardQuantityIncreasedEvent,
  cardRemovedFromDeckEvent,
  deckBuilderViewSwitchedEvent,
  deckLoadedEvent,
  deckCreatedEvent,
  deckCreationStartedEvent,
  deckCreationCompletedEvent,
  deckNameSavingStartedEvent,
  deckNameSavedEvent,
  deckNameSavingFailedEvent,
  deckTagsUpdatedEvent
} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";

export interface DeckBuilderState {
  cardsInDeck: Record<string, { card: DeckBuilderCard; quantity: number }>;
  cardDetailsDisplayed: { cardImage: string; cardName: string } | null;
  view: 'catalog' | 'deck';
  name: string | null;
  deckId: string | null;
  isCreatingDeck: boolean;
  tags: string[];
  nameStatus: 'idle' | 'loading' | 'error';
  nameError: string | null;
}

export const initialDeckBuilderState: DeckBuilderState = {
  cardsInDeck: {},
  cardDetailsDisplayed: null,
  view: 'catalog',
  name: null,
  deckId: null,
  isCreatingDeck: false,
  tags: [],
  nameStatus: 'idle',
  nameError: null,
};

export const deckBuilderReducer = createReducer(initialDeckBuilderState, (builder) => {
  builder
    .addCase(cardAddedToDeckEvent, (state, {payload}) => {
      state.cardsInDeck[payload.card.id] = payload;
    })
    .addCase(cardQuantityIncreasedEvent, (state, {payload}) => {
      state.cardsInDeck[payload.id].quantity = payload.newQuantity;
    })
    .addCase(cardQuantityDecreasedEvent, (state, {payload}) => {
      state.cardsInDeck[payload.id].quantity = payload.newQuantity;
    })
    .addCase(cardRemovedFromDeckEvent, (state, {payload}) => {
      delete state.cardsInDeck[payload];
    })
    .addCase(cardDetailsDisplayedEvent, (state, {payload}) => {
      state.cardDetailsDisplayed = payload;
    })
    .addCase(cardDetailsHiddenEvent, (state) => {
      state.cardDetailsDisplayed = null;
    })
    .addCase(deckBuilderViewSwitchedEvent, (state, {payload}) => {
      state.view = payload;
    })
    .addCase(deckLoadedEvent, (state, {payload}) => {
      state.cardsInDeck = {};
      state.name = payload.name;
      state.deckId = payload.deckId;
      state.tags = payload.tags;
      payload.cards.forEach(({card, quantity}) => {
        state.cardsInDeck[card.id] = {card, quantity};
      });
    })
    .addCase(deckCreatedEvent, (state, {payload}) => {
      state.deckId = payload.deckId;
      state.name = payload.name;
      state.tags = payload.tags;
      state.isCreatingDeck = false;
    })
    .addCase(deckCreationStartedEvent, (state) => {
      state.isCreatingDeck = true;
    })
    .addCase(deckCreationCompletedEvent, (state) => {
      state.isCreatingDeck = false;
    })
    .addCase(deckNameSavingStartedEvent, state => {
      state.nameStatus = 'loading';
      state.nameError = null;
    })
    .addCase(deckNameSavedEvent, (state, {payload}) => {
      state.nameStatus = 'idle';
      state.name = payload.name;
    })
    .addCase(deckNameSavingFailedEvent, (state, {payload}) => {
      state.nameStatus = 'error';
      state.nameError = payload.error;
    })
    .addCase(deckTagsUpdatedEvent, (state, {payload}) => {
      state.tags = payload.tags;
    });
});