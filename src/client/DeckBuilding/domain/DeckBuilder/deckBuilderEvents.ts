import {createAction} from "@reduxjs/toolkit";
import {DeckBuilderCard} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard";

export const ADD_CARD_TO_DECK_EVENT_TYPE = 'deckBuilder/addCardToDeck';
export const REMOVE_CARD_FROM_DECK_EVENT_TYPE = 'deckBuilder/removeCardFromDeck';
export const SHOW_CARD_DETAILS_EVENT_TYPE = 'deckBuilder/showCardDetails';
export const HIDE_CARD_DETAILS_EVENT_TYPE = 'deckBuilder/hideCardDetails';
export const SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE = 'deckBuilder/switchView';
export const ADD_TAG_TO_DECK_EVENT_TYPE = 'deckBuilder/addTagToDeck';

export const cardAddedToDeckEvent = createAction<{
  card: DeckBuilderCard;
  quantity: number
}>(`${ADD_CARD_TO_DECK_EVENT_TYPE}/cardAddedToDeck`);

export const cardQuantityIncreasedEvent = createAction<{
  id: string;
  newQuantity: number;
}>(`${ADD_CARD_TO_DECK_EVENT_TYPE}/cardQuantityIncreased`);

export const cardQuantityDecreasedEvent = createAction<{
  id: string;
  newQuantity: number;
}>(`${REMOVE_CARD_FROM_DECK_EVENT_TYPE}/cardQuantityDecreased`);

export const cardMaxQuantityReachedEvent = createAction<string>(`${ADD_CARD_TO_DECK_EVENT_TYPE}/cardMaxQuantityReached`);

export const cardRemovedFromDeckEvent = createAction<string>(`${REMOVE_CARD_FROM_DECK_EVENT_TYPE}/cardRemovedFromDeck`);
export const cardMinQuantityReachedEvent = createAction<string>(`${REMOVE_CARD_FROM_DECK_EVENT_TYPE}/cardMinQuantityReached`);

export const cardDetailsDisplayedEvent = createAction<{
  cardName: string,
  cardImage: string,
}>(`${SHOW_CARD_DETAILS_EVENT_TYPE}/cardDetailsDisplayed`);

export const cardDetailsHiddenEvent = createAction(`${HIDE_CARD_DETAILS_EVENT_TYPE}/cardDetailsHidden`);

export const deckBuilderViewSwitchedEvent = createAction<'catalog' | 'deck'>(
  `${SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE}/viewSwitched`
);

export const deckLoadedEvent = createAction<{
  deckId: string;
  name: string;
  tags: string[];
  cards: {card: DeckBuilderCard; quantity: number}[];
}>('deckBuilder/deckLoaded');

export const deckCreatedEvent = createAction<{
  deckId: string;
  name: string;
  tags: string[];
}>('deckBuilder/deckCreated');

export const deckCreationStartedEvent = createAction('deckBuilder/deckCreationStarted');
export const deckCreationCompletedEvent = createAction('deckBuilder/deckCreationCompleted');

export const deckNameSavingStartedEvent = createAction('deckBuilder/deckNameSavingStarted');
export const deckNameSavedEvent = createAction<{name: string}>('deckBuilder/deckNameSaved');
export const deckNameSavingFailedEvent = createAction<{error: string}>('deckBuilder/deckNameSavingFailed');

export const deckTagsUpdatedEvent = createAction<{tags: string[]}>(
  `${ADD_TAG_TO_DECK_EVENT_TYPE}/tagsUpdated`
);
