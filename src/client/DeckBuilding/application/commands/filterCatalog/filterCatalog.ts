import {createAsyncThunk} from "@reduxjs/toolkit";
import {CatalogFilters} from "@/src/client/DeckBuilding/domain/Catalog/CatalogFilters";
import {FilterCatalogRequest} from "@/src/client/DeckBuilding/application/commands/filterCatalog/filterCatalogRequest";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";

const FILTER_CATALOG_EVENT_TYPE = 'catalog/filterCatalog';

export const filterCatalog = createAsyncThunk<void, FilterCatalogRequest, {
  state: RootState;
  extra: ThunkExtra;
}>(FILTER_CATALOG_EVENT_TYPE, async ({filters}, {dispatch, getState, extra: {locationService}}) => {
  const catalogFilters = CatalogFilters.fromState(getState().catalogFilters);
  const events = catalogFilters.applyFilters(filters);
  events.forEach(dispatch);
  locationService.setFilters(catalogFilters.toState().active.map(f => f.name));
});
