import {createAsyncThunk} from "@reduxjs/toolkit";
import {
  RemoveCardFromDeckRequest
} from "@/src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeckRequest";

import {REMOVE_CARD_FROM_DECK_EVENT_TYPE} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder";
import {getCatalogCardById} from "../../queries/getCatalogCardById/getCatalogCardById";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";

export const removeCardFromDeck = createAsyncThunk<void, RemoveCardFromDeckRequest, { state: RootState }>(
  REMOVE_CARD_FROM_DECK_EVENT_TYPE,
  async ({cardId}, {dispatch, getState}) => {
    const state = getState();
    const catalogCard = getCatalogCardById(state, cardId);
    const deckBuilder = DeckBuilder.fromState(state.deckBuilder);

    deckBuilder.removeCard(catalogCard);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);