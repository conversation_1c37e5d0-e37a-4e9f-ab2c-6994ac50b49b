import {createAsyncThunk} from '@reduxjs/toolkit';
import {
  catalogCardsLoadedEvent,
  catalogCardsLoadingFailedEvent,
  catalogCardsLoadingStartedEvent,
} from '@/src/client/DeckBuilding/domain/Catalog/catalogEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {
  LoadCatalogCardsByGameIdRequest
} from "@/src/client/DeckBuilding/application/commands/loadCatalogCardsByGameId/loadCatalogCardsByGameIdRequest";

export const loadCatalogCardsByGameId = createAsyncThunk<void, LoadCatalogCardsByGameIdRequest, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/loadCatalogCardsByGameId',
  async ({gameId}, {dispatch, extra: {catalogService}}) => {
    dispatch(catalogCardsLoadingStartedEvent());
    try {
      const result = await catalogService.loadCardsByGameId(gameId);
      dispatch(catalogCardsLoadedEvent({cards: result}));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      dispatch(catalogCardsLoadingFailedEvent({error: message}));
    }
  }
);

