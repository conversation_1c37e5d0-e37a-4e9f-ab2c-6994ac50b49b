import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {CreateDeckAndNavigateRequest} from './createDeckAndNavigateRequest';
import {CreateDeckAndNavigateResult} from './createDeckAndNavigateResult';

export const createDeckAndNavigate = createAsyncThunk<
  CreateDeckAndNavigateResult,
  CreateDeckAndNavigateRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'deckBuilding/createDeckAndNavigate',
  async ({gameId, name}, {extra: {deckService}}) => {
    try {
      const result = await deckService.createDeck({gameId, name, tags: [], cards: []});
      
      if (result.error) {
        return {success: false, error: result.error};
      }
      
      return {success: true, deckId: result.deckId!};
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {success: false, error: message};
    }
  }
);
