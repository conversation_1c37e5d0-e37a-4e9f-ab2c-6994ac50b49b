import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {
  deckNameSavingStartedEvent,
  deckNameSavedEvent,
  deckNameSavingFailedEvent,
} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import {UpdateDeckNameResult} from './updateDeckNameResult';
import {UpdateDeckNameRequest} from './updateDeckNameRequest';

export const updateDeckName = createAsyncThunk<void, UpdateDeckNameRequest, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/updateDeckName',
  async ({name}, {dispatch, getState, extra: {deckService}}) => {
    dispatch(deckNameSavingStartedEvent());

    const state = getState();
    const {deckId, cardsInDeck, tags} = state.deckBuilder;

    if (!deckId) {
      dispatch(deckNameSavedEvent({name}));
      return;
    }
    const cards = Object.values(cardsInDeck).map(({card, quantity}) => ({
      cardId: card.id,
      quantity,
    }));
    const result: UpdateDeckNameResult = await deckService.updateDeck({deckId, name, tags, cards});
    if (result.error) {
      dispatch(deckNameSavingFailedEvent({error: result.error}));
      return;
    }

    dispatch(deckNameSavedEvent({name}));
  }
);
