import {createAsyncThunk} from '@reduxjs/toolkit';
import {
  deckLoadedEvent,
  deckLoadingFailedEvent,
  deckLoadingStartedEvent,
  deckNotFoundEvent,
} from '@/src/client/DeckBuilding/domain/Deck/deckEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';

export const loadDeckById = createAsyncThunk<void, {deckId: string}, {state: RootState; extra: ThunkExtra}>(
  'deck/loadById',
  async ({deckId}, {dispatch, extra: {deckService}}) => {
    dispatch(deckLoadingStartedEvent());
    try {
      const result = await deckService.loadDeckById(deckId);
      if (!result) {
        dispatch(deckNotFoundEvent());
        return;
      }
      dispatch(deckLoadedEvent({deck: result}));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      dispatch(deckLoadingFailedEvent({error: message}));
    }
  }
);
