import {createAsyncThunk} from "@reduxjs/toolkit";
import {SHOW_CARD_DETAILS_EVENT_TYPE} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder";
import {ShowCardDetailsRequest} from "@/src/client/DeckBuilding/application/commands/showCardDetails/showCardDetailsRequest";
import {getCatalogCardById} from "@/src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";

export const showCardDetails = createAsyncThunk<void, ShowCardDetailsRequest, { state: RootState }>(
  SHOW_CARD_DETAILS_EVENT_TYPE,
  async ({cardId}, {dispatch, getState}) => {
    const state = getState();
    const card = getCatalogCardById(state, cardId);
    const deckBuilder = DeckBuilder.fromState(state.deckBuilder);
    deckBuilder.showCardDetails(card);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);