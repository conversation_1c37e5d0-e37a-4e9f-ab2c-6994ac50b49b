import {createAsyncThunk} from "@reduxjs/toolkit";
import {SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder";
import {SwitchDeckBuilderViewRequest} from "@/src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";

export const switchDeckBuilderView = createAsyncThunk<void, SwitchDeckBuilderViewRequest, { state: RootState; extra: ThunkExtra }>(
  SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE,
  async ({view}, {dispatch, getState, extra: {locationService}}) => {
    const deckBuilder = DeckBuilder.fromState(getState().deckBuilder);
    deckBuilder.switchView(view);
    deckBuilder.getDomainEvents().forEach(dispatch);
    locationService.setView(view);
  }
);
