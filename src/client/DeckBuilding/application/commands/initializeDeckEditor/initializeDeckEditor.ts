import {createAsyncThunk} from "@reduxjs/toolkit";
import {loadDeckIntoBuilder} from "@/src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder";
import {getCatalogCardById} from "@/src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {InitializeDeckEditorRequest} from "./initializeDeckEditorRequest";

export const initializeDeckEditor = createAsyncThunk<void, InitializeDeckEditorRequest, {state: RootState}>(
  'deckBuilder/initializeDeckEditor',
  async ({deck}, {dispatch, getState}) => {
    if (!deck) return;
    const state = getState();
    const catalogLoaded = !isCatalogLoading(state);
    const allCardsAvailable = deck.cards.every(({cardId}) => !!getCatalogCardById(state, cardId));

    if (!catalogLoaded || !allCardsAvailable) {
      return;
    }

    await dispatch(loadDeckIntoBuilder({deck}));
  }
);
