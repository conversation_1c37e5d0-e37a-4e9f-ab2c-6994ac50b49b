import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {
  deckCreatedEvent,
  deckCreationStartedEvent,
  deckCreationCompletedEvent,
} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import {AutoSaveDeckRequest} from './autoSaveDeckRequest';
import {AutoSaveDeckResult} from './autoSaveDeckResult';
import {buildEditDeckUrlWithQuery} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

export const autoSaveDeck = createAsyncThunk<
  AutoSaveDeckResult,
  AutoSaveDeckRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'deckBuilder/autoSaveDeck',
  async ({gameId, locale}, {dispatch, getState, extra: {deckService}}) => {
    const state = getState();
    const {cardsInDeck, name, deckId, isCreatingDeck, tags} = state.deckBuilder;

    const cards = Object.values(cardsInDeck).map(({card, quantity}) => ({
      cardId: card.id,
      quantity,
    }));

    if (cards.length === 0) {
      return {success: false, error: 'No cards to save'};
    }

    if (!deckId && isCreatingDeck) {
      return {success: false, error: 'Deck creation already in progress'};
    }

    try {
      if (deckId) {
        const result = await deckService.updateDeck({
          deckId,
          name: name || `deck-${crypto.randomUUID()}`,
          tags,
          cards,
        });

        if (result.error) {
          return {success: false, error: result.error};
        }

        return {success: true};
      } else {
        dispatch(deckCreationStartedEvent());

        const effectiveName = name || `deck-${crypto.randomUUID()}`;

        const result = await deckService.createDeck({
          gameId,
          name: effectiveName,
          tags,
          cards,
        });

        if (result.error) {
          dispatch(deckCreationCompletedEvent());
          return {success: false, error: result.error};
        }

        const newDeckId = result.deckId!;
        dispatch(deckCreatedEvent({deckId: newDeckId, name: effectiveName, tags}));

        const newUrl = buildEditDeckUrlWithQuery(locale, gameId, newDeckId, '');

        return {
          success: true,
          newDeckId,
          newUrl,
        };
      }
    } catch (error) {
      dispatch(deckCreationCompletedEvent());
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {success: false, error: errorMessage};
    }
  }
);
