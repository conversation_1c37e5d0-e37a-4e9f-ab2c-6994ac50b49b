import {createAsyncThunk} from "@reduxjs/toolkit";
import {filterCatalog} from "@/src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog";
import {search as searchCommand} from "@/src/client/DeckBuilding/application/commands/search/search";
import {switchDeckBuilderView} from "@/src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";
import {getAvailableFilters} from '@/src/client/DeckBuilding/application/queries/getAvailableFilters/getAvailableFilters';
import {
  deckBuilderLoadingStartedEvent,
  deckBuilderIdleEvent,
} from '@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiEvents';

export const initializeDeckBuilderFromLocation = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/initFromLocation',
  async (_, {dispatch, getState, extra: {locationService}}) => {
    if (getAvailableFilters(getState()).length === 0) {
      return;
    }
    dispatch(deckBuilderLoadingStartedEvent());
    const filters = locationService.getFilters();
    const search = locationService.getSearch();
    const view = locationService.getView();
    if (filters.length > 0) {
      await dispatch(filterCatalog({filters}));
    }
    if (search) {
      await dispatch(searchCommand({search}));
    }
    if (view !== 'catalog') {
      await dispatch(switchDeckBuilderView({view}));
    }
    dispatch(deckBuilderIdleEvent());
  }
);
