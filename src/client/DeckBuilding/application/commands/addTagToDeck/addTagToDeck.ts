import {createAsyncThunk} from '@reduxjs/toolkit';
import {DeckBuilder} from '@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder';
import {ADD_TAG_TO_DECK_EVENT_TYPE} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import {AddTagToDeckRequest} from './addTagToDeckRequest';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';

export const addTagToDeck = createAsyncThunk<void, AddTagToDeckRequest, {state: RootState}>(
  ADD_TAG_TO_DECK_EVENT_TYPE,
  async ({tag}, {dispatch, getState}) => {
    const deckBuilder = DeckBuilder.fromState(getState().deckBuilder);
    deckBuilder.addTag(tag);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);
