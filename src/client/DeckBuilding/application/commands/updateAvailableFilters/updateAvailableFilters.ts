import {availableFiltersUpdatedEvent} from "@/src/client/DeckBuilding/domain/Catalog/catalogFilterEvents";
import {
  UpdateAvailableFiltersRequest
} from "@/src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFiltersRequest";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export const updateAvailableFilters = ({filters}: UpdateAvailableFiltersRequest) => (dispatch: AppDispatch): void => {
  dispatch(availableFiltersUpdatedEvent({filters}));
};