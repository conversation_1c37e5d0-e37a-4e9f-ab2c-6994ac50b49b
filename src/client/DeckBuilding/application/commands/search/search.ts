import {createAsyncThunk} from '@reduxjs/toolkit';
import {searchUpdatedEvent} from '@/src/client/DeckBuilding/domain/Catalog/catalogSearchEvents';
import {SearchRequest} from "@/src/client/DeckBuilding/application/commands/search/searchRequest";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";

export const search = createAsyncThunk<void, SearchRequest, { state: RootState; extra: ThunkExtra }>(
  'catalog/search',
  async ({search}, {dispatch, extra: {timerService, locationService}}) =>
    await new Promise(resolve => {
      timerService.debounce(() => {
        dispatch(searchUpdatedEvent({search}));

        locationService.setSearch(search);

        resolve();
      }, 300);
    })
);
