import {RootState} from '@/src/client/Shared/store/appStore/rootState';

export const getDeckAutoSaveSignature = (state: RootState): string => {
  const {deckId, cardsInDeck, tags} = state.deckBuilder;

  const entries = Object.entries(cardsInDeck)
    .map(([id, {quantity}]) => `${id}:${quantity}`)
    .sort();

  const tagsPart = [...tags].sort().join(',');

  const base = deckId ?? 'no-deck';
  const cardsPart = entries.join('|');

  return `${base}::${cardsPart}::${tagsPart}`;
};

