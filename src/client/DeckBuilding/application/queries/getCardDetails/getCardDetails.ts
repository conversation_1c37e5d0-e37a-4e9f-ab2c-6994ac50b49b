import {createSelector} from "reselect";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";

export const getCardDetails = createSelector(
  (state: RootState) => state.deckBuilder.cardDetailsDisplayed,
  (cardDetailsDisplayed) => {
    if (!cardDetailsDisplayed) {
      return null;
    }

    return {
      cardImage: cardDetailsDisplayed.cardImage,
      cardName: cardDetailsDisplayed.cardName,
    };
  }
);