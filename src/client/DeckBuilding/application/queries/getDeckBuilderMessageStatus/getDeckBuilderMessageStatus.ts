import {createSelector} from "reselect";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {isGameSettingsLoading} from "@/src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading";


export type DeckBuilderMessageStatus =
  | 'loading'
  | 'onboarding'
  | null;

export const getDeckBuilderMessageStatus = createSelector(
  isCatalogLoading,
  isGameSettingsLoading,
  (
    catalogLoading,
    settingsLoading,
  ): DeckBuilderMessageStatus => {
    if (catalogLoading || settingsLoading) return 'loading';
    return 'onboarding';
  },
);
