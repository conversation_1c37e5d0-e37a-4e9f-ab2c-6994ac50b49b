import {Deck} from '@/src/client/DeckBuilding/domain/Deck/Deck';
import {UpdateDeckNameResult} from '@/src/client/DeckBuilding/application/commands/updateDeckName/updateDeckNameResult';

export type CreateDeckResult = {
  deckId?: string;
  error?: string;
};

export interface DeckService {
  loadDeckById(deckId: string): Promise<Deck | null>;
  updateDeck(request: {deckId: string; name: string; tags: string[]; cards: {cardId: string; quantity: number}[]}): Promise<UpdateDeckNameResult>;
  createDeck(request: {gameId: string; name: string; tags: string[]; cards: {cardId: string; quantity: number}[]}): Promise<CreateDeckResult>;
}
