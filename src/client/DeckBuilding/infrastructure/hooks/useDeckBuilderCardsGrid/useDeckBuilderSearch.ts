import {useEffect, useState} from 'react';

export const useDeckBuilderSearch = (
  search: string,
  searchCatalog: (value: string) => void,
) => {
  const [localSearch, setLocalSearchState] = useState(search);

  useEffect(() => {
    setLocalSearchState(search);
  }, [search]);

  const setLocalSearch = (value: string) => {
    setLocalSearchState(value);
    if (value !== search) {
      searchCatalog(value);
    }
  };

  return {localSearch, setLocalSearch};
};
