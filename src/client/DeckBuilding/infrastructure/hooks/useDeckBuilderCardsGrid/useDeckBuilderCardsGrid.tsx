import {useGameId} from '@/src/client/Shared/hooks/useGameId/useGameId';
import {useLocale} from '@/src/client/Shared/hooks/useLocale/useLocale';
import {useCardsByGameId} from '@/src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId';
import {useDeckBuilder} from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder';
import {useDeckName} from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckName/useDeckName';
import {useResponsiveColumnCount} from '@/src/client/Shared/hooks/useResponsiveColumnCount/useResponsiveColumnCount';
import {useDeckBuilderSearch} from './useDeckBuilderSearch';
import {useSelector} from 'react-redux';
import {getDeckCardQuantities} from '@/src/client/DeckBuilding/application/queries/getDeckCardQuantities/getDeckCardQuantities';
import {useVirtualGrid} from './useVirtualGrid';
import {DeckBuilderCardsGridComponentProps} from '@/src/client/DeckBuilding/infrastructure/components/DeckBuilderCardsGrid/DeckBuilderCardsGridComponent';


export const useDeckBuilderCardsGrid = (): DeckBuilderCardsGridComponentProps => {
  const gameId = useGameId();
  const locale = useLocale();
  const {displayedCards, isLoading} = useCardsByGameId({gameId});
  const {
    addCardToDeck,
    removeCard,
    showCardDetails,
    searchCatalog,
    search,
    view,
    switchView,
    name,
    tags,
    addTag,
  } = useDeckBuilder();
  const {saveNameChange} = useDeckName();

  const {localSearch, setLocalSearch} = useDeckBuilderSearch(search, searchCatalog);
  const quantities = useSelector(getDeckCardQuantities);

  const {columnCount, containerRef} = useResponsiveColumnCount();
  const {cardRows, virtualRows, totalSize} = useVirtualGrid(displayedCards, columnCount, containerRef);

  return {
    viewControl: {
      view,
      switchView,
      name,
      tags,
      cardCount: cardRows.flat().length,
    },
    search: {
      localSearch,
      setLocalSearch,
    },
    virtualGrid: {
      isLoading,
      cardRows,
      columnCount,
      containerRef,
      virtualRows,
      totalSize,
    },
    cardOperations: {
      locale,
      quantities,
      addCardToDeck,
      removeCard,
      showCardDetails,
    },
    deckManagement: {
      name,
      saveNameChange,
      addTag,
    },
  };
};
