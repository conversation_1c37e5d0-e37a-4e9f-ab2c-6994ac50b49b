import {useEffect, useMemo} from 'react';
import {useVirtualizer} from '@tanstack/react-virtual';
import {chunk} from '@/src/client/Shared/helpers/Chunk/chunk';
import {DeckBuilderCard} from '@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard';

const CARD_HEIGHT = 384;
const GAP = 8;

export const useVirtualGrid = (
  displayedCards: DeckBuilderCard[],
  columnCount: number,
  containerRef: React.RefObject<HTMLDivElement | null>,
) => {
  const cardRows = useMemo(
    () => chunk(displayedCards, columnCount),
    [displayedCards, columnCount],
  );

  const rowVirtualizer = useVirtualizer({
    count: cardRows.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => CARD_HEIGHT + GAP,
    overscan: 4,
    measureElement: el => el.getBoundingClientRect().height,
  });

  useEffect(() => {
    rowVirtualizer.measure();
  }, [columnCount, rowVirtualizer]);

  return {
    cardRows,
    virtualRows: rowVirtualizer.getVirtualItems(),
    totalSize: rowVirtualizer.getTotalSize(),
  };
};
