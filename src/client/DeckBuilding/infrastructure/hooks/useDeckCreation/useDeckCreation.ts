import {useCallback} from 'react';
import {useDispatch} from 'react-redux';
import {useRouter} from 'next/navigation';
import {createDeckAndNavigate} from '@/src/client/DeckBuilding/application/commands/createDeckAndNavigate/createDeckAndNavigate';
import {buildEditDeckUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';

type UseDeckCreationProps = {
  locale: string;
  gameId: string;
};

export const useDeckCreation = ({locale, gameId}: UseDeckCreationProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const createDeck = useCallback(async (name: string) => {
    try {
      const result = await dispatch(createDeckAndNavigate({gameId, name})).unwrap();
      
      if (result.success && result.deckId) {
        router.push(buildEditDeckUrl(locale, gameId, result.deckId));
        return {success: true};
      }
      
      return {success: false, error: result.error};
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {success: false, error: message};
    }
  }, [dispatch, gameId, locale, router]);

  return {createDeck};
};
