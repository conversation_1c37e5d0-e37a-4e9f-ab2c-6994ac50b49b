import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {loadDeckById} from '@/src/client/DeckBuilding/application/commands/loadDeckById/loadDeckById';
import {getDeck} from '@/src/client/DeckBuilding/application/queries/getDeck/getDeck';
import {isDeckLoading} from '@/src/client/DeckBuilding/application/queries/isDeckLoading/isDeckLoading';
import {getDeckError} from '@/src/client/DeckBuilding/application/queries/getDeckError/getDeckError';
import {isDeckNotFound} from '@/src/client/DeckBuilding/application/queries/isDeckNotFound/isDeckNotFound';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';

export function useDeckById(deckId: string) {
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    void dispatch(loadDeckById({deckId}));
  }, [dispatch, deckId]);

  return {
    deck: useSelector(getDeck),
    isLoading: useSelector(isDeckLoading),
    isNotFound: useSelector(isDeckNotFound),
    error: useSelector(getDeckError),
  };
}
