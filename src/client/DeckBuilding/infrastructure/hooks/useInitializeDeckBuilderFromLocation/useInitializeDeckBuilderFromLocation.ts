import {useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {initializeDeckBuilderFromLocation} from '@/src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation';
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export const useInitializeDeckBuilderFromLocation = () => {
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    dispatch(initializeDeckBuilderFromLocation());
  }, [dispatch]);
};
