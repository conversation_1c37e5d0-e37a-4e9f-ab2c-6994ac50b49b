import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import { AppDispatch } from '@/src/client/Shared/store/appStore/appDispatch';
import { useGameId } from '@/src/client/Shared/hooks/useGameId/useGameId';
import { useLocale } from '@/src/client/Shared/hooks/useLocale/useLocale';
import { autoSaveDeck } from '@/src/client/DeckBuilding/application/commands/autoSaveDeck/autoSaveDeck';
import { shouldAutoSave } from '@/src/client/DeckBuilding/application/queries/shouldAutoSave/shouldAutoSave';
import { getDeckAutoSaveSignature } from '@/src/client/DeckBuilding/application/queries/getDeckAutoSaveSignature/getDeckAutoSaveSignature';

export const useAutoSaveDeck = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const locale = useLocale();
  const gameId = useGameId();
  const shouldSave = useSelector(shouldAutoSave);
  const signature = useSelector(getDeckAutoSaveSignature);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialMount = useRef(true);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(async () => {
      if (!shouldSave) return;
      if (!signature) return;

      try {
        const result = await dispatch(autoSaveDeck({ gameId, locale })).unwrap();

        if (result.success && result.newUrl) {
          router.replace(result.newUrl);
        }

        if (!result.success && result.error) {
          console.error('Failed to auto-save deck:', result.error);
        }
      } catch (error) {
        console.error('Failed to auto-save deck:', error);
      }
    }, 1000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [signature, shouldSave, gameId, locale, dispatch, router]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
};
