import {useEffect} from "react";
import {useDispatch, useSelector} from "react-redux";
import {loadCatalogCardsByGameId} from "@/src/client/DeckBuilding/application/commands/loadCatalogCardsByGameId/loadCatalogCardsByGameId";
import {getCatalogError} from "@/src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {getFilteredCards} from "@/src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export function useCardsByGameId({gameId}: { gameId: string }) {
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    void dispatch(loadCatalogCardsByGameId({gameId}));
  }, [dispatch, gameId]);

  return {
    error: useSelector(getCatalogError),
    isLoading: useSelector(isCatalogLoading),
    displayedCards: useSelector(getFilteredCards)
  };
}
