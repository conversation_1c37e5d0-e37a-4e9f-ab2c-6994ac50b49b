import {useCallback} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {updateDeckName} from '@/src/client/DeckBuilding/application/commands/updateDeckName/updateDeckName';
import {getDeckName} from '@/src/client/DeckBuilding/application/queries/getDeckName/getDeckName';
import {isDeckNameSaving} from '@/src/client/DeckBuilding/application/queries/isDeckNameSaving/isDeckNameSaving';
import {getDeckNameError} from '@/src/client/DeckBuilding/application/queries/getDeckNameError/getDeckNameError';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';

export const useDeckName = () => {
  const dispatch = useDispatch<AppDispatch>();

  const saveNameChange = useCallback(
    (newName: string) => {
      if (!newName.trim()) return;
      void dispatch(updateDeckName({name: newName.trim()}));
    },
    [dispatch]
  );

  return {
    name: useSelector(getDeckName),
    isSaving: useSelector(isDeckNameSaving),
    error: useSelector(getDeckNameError),
    saveNameChange,
  };
};
