import {useDispatch, useSelector} from "react-redux";
import {useCallback} from "react";
import {addCardToDeck} from "@/src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck";
import {getTotalCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck";
import {getCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck";
import {removeCardFromDeck} from "@/src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck";
import {showCardDetails} from "@/src/client/DeckBuilding/application/commands/showCardDetails/showCardDetails";
import {getCardDetails} from "@/src/client/DeckBuilding/application/queries/getCardDetails/getCardDetails";
import {hideCardDetails} from "@/src/client/DeckBuilding/application/commands/hideCardDetails/hideCardDetails";
import {DeckBuilderCard} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard";
import {filterCatalog} from "@/src/client/DeckBuilding/application/commands/filterCatalog/filterCatalog";
import {search as searchCommand} from "@/src/client/DeckBuilding/application/commands/search/search";
import {getActiveFilters} from "@/src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters";
import {getSearchTerm} from "@/src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm";
import {Filter} from "@/src/client/DeckBuilding/domain/Catalog/Filter";
import {updateAvailableFilters} from "@/src/client/DeckBuilding/application/commands/updateAvailableFilters/updateAvailableFilters";
import {getDeckBuilderView} from "@/src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView";
import {getDeckName} from "@/src/client/DeckBuilding/application/queries/getDeckName/getDeckName";
import {getDeckTags} from "@/src/client/DeckBuilding/application/queries/getDeckTags/getDeckTags";
import {switchDeckBuilderView} from "@/src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";
import {useAutoSaveDeck} from "@/src/client/DeckBuilding/infrastructure/hooks/useAutoSaveDeck/useAutoSaveDeck";
import {addTagToDeck} from "@/src/client/DeckBuilding/application/commands/addTagToDeck/addTagToDeck";

export const useDeckBuilder = (): {
  deckCards: DeckBuilderCard[];
  totalCardsInDeck: number;
  addCardToDeck: (cardId: string) => void;
  removeCard: (cardId: string) => void;
  showCardDetails: (cardId: string) => void;
  hideCardDetails: () => void;
  cardDetails: { cardImage: string; cardName: string } | null;
  filterCatalog: (filters: string[]) => void;
  searchCatalog: (search: string) => void;
  activeFilters: string[];
  search: string;
  loadAvailableFilters: (filters: Filter[]) => void;
  view: 'catalog' | 'deck';
  name: string | null;
  tags: string[];
  switchView: (view: 'catalog' | 'deck') => void;
  addTag: (tag: string) => void;
} => {
  const dispatch = useDispatch<AppDispatch>();

  useAutoSaveDeck();

  return {
    activeFilters: useSelector(getActiveFilters),
    search: useSelector(getSearchTerm),
    deckCards: useSelector(getCardsInDeck),
    totalCardsInDeck: useSelector(getTotalCardsInDeck),
    cardDetails: useSelector(getCardDetails),
    view: useSelector(getDeckBuilderView),
    name: useSelector(getDeckName),
    tags: useSelector(getDeckTags),
    showCardDetails: useCallback((cardId: string) => dispatch(showCardDetails({cardId})), [dispatch]),
    hideCardDetails: useCallback(() => dispatch(hideCardDetails()), [dispatch]),
    addCardToDeck: useCallback((cardId: string) => dispatch(addCardToDeck({cardId})), [dispatch]),
    removeCard: useCallback((cardId: string) => dispatch(removeCardFromDeck({cardId})), [dispatch]),
    filterCatalog: useCallback((filters: string[]) => dispatch(filterCatalog({filters})), [dispatch]),
    searchCatalog: useCallback((search: string) => dispatch(searchCommand({search})), [dispatch]),
    loadAvailableFilters: useCallback((filters: Filter[]) => dispatch(updateAvailableFilters({filters})), [dispatch]),
    switchView: useCallback((view: 'catalog' | 'deck') => dispatch(switchDeckBuilderView({view})), [dispatch]),
    addTag: useCallback((tag: string) => dispatch(addTagToDeck({tag})), [dispatch]),
  };
};
