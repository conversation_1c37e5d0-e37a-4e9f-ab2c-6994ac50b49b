import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {loadGameSettingsByGameId} from '@/src/client/DeckBuilding/application/commands/loadGameSettingsByGameId/loadGameSettingsByGameId';
import {getGameSettings} from '@/src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings';
import {isGameSettingsLoading} from '@/src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading';
import {getGameSettingsError} from '@/src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError';
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export function useGameSettingsByGameId({gameId}: {gameId: string}) {
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    void dispatch(loadGameSettingsByGameId({gameId}));
  }, [dispatch, gameId]);

  return {
    settings: useSelector(getGameSettings),
    isLoading: useSelector(isGameSettingsLoading),
    error: useSelector(getGameSettingsError),
  };
}
