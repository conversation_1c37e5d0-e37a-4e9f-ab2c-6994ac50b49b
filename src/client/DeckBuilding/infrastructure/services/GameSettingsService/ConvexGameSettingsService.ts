import {GameSettingsService} from '@/src/client/DeckBuilding/application/ports/GameSettingsService';
import {GameSettings} from '@/src/client/DeckBuilding/domain/GameSettings/GameSettings';
import {fetchQuery} from 'convex/nextjs';
import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';
import {LoadGameSettingsByGameIdDto} from '@/src/client/DeckBuilding/infrastructure/dtos/LoadGameSettingsByGameIdDto';

export class ConvexGameSettingsService implements GameSettingsService {
  async loadSettingsByGameId(gameId: string): Promise<GameSettings | null> {
    const data = await fetchQuery(
      api.queries.gaming.loadGameSettingsByGameId,
      {gameId: gameId as Id<'games'>},
    );
    const result = data as LoadGameSettingsByGameIdDto;
    if (result.error) {
      throw new Error(result.error);
    }

    return result.data;
  }
}
