import {CatalogService} from '@/src/client/DeckBuilding/application/ports/CatalogService';
import {CatalogCard} from '@/src/client/DeckBuilding/domain/Catalog/CatalogCard';
import {convexClient} from '@/src/client/Shared/providers/ConvexClientProvider/ConvexClient';
import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';
import {LoadCatalogCardsByGameIdDto} from '@/src/client/DeckBuilding/infrastructure/dtos/LoadCatalogCardsByGameIdDto';

export class ConvexCatalogService implements CatalogService {
  async loadCardsByGameId(gameId: string): Promise<CatalogCard[]> {
    const data = await convexClient.query(
      api.queries.catalog.loadCatalogCardsByGameId,
      {gameId: gameId as Id<'games'>}
    );
    const result = data as LoadCatalogCardsByGameIdDto;
    if (result.error) {
      throw new Error(result.error);
    }
    return result.data!.cards;
  }
}
