import {CatalogService} from '../../../application/ports/CatalogService';
import {CatalogCard} from '@/src/client/DeckBuilding/domain/Catalog/CatalogCard';

export class FakeCatalogService implements CatalogService {
  private readonly result: CatalogCard[];
  private readonly shouldThrow: boolean;

  constructor(result: CatalogCard[] = [], shouldThrow = false) {
    this.result = result;
    this.shouldThrow = shouldThrow;
  }

  async loadCardsByGameId(): Promise<CatalogCard[]> {
    if (this.shouldThrow) {
      throw new Error('Failed to load catalog cards');
    }
    return this.result;
  }
}
