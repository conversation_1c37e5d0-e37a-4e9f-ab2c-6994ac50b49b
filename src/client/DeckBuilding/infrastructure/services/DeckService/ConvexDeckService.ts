import {DeckService, CreateDeckResult} from '@/src/client/DeckBuilding/application/ports/DeckService';
import {Deck} from '@/src/client/DeckBuilding/domain/Deck/Deck';
import {UpdateDeckNameResult} from '@/src/client/DeckBuilding/application/commands/updateDeckName/updateDeckNameResult';
import {convexClient} from '@/src/client/Shared/providers/ConvexClientProvider/ConvexClient';
import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';
import {LoadDeckByIdDto} from '@/src/client/DeckBuilding/infrastructure/dtos/LoadDeckByIdDto';

export class ConvexDeckService implements DeckService {
  async loadDeckById(deckId: string): Promise<Deck | null> {
    const data = await convexClient.query(
      api.queries.deck.loadDeckById,
      {deckId: deckId as Id<'decks'>}
    );
    const dto = data as LoadDeckByIdDto;
    if (dto.error) throw new Error(dto.error);
    return dto.data;
  }

  async updateDeck({deckId, name, tags, cards}: {deckId: string; name: string; tags: string[]; cards: {cardId: string; quantity: number}[]}): Promise<UpdateDeckNameResult> {
    try {
      await convexClient.mutation(
        api.mutations.updateDeck.endpoint,
        {deckId: deckId as Id<'decks'>, name, tags, cards}
      );
      return {error: null};
    } catch (e) {
      const message = e instanceof Error ? e.message : 'Unknown error';
      return {error: message};
    }
  }

  async createDeck({gameId, name, tags, cards}: {gameId: string; name: string; tags: string[]; cards: {cardId: string; quantity: number}[]}): Promise<CreateDeckResult> {
    try {
      const deckId = await convexClient.mutation(
        api.mutations.saveDeck.endpoint,
        {gameId: gameId as Id<'games'>, name, tags, cards}
      );
      return {deckId: deckId as string, error: undefined};
    } catch (e) {
      const message = e instanceof Error ? e.message : 'Unknown error';
      return {error: message};
    }
  }
}
