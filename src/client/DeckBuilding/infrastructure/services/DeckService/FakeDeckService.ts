import {DeckService, CreateDeckResult} from '../../../application/ports/DeckService';
import {Deck} from '@/src/client/DeckBuilding/domain/Deck/Deck';
import {UpdateDeckNameResult} from '@/src/client/DeckBuilding/application/commands/updateDeckName/updateDeckNameResult';

export class FakeDeckService implements DeckService {
  private readonly result: Deck | null;
  private readonly updateResult: UpdateDeckNameResult;
  private readonly createResult: CreateDeckResult;
  private readonly shouldThrow: boolean;

  constructor(
    result: Deck | null = null,
    updateResult: UpdateDeckNameResult = {error: null},
    createResult: CreateDeckResult = {deckId: 'fake-deck-id', error: undefined},
    shouldThrow = false
  ) {
    this.result = result;
    this.updateResult = updateResult;
    this.createResult = createResult;
    this.shouldThrow = shouldThrow;
  }

  async loadDeckById(): Promise<Deck | null> {
    if (this.shouldThrow) {
      throw new Error('Failed to load deck');
    }
    return this.result;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async updateDeck(_req: {deckId: string; name: string; tags: string[]; cards: {cardId: string; quantity: number}[]}): Promise<UpdateDeckNameResult> {
    return this.updateResult;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async createDeck(_req: {gameId: string; name: string; tags: string[]; cards: {cardId: string; quantity: number}[]}): Promise<CreateDeckResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to create deck');
    }
    return this.createResult;
  }
}
