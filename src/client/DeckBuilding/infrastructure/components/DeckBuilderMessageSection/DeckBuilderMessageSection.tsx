'use client';

import {useSelector} from 'react-redux';
import {getDeckBuilderMessageStatus} from '@/src/client/DeckBuilding/application/queries/getDeckBuilderMessageStatus/getDeckBuilderMessageStatus';
import DeckBuilderCreationOnboardingMessage from './DeckBuilderCreationOnboardingMessage';
import DeckBuilderDeckLoadingMessage from './DeckBuilderDeckLoadingMessage';

const DeckBuilderMessageSection = () => {
  const status = useSelector(getDeckBuilderMessageStatus);

  if (status === 'loading') return <DeckBuilderDeckLoadingMessage/>;
  if (status === 'onboarding') return <DeckBuilderCreationOnboardingMessage/>;

  return null;
};

export default DeckBuilderMessageSection;
