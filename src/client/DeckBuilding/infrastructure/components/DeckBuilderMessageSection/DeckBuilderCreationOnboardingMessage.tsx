import {FC} from "react";
import {Callout, Text} from "@radix-ui/themes";
import {InfoCircledIcon} from "@radix-ui/react-icons";

const DeckBuilderCreationOnboardingMessage: FC = () => {
  return (
    <Callout.Root color="iris" size="1">
      <Callout.Icon>
        <InfoCircledIcon/>
      </Callout.Icon>
      <Text size="2" align="center">Click on the plus button at the bottom right of each card to start building your
        deck.</Text>
    </Callout.Root>
  );
};

export default DeckBuilderCreationOnboardingMessage;