'use client';

import {FC, useState, useEffect} from "react";

import {Button, Dialog, Flex, Text, TextField} from '@radix-ui/themes';

type Props = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    currentName?: string;
    onSave: (name: string) => void;
    isCreation?: boolean;
}

const CreateOrRenameDeckDialog: FC<Props> = ({open, onOpenChange, currentName, onSave, isCreation = false}) => {
    const [name, setName] = useState(currentName || "");

    useEffect(() => {
        setName(currentName || "");
    }, [currentName]);

    const title = isCreation ? "Create new deck" : "Rename deck";
    const description = isCreation ? "Choose a name for your new deck." : "Choose a new name for your deck.";

    return (
        <Dialog.Root open={open} onOpenChange={onOpenChange}>
            <Dialog.Content style={{maxWidth: 450}}>
                <Dialog.Title>{title}</Dialog.Title>
                <Dialog.Description size="2" mb="4">
                    {description}
                </Dialog.Description>

                <Flex direction="column" gap="3">
                    <label>
                        <Text as="div" size="2" mb="1" weight="bold">
                            Deck name
                        </Text>
                        <TextField.Root
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="Enter deck name"
                        />
                    </label>
                </Flex>

                <Flex gap="3" mt="4" justify="end">
                    <Dialog.Close>
                        <Button variant="soft" color="gray">
                            Cancel
                        </Button>
                    </Dialog.Close>
                    <Dialog.Close>
                        <Button onClick={() => onSave(name)}>Save</Button>
                    </Dialog.Close>
                </Flex>
            </Dialog.Content>
        </Dialog.Root>
    );
};

export default CreateOrRenameDeckDialog;
