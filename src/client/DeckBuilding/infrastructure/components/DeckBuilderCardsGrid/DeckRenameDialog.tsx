'use client';

import {memo} from 'react';
import CreateOrRenameDeckDialog from "@/src/client/DeckBuilding/infrastructure/components/RenameDeckDialog/CreateOrRenameDeckDialog";
import {DeckManagementProps} from './DeckBuilderCardsGridComponent';

type DeckRenameDialogProps = {
  deckManagement: DeckManagementProps;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

const DeckRenameDialog = memo(({
  deckManagement,
  open,
  onOpenChange,
}: DeckRenameDialogProps) => {
  const {name, saveNameChange} = deckManagement;

  return (
    <CreateOrRenameDeckDialog
      open={open}
      onOpenChange={onOpenChange}
      currentName={name ?? ''}
      onSave={saveNameChange}
    />
  );
});

DeckRenameDialog.displayName = 'DeckRenameDialog';

export default DeckRenameDialog;
