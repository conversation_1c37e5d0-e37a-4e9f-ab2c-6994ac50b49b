'use client';

import {Box, Flex} from '@radix-ui/themes';
import DeckBuilderMessageSection
  from "@/src/client/DeckBuilding/infrastructure/components/DeckBuilderMessageSection/DeckBuilderMessageSection";
import {memo, useState, RefObject} from 'react';
import DeckBuilderHeader from './DeckBuilderHeader';
import DeckRenameDialog from './DeckRenameDialog';
import AddTagToDeckDialog from '../AddTagToDeckDialog/AddTagToDeckDialog';
import VirtualizedCardsGrid from './VirtualizedCardsGrid';

import {DeckBuilderCard} from '@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard';

export type ViewControlProps = {
  view: 'catalog' | 'deck';
  switchView: (view: 'catalog' | 'deck') => void;
  name: string | null;
  tags: string[];
  cardCount: number;
};

export type SearchProps = {
  localSearch: string;
  setLocalSearch: (value: string) => void;
};

export type VirtualGridProps = {
  isLoading: boolean;
  cardRows: Array<Array<DeckBuilderCard>>;
  columnCount: number;
  containerRef: RefObject<HTMLDivElement | null>;
  virtualRows: ReturnType<import('@tanstack/react-virtual').Virtualizer<HTMLDivElement, Element>['getVirtualItems']>;
  totalSize: number;
};

export type CardOperationsProps = {
  locale: string;
  quantities: Map<string, number>;
  addCardToDeck: (id: string) => void;
  removeCard: (id: string) => void;
  showCardDetails: (id: string) => void;
};

export type DeckManagementProps = {
  name: string | null;
  saveNameChange: (name: string) => void;
  addTag: (tag: string) => void;
};

export type DeckBuilderCardsGridComponentProps = {
  viewControl: ViewControlProps;
  search: SearchProps;
  virtualGrid: VirtualGridProps;
  cardOperations: CardOperationsProps;
  deckManagement: DeckManagementProps;
};

const DeckBuilderCardsGridComponent = memo(({
  viewControl,
  search,
  virtualGrid,
  cardOperations,
  deckManagement,
}: DeckBuilderCardsGridComponentProps) => {
  const [open, setOpen] = useState(false);
  const [tagOpen, setTagOpen] = useState(false);

  return (
    <Flex direction="column" gap="3" className="h-full">
      <DeckBuilderHeader
        viewControl={viewControl}
        search={search}
        onRenameClick={() => setOpen(true)}
        onAddTagClick={() => setTagOpen(true)}
      />

      <Box mr="4">
        <DeckBuilderMessageSection/>
      </Box>

      <DeckRenameDialog
        deckManagement={deckManagement}
        open={open}
        onOpenChange={setOpen}
      />
      <AddTagToDeckDialog
        open={tagOpen}
        onOpenChange={setTagOpen}
        onAddTag={deckManagement.addTag}
      />

      <VirtualizedCardsGrid
        virtualGrid={virtualGrid}
        cardOperations={cardOperations}
      />
    </Flex>
  );
});

DeckBuilderCardsGridComponent.displayName = 'DeckBuilderCardsGridComponent';

export default DeckBuilderCardsGridComponent;
