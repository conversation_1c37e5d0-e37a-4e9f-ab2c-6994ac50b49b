'use client';

import {FC, useState} from "react";
import {Box, Callout, Container, Flex, Grid, Heading, Text} from "@radix-ui/themes";
import ShiningCard from "@/src/client/Shared/components/ShiningCard/ShiningCard";
import CreateOrRenameDeckDialog from "@/src/client/DeckBuilding/infrastructure/components/RenameDeckDialog/CreateOrRenameDeckDialog";
import ShiningButton from "@/src/client/Shared/components/ShiningButton/ShiningButton";
import Image, {ImageProps} from "next/image";
import {CardDeck} from "@/src/client/Shared/components/CardDeck/CardDeck";
import Link from "next/link";
import {buildEditDeckUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";
import {InfoCircledIcon} from "@radix-ui/react-icons";
import {DeckResponse} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdResponse";
import {useDeckCreation} from "@/src/client/DeckBuilding/infrastructure/hooks/useDeckCreation/useDeckCreation";

type Props = {
    decks: DeckResponse[];
    locale: string;
    gameId: string;
};

const DeckList: FC<Props> = ({decks, locale, gameId}) => {
    const {createDeck} = useDeckCreation({locale, gameId});
    const [isCreateDeckDialogOpen, setIsCreateDeckDialogOpen] = useState(false);

    const handleCreateDeck = async (name: string) => {
        const result = await createDeck(name);
        if (result.success) {
            setIsCreateDeckDialogOpen(false);
        }
    };

    return (
        <Container p="3">
            <Flex direction="column" className="h-full w-full">
                <Flex direction="column" gap="4" mt="2">
                    <Flex justify="between" align="center">
                        <Flex direction="column">
                            <Heading size="4">Your decks</Heading>
                            <Text as="span" size="2" color="gray">
                                {decks.length} decks found
                            </Text>
                        </Flex>
                        <ShiningButton color="iris" size="2" onClick={() => setIsCreateDeckDialogOpen(true)}>
                            Create New Deck
                        </ShiningButton>
                    </Flex>

                    <Callout.Root>
                        <Callout.Icon>
                            <InfoCircledIcon/>
                        </Callout.Icon>
                        <Callout.Text>
                            Click on a deck to edit it, or use the button above to create a new one.
                        </Callout.Text>
                    </Callout.Root>

                    <Grid columns="5" gap="3">
                        {decks.map((deck) => (
                            <ShiningCard key={deck.id} disableScaling className="h-[300px]" asChild selected>
                                <Link href={buildEditDeckUrl(locale, gameId, deck.id)}>
                                    <Flex direction="column" gap="3" align="center" justify="center">
                                        <Heading size="3" style={{marginBottom: '4px'}} align="center" weight="regular">
                                            {deck.name}
                                        </Heading>
                                        <Box width="150px" mt="5" height="120px">
                                            {(() => {
                                                const elems = deck.images.map((src, index) => (
                                                    <Image key={index} src={src} alt={`Card ${index + 1}`} fill
                                                           className="object-cover rounded-lg"/>
                                                )) as [React.ReactElement<ImageProps>, React.ReactElement<ImageProps>, React.ReactElement<ImageProps>];
                                                return (
                                                    <CardDeck cover={`/game-assets/cards/${locale}/thumbnail/1.jpg`}>
                                                        {elems[0]}
                                                        {elems[1]}
                                                        {elems[2]}
                                                    </CardDeck>
                                                );
                                            })()}
                                        </Box>
                                    </Flex>
                                </Link>
                            </ShiningCard>
                        ))}
                    </Grid>
                </Flex>
            </Flex>
            <CreateOrRenameDeckDialog
                open={isCreateDeckDialogOpen}
                onOpenChange={setIsCreateDeckDialogOpen}
                onSave={handleCreateDeck}
                isCreation={true}
            />
        </Container>
    );
};

export default DeckList;
