'use client';

import {FC, memo, useState} from 'react';
import {Flex, ScrollArea, Text} from '@radix-ui/themes';
import Image from 'next/image';
import {useAutoAnimate} from '@formkit/auto-animate/react';
import DeckCardDetailsDialog
  from '@/src/client/DeckBuilding/infrastructure/components/DeckCardDetailsDialog/DeckCardDetailsDialog';
import {useDeckBuilder} from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder';
import DeckCardRow from './DeckCardRow';
import TotalCardsInDeckCounter
  from "@/src/client/DeckBuilding/infrastructure/components/TotalCardsInDeckCounter/TotalCardsInDeckCounter";

const DeckBuilderPanel: FC = () => {
  const {
    deckCards,
    addCardToDeck,
    removeCard,
    showCardDetails,
  } = useDeckBuilder();
  const [hoveredCardId, setHoveredCardId] = useState<string | null>(null);
  const [parentRef] = useAutoAnimate<HTMLDivElement>({easing: 'linear'});

  return (
    <Flex direction="column" gap="3" className="h-full">
      <TotalCardsInDeckCounter/>

      <ScrollArea type="always" scrollbars="vertical" className="h-full">
        {deckCards.length === 0 ? (
          <Flex direction="column" align="center" p="4">
            <Text size="4" color="gray" align="center" mt="4">
              Your deck is empty.
            </Text>
            <Image
              src="/logos/deck.png"
              alt="Deck loading"
              width={200}
              height={200}
            />
            <Text size="3" color="gray" align="center" my="2">
              Add cards by clicking the plus button on each card.
            </Text>
            <Image
              src="/ui/arrow-left.png"
              alt="Arrow left"
              width={60}
              height={60}
            />
          </Flex>
        ) : (
          <Flex direction="column" gap="1" pr="4" ref={parentRef}>
            {deckCards.map(card => (
              <DeckCardRow
                key={card.id}
                card={card}
                isHovered={card.id === hoveredCardId}
                setHoveredCardId={setHoveredCardId}
                addCardToDeck={addCardToDeck}
                removeCard={removeCard}
                showCardDetails={showCardDetails}
              />
            ))}
          </Flex>
        )}
      </ScrollArea>

      <DeckCardDetailsDialog/>
    </Flex>
  );
};

export default memo(DeckBuilderPanel);
