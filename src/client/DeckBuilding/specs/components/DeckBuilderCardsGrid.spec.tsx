import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {PathParamsContext, SearchParamsContext} from 'next/dist/shared/lib/hooks-client-context.shared-runtime';

vi.mock('@/src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId', () => ({
  useCardsByGameId: () => ({displayedCards: [], isLoading: false, error: null})
}));
vi.mock('@/src/client/DeckBuilding/infrastructure/hooks/useAutoSaveDeck/useAutoSaveDeck', () => ({
  useAutoSaveDeck: () => {}
}));

beforeAll(() => {
  global.ResizeObserver = class { observe() {} unobserve() {} disconnect() {} } as unknown as typeof ResizeObserver;
});

import DeckBuilderCardsGrid from '@/src/client/DeckBuilding/infrastructure/components/DeckBuilderCardsGrid/DeckBuilderCardsGrid';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {FakeLocationService} from '@/src/client/Shared/services/Location/FakeLocationService';
import {getDeckBuilderView} from '@/src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView';

describe('DeckBuilderCardsGrid', () => {
  describe('When switching view', () => {
    it('should switch to the selected view', async () => {
      // Arrange
      const locationService = new FakeLocationService();
      const store = createTestingStore({}, {locationService});

      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Act
      fireEvent.click(screen.getByRole('radio', {name: 'Deck'}));

      // Assert
      await waitFor(() => {
        expect(getDeckBuilderView(store.getState())).toBe('deck');
        expect(locationService.getView()).toBe('deck');
      });
    });
  });

  describe('When the view is deck', () => {
    it('should display the deck name', () => {
      // Arrange
      const store = createTestingStore({
        deckBuilder: {view: 'deck', name: 'Cool deck'},
      });

      // Act
      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Assert
      expect(screen.getByText('Cool deck')).toBeTruthy();
    });

    it('should display the deck tags', () => {
      // Arrange
      const store = createTestingStore({
        deckBuilder: {view: 'deck', name: 'Cool deck', tags: ['aggro', 'control']},
      });
      // Act
      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Assert
      expect(screen.getByText('aggro')).toBeTruthy();
      expect(screen.getByText('control')).toBeTruthy();
    });

    it('should open the rename dialog when clicking the edit button', () => {
      // Arrange
      const store = createTestingStore({
        deckBuilder: {view: 'deck', name: 'Cool deck'},
      });

      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Act
      fireEvent.click(screen.getByLabelText('Rename deck'));

      // Assert
      expect(screen.getByText('Rename deck')).toBeTruthy();
    });
  });
});
