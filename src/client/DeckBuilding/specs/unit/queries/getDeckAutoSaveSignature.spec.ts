import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {getDeckAutoSaveSignature} from '@/src/client/DeckBuilding/application/queries/getDeckAutoSaveSignature/getDeckAutoSaveSignature';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';
import {addCardToDeck} from '@/src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck';
import {addTagToDeck} from '@/src/client/DeckBuilding/application/commands/addTagToDeck/addTagToDeck';
import {deckLoadedEvent} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';

describe('getDeckAutoSaveSignature', () => {
  describe('When the deck content changes', () => {
    it('should change when a card is added', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore({
        catalog: {
          status: 'success',
          cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED},
        },
        deckBuilder: {
          deckId: 'deck-1',
          name: 'Deck',
          tags: [],
        },
      });

      const before = getDeckAutoSaveSignature(getState());

      // Act
      await dispatch(addCardToDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

      // Assert
      const after = getDeckAutoSaveSignature(getState());
      expect(after).not.toBe(before);
    });

    it('should change when tags change', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore({
        deckBuilder: {
          deckId: 'deck-1',
          name: 'Deck',
          tags: [],
        },
      });
      const before = getDeckAutoSaveSignature(getState());

      // Act
      dispatch(addTagToDeck({tag: 'aggro'}));

      // Assert
      const after = getDeckAutoSaveSignature(getState());
      expect(after).not.toBe(before);
    });

    it('should include the deck id so different decks differ', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore({
        catalog: {status: 'success'},
      });

      dispatch(
        deckLoadedEvent({deckId: 'A', name: 'A', tags: [], cards: []})
      );
      const sigA = getDeckAutoSaveSignature(getState());

      dispatch(
        deckLoadedEvent({deckId: 'B', name: 'B', tags: [], cards: []})
      );

      // Act
      const sigB = getDeckAutoSaveSignature(getState());

      // Assert
      expect(sigA).not.toBe(sigB);
    });
  });
});

