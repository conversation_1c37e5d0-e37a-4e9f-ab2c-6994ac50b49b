import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';
import {getDeckBuilderMessageStatus} from '@/src/client/DeckBuilding/application/queries/getDeckBuilderMessageStatus/getDeckBuilderMessageStatus';

describe('getDeckBuilderMessageStatus', () => {
  describe('When deck has cards', () => {
    it('should return onboarding', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 2,
            },
          },
        },
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('onboarding');
    });
  });



  describe('When the deck was saved', () => {
    it('should return onboarding', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
        deckBuilderUi: {status: 'idle'},
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('onboarding');
    });
  });

  describe('When the builder is idle with cards', () => {
    it('should return onboarding', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
        deckBuilderUi: {status: 'idle'},
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('onboarding');
    });
  });

  describe('When the deck is empty', () => {
    it('should return onboarding', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {},
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('onboarding');
    });
  });
});
