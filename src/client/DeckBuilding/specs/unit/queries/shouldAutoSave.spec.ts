import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {shouldAutoSave} from '@/src/client/DeckBuilding/application/queries/shouldAutoSave/shouldAutoSave';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';

describe('shouldAutoSave', () => {
  describe('When there are no cards in deck', () => {
    it('should return false', () => {
      // Arrange
      const {getState} = createTestingStore();

      // Act
      const result = shouldAutoSave(getState());

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('When there are cards and deck creation is not in progress', () => {
    it('should return true', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          isCreatingDeck: false,
        },
      });

      // Act
      const result = shouldAutoSave(getState());

      // Assert
      expect(result).toBe(true);
    });
  });

  describe('When there are cards but deck creation is in progress and no deckId', () => {
    it('should return false', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          isCreatingDeck: true,
          deckId: null,
        },
      });

      // Act
      const result = shouldAutoSave(getState());

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('When there are cards and deck creation is in progress but has deckId', () => {
    it('should return true', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          isCreatingDeck: true,
          deckId: 'deck1',
        },
      });

      // Act
      const result = shouldAutoSave(getState());

      // Assert
      expect(result).toBe(true);
    });
  });
});
