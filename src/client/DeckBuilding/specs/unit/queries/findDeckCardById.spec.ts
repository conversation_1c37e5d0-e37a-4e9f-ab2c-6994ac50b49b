import {findDeckCardById} from "@/src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById";
import {PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";
import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";

describe('findDeckCardById', () => {
  describe('When the card does not exist', () => {
    it('should not find it', () => {
      // Arrange
      const {getState} = createTestingStore();

      // Act
      const card = findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id);

      // Assert
      expect(card).toBeUndefined();
    });
  });

  describe('When the card exists', () => {
    it('should find it', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
      });

      // Act
      const card = findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id);

      // Assert
      expect(card).toEqual({
        card: PINOCCHIO_STRINGS_ATTACHED,
        quantity: 1,
      });
    });
  });
});