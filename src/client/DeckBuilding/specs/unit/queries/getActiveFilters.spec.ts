import {getActiveFilters} from "@/src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters";
import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";

describe('getActiveFilters', () => {
  it('should return the active filters', () => {
    // Arrange
    const {getState} = createTestingStore({
      catalogFilters: {
        active: [
          {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
          {id: '2', name: 'CHARACTER', text: 'Character', dataProperty: 'type', dataType: 'string', value: 'CHARACTER', order: 2},
          {id: '3', name: 'AMBER', text: 'Amber', dataProperty: 'color', dataType: 'string', value: 'AMBER', order: 3},
        ],
      },
    });

    // Act
    const activeFilters = getActiveFilters(getState());

    // Assert
    expect(activeFilters).toEqual(['INKABLE', 'CHARACTER', 'AMBER']);
  });
});