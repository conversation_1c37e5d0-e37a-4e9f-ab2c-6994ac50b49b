import {getDeckCardQuantities} from '@/src/client/DeckBuilding/application/queries/getDeckCardQuantities/getDeckCardQuantities';
import {GOOFY_GROUNDBREAKING_CHEF, PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';

describe('getDeckCardQuantities', () => {
  describe('When no cards are in the deck', () => {
    it('should return an empty map', () => {
      // Arrange
      const {getState} = createTestingStore();

      // Act
      const map = getDeckCardQuantities(getState());

      // Assert
      expect(map.size).toBe(0);
    });
  });

  describe('When cards are in the deck', () => {
    it('should map each card id to its quantity', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {
          cardsInDeck: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: {card: GOOFY_GROUNDBREAKING_CHEF, quantity: 2},
            [PINOCCHIO_STRINGS_ATTACHED.id]: {card: PINOCCHIO_STRINGS_ATTACHED, quantity: 1},
          }
        }
      });

      // Act
      const map = getDeckCardQuantities(getState());

      // Assert
      expect(map.get(GOOFY_GROUNDBREAKING_CHEF.id)).toBe(2);
      expect(map.get(PINOCCHIO_STRINGS_ATTACHED.id)).toBe(1);
    });
  });
});
