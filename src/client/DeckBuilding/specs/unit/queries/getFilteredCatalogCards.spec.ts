import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";
import {getFilteredCards} from "@/src/client/DeckBuilding/application/queries/getFilteredCards/getFilteredCards";
import {
  BOLT_SUPERDOG,
  ELSA_ICE_MAKER,
  GOOFY_GROUNDBREAKING_CHEF,
  INK_MOAT_FIRST_LINE_OF_DEFENSE,
  PINOCCHIO_STRINGS_ATTACHED
} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";

describe("getFilteredCatalogCards", () => {
  describe("When all filters are active", () => {
    it("should return all catalog cards matching at least one filter per property", () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {
          cards: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED,
            [GOOFY_GROUNDBREAKING_CHEF.id]: GOOFY_GROUNDBREAKING_CHEF,
            [ELSA_ICE_MAKER.id]: ELSA_ICE_MAKER,
            [BOLT_SUPERDOG.id]: BOLT_SUPERDOG,
            [INK_MOAT_FIRST_LINE_OF_DEFENSE.id]: INK_MOAT_FIRST_LINE_OF_DEFENSE,
          },
        },
        catalogFilters: {
          active: [
            {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
            {id: '2', name: 'CHARACTER', text: 'Character', dataProperty: 'type', dataType: 'string', value: 'CHARACTER', order: 2},
            {id: '3', name: 'AMBER', text: 'Amber', dataProperty: 'color', dataType: 'string', value: 'AMBER', order: 3},
          ],
        },
        deckBuilder: {
          view: 'catalog',
          cardsInDeck: {},
        },
      });

      // Act
      const result = getFilteredCards(getState());

      // Assert
      expect(result).toEqual([
        GOOFY_GROUNDBREAKING_CHEF,
        BOLT_SUPERDOG,
      ]);
    });
  });

  describe("When only INKABLE filter is active", () => {
    it("should return only inkable cards", () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {
          cards: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: GOOFY_GROUNDBREAKING_CHEF,
            [ELSA_ICE_MAKER.id]: ELSA_ICE_MAKER,
          },
        },
        catalogFilters: {
          active: [
            {id: '1', name: "INKABLE", text: "Inkable", dataProperty: "inkable", dataType: "boolean", value: true, order: 1}
          ],
        },
        deckBuilder: {
          view: 'catalog',
          cardsInDeck: {},
        },
      });

      // Act
      const result = getFilteredCards(getState());

      // Assert
      expect(result).toEqual([GOOFY_GROUNDBREAKING_CHEF]);
    });
  });

  describe("When only CHARACTER filter is active", () => {
    it("should return only character type cards", () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {
          cards: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED,
            [INK_MOAT_FIRST_LINE_OF_DEFENSE.id]: INK_MOAT_FIRST_LINE_OF_DEFENSE,
          },
        },
        catalogFilters: {
          active: [
            {id: '1', name: "CHARACTER", text: "Character", dataProperty: "type", dataType: "string", value: "CHARACTER", order: 1}
          ],
        },
        deckBuilder: {
          view: 'catalog',
          cardsInDeck: {},
        },
      });

      // Act
      const result = getFilteredCards(getState());

      // Assert
      expect(result).toEqual([PINOCCHIO_STRINGS_ATTACHED]);
    });
  });

  describe("When no filters match any card", () => {
    it("should return an empty array", () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {
          cards: {
            [ELSA_ICE_MAKER.id]: ELSA_ICE_MAKER,
            [INK_MOAT_FIRST_LINE_OF_DEFENSE.id]: INK_MOAT_FIRST_LINE_OF_DEFENSE,
          },
        },
        catalogFilters: {
          active: [
            {id: '1', name: "RUBY", text: "Ruby", dataProperty: "color", dataType: "string", value: "RUBY", order: 1},
            {id: '2', name: "ITEM", text: "Item", dataProperty: "type", dataType: "string", value: "ITEM", order: 2},
          ],
        },
        deckBuilder: {
          view: 'catalog',
          cardsInDeck: {},
        },
      });

      // Act
      const result = getFilteredCards(getState());

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe("When view is deck", () => {
    it("should return only filtered cards present in the deck", () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {
          cards: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: GOOFY_GROUNDBREAKING_CHEF,
            [ELSA_ICE_MAKER.id]: ELSA_ICE_MAKER,
            [BOLT_SUPERDOG.id]: BOLT_SUPERDOG,
          },
        },
        catalogFilters: {
          active: [
            {id: '1', name: "INKABLE", text: "Inkable", dataProperty: "inkable", dataType: "boolean", value: true, order: 1},
          ],
        },
        deckBuilder: {
          view: 'deck',
          cardsInDeck: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: {card: GOOFY_GROUNDBREAKING_CHEF, quantity: 2},
            [BOLT_SUPERDOG.id]: {card: BOLT_SUPERDOG, quantity: 1},
          },
        },
      });

      // Act
      const result = getFilteredCards(getState());

      // Assert
      expect(result).toEqual([
        GOOFY_GROUNDBREAKING_CHEF,
        BOLT_SUPERDOG,
      ]);
    });
  });
  describe("When a search term is entered", () => {
    it("should return only cards matching the search", () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {
          cards: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: GOOFY_GROUNDBREAKING_CHEF,
            [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED,
          },
        },
        catalogSearch: { search: "goofy" },
        deckBuilder: {
          view: "catalog",
          cardsInDeck: {},
        },
      });

      // Act
      const result = getFilteredCards(getState());

      // Assert
      expect(result).toEqual([GOOFY_GROUNDBREAKING_CHEF]);
    });
  });

  describe("When a search term is entered and view is deck", () => {
    it("should return only deck cards matching the search", () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {
          cards: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: GOOFY_GROUNDBREAKING_CHEF,
            [BOLT_SUPERDOG.id]: BOLT_SUPERDOG,
          },
        },
        catalogSearch: { search: "goofy" },
        deckBuilder: {
          view: "deck",
          cardsInDeck: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: {
              card: GOOFY_GROUNDBREAKING_CHEF,
              quantity: 1,
            },
          },
        },
      });

      // Act
      const result = getFilteredCards(getState());

      // Assert
      expect(result).toEqual([GOOFY_GROUNDBREAKING_CHEF]);
    });
  });

});
