import {renderHook, act} from '@testing-library/react'
import {useDeckBuilderSearch} from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckBuilderCardsGrid/useDeckBuilderSearch'

describe('useDeckBuilderSearch', () => {
  describe('When updating the search term', () => {
    it('should call the search command', () => {
      // Arrange
      const searchCatalog = vi.fn()
      const {result, rerender} = renderHook(({search}) => useDeckBuilderSearch(search, searchCatalog), {initialProps: {search: ''}})

      // Act
      act(() => {
        result.current.setLocalSearch('goofy')
      })

      // Assert
      expect(searchCatalog).toHaveBeenCalledWith('goofy')
      expect(result.current.localSearch).toBe('goofy')

      // Act
      rerender({search: 'mickey'})

      // Assert
      expect(result.current.localSearch).toBe('mickey')
    })
  })
})
