import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {autoSaveDeck} from '@/src/client/DeckBuilding/application/commands/autoSaveDeck/autoSaveDeck';
import {FakeDeckService} from '@/src/client/DeckBuilding/infrastructure/services/DeckService/FakeDeckService';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';

describe('autoSaveDeck', () => {
  describe('When auto-saving a deck with no cards', () => {
    it('should return failure', async () => {
      // Arrange
      const deckService = new FakeDeckService();
      const {dispatch} = createTestingStore({}, {deckService});

      // Act
      const result = await dispatch(autoSaveDeck({gameId: 'game1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No cards to save');
    });
  });

  describe('When auto-saving an existing deck', () => {
    it('should update the deck', async () => {
      // Arrange
      const deckService = new FakeDeckService(null, {error: null});
      const {dispatch} = createTestingStore({
        deckBuilder: {
          deckId: 'deck1',
          name: 'My Deck',
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 2,
            },
          },
        },
      }, {deckService});

      // Act
      const result = await dispatch(autoSaveDeck({gameId: 'game1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(true);
      expect(result.newUrl).toBeUndefined();
    });
  });

  describe('When auto-saving a new deck', () => {
    it('should create the deck and return new URL', async () => {
      // Arrange
      const deckService = new FakeDeckService(null, {error: null}, {deckId: 'new-deck-id'});
      const {dispatch} = createTestingStore({
        deckBuilder: {
          deckId: null,
          name: 'New Deck',
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
      }, {deckService});

      // Act
      const result = await dispatch(autoSaveDeck({gameId: 'game1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(true);
      expect(result.newDeckId).toBe('new-deck-id');
      expect(result.newUrl).toContain('/en/games/game1/decks/new-deck-id');
    });
  });

  describe('When deck creation is already in progress', () => {
    it('should return failure', async () => {
      // Arrange
      const deckService = new FakeDeckService();
      const {dispatch} = createTestingStore({
        deckBuilder: {
          deckId: null,
          isCreatingDeck: true,
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
      }, {deckService});

      // Act
      const result = await dispatch(autoSaveDeck({gameId: 'game1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Deck creation already in progress');
    });
  });
});
