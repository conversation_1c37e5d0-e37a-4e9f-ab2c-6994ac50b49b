import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadGameSettings} from '@/src/client/DeckBuilding/application/commands/loadGameSettings/loadGameSettings';
import {isGameSettingsLoading} from '@/src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading';
import {getGameSettingsError} from '@/src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError';
import {getGameSettings} from '@/src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings';

describe('loadGameSettings', () => {
  describe('When the request is undefined', () => {
    it('should start loading the settings', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadGameSettings(request)(dispatch);

      // Assert
      expect(isGameSettingsLoading(getState())).toBe(true);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadGameSettings(request)(dispatch);

      // Assert
      expect(getGameSettingsError(getState())).toBeNull();
    });
  });

  describe('When the request has an error', () => {
    it('should return the error ', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadGameSettings(request)(dispatch);

      // Assert
      expect(getGameSettingsError(getState())).toBe('An error occurred');
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadGameSettings(request)(dispatch);

      // Assert
      expect(isGameSettingsLoading(getState())).toBe(false);
    });
  });

  describe('When the request is successful', () => {
    const settings = {maxCardsInDeck: 60};

    it('should store the settings', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: settings};

      // Act
      loadGameSettings(request)(dispatch);

      // Assert
      expect(getGameSettings(getState())).toEqual(settings);
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: settings};

      // Act
      loadGameSettings(request)(dispatch);

      // Assert
      expect(isGameSettingsLoading(getState())).toBe(false);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: settings};

      // Act
      loadGameSettings(request)(dispatch);

      // Assert
      expect(getGameSettingsError(getState())).toBeNull();
    });
  });
});
