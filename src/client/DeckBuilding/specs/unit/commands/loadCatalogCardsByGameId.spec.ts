import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadCatalogCardsByGameId} from '@/src/client/DeckBuilding/application/commands/loadCatalogCardsByGameId/loadCatalogCardsByGameId';
import {FakeCatalogService} from '@/src/client/DeckBuilding/infrastructure/services/CatalogService/FakeCatalogService';
import {getCatalogCards} from '@/src/client/DeckBuilding/application/queries/getCatalogCards/getCatalogCards';
import {getCatalogError} from '@/src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';

describe('loadCatalogCardsByGameId', () => {
  describe('When the service returns cards', () => {
    it('should store the cards', async () => {
      // Arrange
      const catalogService = new FakeCatalogService([PINOCCHIO_STRINGS_ATTACHED]);
      const {dispatch, getState} = createTestingStore({}, {catalogService});

      // Act
      await dispatch(loadCatalogCardsByGameId({gameId: 'game'}));

      // Assert
      expect(getCatalogCards(getState())).toEqual([PINOCCHIO_STRINGS_ATTACHED]);
    });
  });

  describe('When dispatching the command', () => {
    it('should set the catalog status to loading while waiting', async () => {
      // Arrange
      const catalogService = new FakeCatalogService([PINOCCHIO_STRINGS_ATTACHED]);
      const {dispatch, getState} = createTestingStore({}, {catalogService});

      // Act
      const promise = dispatch(loadCatalogCardsByGameId({gameId: 'game'}));

      // Assert
      expect(getState().catalog.status).toBe('loading');
      await promise;
    });
  });

  describe('When the service returns an error', () => {
    it('should store the error', async () => {
      // Arrange
      const catalogService = new FakeCatalogService([], true);
      const {dispatch, getState} = createTestingStore({}, {catalogService});

      // Act
      await dispatch(loadCatalogCardsByGameId({gameId: 'game'}));

      // Assert
      expect(getCatalogError(getState())).toBe('Failed to load catalog cards');
    });
  });

  describe('When the service returns no cards', () => {
    it('should not store the cards', async () => {
      // Arrange
      const catalogService = new FakeCatalogService([]);
      const {dispatch, getState} = createTestingStore({}, {catalogService});

      // Act
      await dispatch(loadCatalogCardsByGameId({gameId: 'game'}));

      // Assert
      expect(getCatalogCards(getState())).toEqual([]);
    });
  });

});
