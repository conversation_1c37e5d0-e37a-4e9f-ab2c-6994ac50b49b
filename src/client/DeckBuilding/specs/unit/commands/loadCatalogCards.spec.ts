import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";
import {loadCatalogCards} from "../../../application/commands/loadCatalogCards/loadCatalogCards";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {GOOFY_GROUNDBREAKING_CHEF, PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";
import {getCatalogError} from "@/src/client/DeckBuilding/application/queries/getCatalogError/getCatalogError";
import {getCatalogCards} from "@/src/client/DeckBuilding/application/queries/getCatalogCards/getCatalogCards";

describe('loadCatalogCards', () => {
  describe('When the request is undefined', () => {
    it('should start loading the catalog', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(isCatalogLoading(getState())).toBe(true);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogError(getState())).toBeNull();
    });
  });

  describe('When the request has an error', () => {
    it('should return the error ', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogError(getState())).toBe('An error occurred');
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(isCatalogLoading(getState())).toBe(false);
    });
  });

  describe('When the request is successful', () => {
    const cards = [PINOCCHIO_STRINGS_ATTACHED, GOOFY_GROUNDBREAKING_CHEF];

    it('should retrieve the cards', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {cards}};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogCards(getState())).toEqual([
        PINOCCHIO_STRINGS_ATTACHED,
        GOOFY_GROUNDBREAKING_CHEF,
      ]);
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {cards}};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(isCatalogLoading(getState())).toBe(false);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {cards}};

      // Act
      loadCatalogCards(request)(dispatch);

      // Assert
      expect(getCatalogError(getState())).toBeNull();
    });
  });
});