import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";
import {search} from "@/src/client/DeckBuilding/application/commands/search/search";
import {getSearchTerm} from "@/src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm";
import {FakeLocationService} from "@/src/client/Shared/services/Location/FakeLocationService";
import {RealTimerService} from "@/src/client/Shared/services/Timer/RealTimerService";

describe('search', () => {
  describe('When a search term is provided', () => {
    it('should update the location with the search term', async () => {
      // Arrange
      vi.useFakeTimers();
      const locationService = new FakeLocationService();
      const timerService = new RealTimerService();
      const {dispatch, getState} = createTestingStore({}, {locationService, timerService});

      // Act
      const promise = dispatch(search({search: 'goofy'}));
      vi.advanceTimersByTime(300);
      await promise;

      // Assert
      expect(getSearchTerm(getState())).toBe('goofy');
      expect(locationService.getSearch()).toBe('goofy');
      vi.useRealTimers();
    });
  });
});
