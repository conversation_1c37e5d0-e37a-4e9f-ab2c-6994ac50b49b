import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {addTagToDeck} from '@/src/client/DeckBuilding/application/commands/addTagToDeck/addTagToDeck';
import {getDeckTags} from '@/src/client/DeckBuilding/application/queries/getDeckTags/getDeckTags';

describe('addTagToDeck', () => {
  describe('When adding a new tag', () => {
    it('should add the tag to the deck', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();

      // Act
      await dispatch(addTagToDeck({tag: 'aggro'}));

      // Assert
      expect(getDeckTags(getState())).toEqual(['aggro']);
    });
  });

  describe('When tags already exist', () => {
    it('should append the tag to the existing ones', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore({deckBuilder: {tags: ['aggro']}});

      // Act
      await dispatch(addTagToDeck({tag: 'midrange'}));

      // Assert
      expect(getDeckTags(getState())).toEqual(['aggro', 'midrange']);
    });
  });
});
