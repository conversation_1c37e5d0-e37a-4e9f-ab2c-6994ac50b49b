import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadDeckById} from '@/src/client/DeckBuilding/application/commands/loadDeckById/loadDeckById';
import {FakeDeckService} from '@/src/client/DeckBuilding/infrastructure/services/DeckService/FakeDeckService';
import {getDeck} from '@/src/client/DeckBuilding/application/queries/getDeck/getDeck';
import {getDeckError} from '@/src/client/DeckBuilding/application/queries/getDeckError/getDeckError';
import {isDeckLoading} from '@/src/client/DeckBuilding/application/queries/isDeckLoading/isDeckLoading';
import {isDeckNotFound} from '@/src/client/DeckBuilding/application/queries/isDeckNotFound/isDeckNotFound';

const deck = {id: 'd1', gameId: 'g1', playerId: 'p1', name: 'Deck', tags: [], cards: []};

describe('loadDeckById', () => {
  describe('When the service returns a deck', () => {
    it('should store the deck', async () => {
      // Arrange
      const deckService = new FakeDeckService(deck);
      const {dispatch, getState} = createTestingStore({}, {deckService});

      // Act
      await dispatch(loadDeckById({deckId: 'd1'}));

      // Assert
      expect(getDeck(getState())).toEqual(deck);
    });
  });

  describe('When dispatching the command', () => {
    it('should set the deck status to loading while waiting', async () => {
      // Arrange
      const deckService = new FakeDeckService(deck);
      const {dispatch, getState} = createTestingStore({}, {deckService});

      // Act
      const promise = dispatch(loadDeckById({deckId: 'd1'}));

      // Assert
      expect(isDeckLoading(getState())).toBe(true);
      await promise;
    });
  });

  describe('When the service returns an error', () => {
    it('should store the error', async () => {
      // Arrange
      const deckService = new FakeDeckService(null, {error: null}, {deckId: 'fake-deck-id'}, true);
      const {dispatch, getState} = createTestingStore({}, {deckService});

      // Act
      await dispatch(loadDeckById({deckId: 'd1'}));

      // Assert
      expect(getDeckError(getState())).toBe('Failed to load deck');
    });
  });

  describe('When the deck is not found', () => {
    it('should set the status to notFound', async () => {
      // Arrange
      const deckService = new FakeDeckService();
      const {dispatch, getState} = createTestingStore({}, {deckService});

      // Act
      await dispatch(loadDeckById({deckId: 'd1'}));

      // Assert
      expect(isDeckNotFound(getState())).toBe(true);
    });
  });
});

