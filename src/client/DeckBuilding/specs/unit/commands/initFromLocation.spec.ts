import {initializeDeckBuilderFromLocation} from "@/src/client/DeckBuilding/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation";
import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";
import {getActiveFilters} from "@/src/client/DeckBuilding/application/queries/getActiveFilters/getActiveFilters";
import {FakeLocationService} from "@/src/client/Shared/services/Location/FakeLocationService";
import {getSearchTerm} from "@/src/client/DeckBuilding/application/queries/getSearchTerm/getSearchTerm";
import {getDeckBuilderView} from "@/src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView";
import {getDeckBuilderUiStatus} from '@/src/client/DeckBuilding/application/queries/getDeckBuilderUiStatus/getDeckBuilderUiStatus';

describe('initializeDeckBuilderFromLocation', () => {
  describe('When the location contains filters', () => {
    it('should load the deck builder filters from the location', async () => {
      // Arrange
      const locationService = new FakeLocationService({filters: ['INKABLE']});
      const {dispatch, getState} = createTestingStore({
        catalogFilters: {
          available: [
            {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
          ]
        }
      }, {locationService});

      // Act
      await dispatch(initializeDeckBuilderFromLocation());

      // Assert
      expect(getActiveFilters(getState())).toEqual(['INKABLE']);
    });
  });
  describe('When the location contains a search term', () => {
    it('should load the search term from the location', async () => {
      // Arrange
      const locationService = new FakeLocationService({search: 'goofy'});
      const {dispatch, getState} = createTestingStore({
        catalogFilters: {
          available: [
            {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
          ],
        },
      }, {locationService});

      // Act
      await dispatch(initializeDeckBuilderFromLocation());

      // Assert
      expect(getSearchTerm(getState())).toBe('goofy');
    });
  });

  describe('When the location contains a view', () => {
    it('should load the view from the location', async () => {
      // Arrange
      const locationService = new FakeLocationService({view: 'deck'});
      const {dispatch, getState} = createTestingStore({
        catalogFilters: {
          available: [
            {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
          ],
        },
      }, {locationService});

      // Act
      await dispatch(initializeDeckBuilderFromLocation());

      // Assert
      expect(getDeckBuilderView(getState())).toBe('deck');
    });
  });

  describe('When available filters are not loaded', () => {
    it('should ignore the location settings', async () => {
      // Arrange
      const locationService = new FakeLocationService({view: 'deck', search: 'goofy', filters: ['INKABLE']});
      const {dispatch, getState} = createTestingStore({}, {locationService});

      // Act
      await dispatch(initializeDeckBuilderFromLocation());

      // Assert
      expect(getActiveFilters(getState())).toEqual([]);
      expect(getSearchTerm(getState())).toBe('');
      expect(getDeckBuilderView(getState())).toBe('catalog');
      expect(getDeckBuilderUiStatus(getState())).toBe('idle');
    });
  });
});
