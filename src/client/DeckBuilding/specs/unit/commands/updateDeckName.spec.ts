import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {updateDeckName} from '@/src/client/DeckBuilding/application/commands/updateDeckName/updateDeckName';
import {FakeDeckService} from '@/src/client/DeckBuilding/infrastructure/services/DeckService/FakeDeckService';
import {getDeckName} from '@/src/client/DeckBuilding/application/queries/getDeckName/getDeckName';
import {isDeckNameSaving} from '@/src/client/DeckBuilding/application/queries/isDeckNameSaving/isDeckNameSaving';
import {getDeckNameError} from '@/src/client/DeckBuilding/application/queries/getDeckNameError/getDeckNameError';

describe('updateDeckName', () => {
  describe('When the deck does not exist yet', () => {
    it('should store the name locally', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();

      // Act
      await dispatch(updateDeckName({name: 'New'}));

      // Assert
      expect(getDeckName(getState())).toBe('New');
    });
  });

  describe('When dispatching the command', () => {
    it('should set the status to loading while waiting', async () => {
      // Arrange
      const deckService = new FakeDeckService(undefined);
      const {dispatch, getState} = createTestingStore({deckBuilder: {deckId: 'd1'}}, {deckService});

      // Act
      const promise = dispatch(updateDeckName({name: 'New'}));

      // Assert
      expect(isDeckNameSaving(getState())).toBe(true);
      await promise;
    });
  });

  describe('When the service returns an error', () => {
    it('should store the error', async () => {
      // Arrange
      const deckService = new FakeDeckService(undefined, {error: 'oops'});
      const {dispatch, getState} = createTestingStore({deckBuilder: {deckId: 'd1'}}, {deckService});

      // Act
      await dispatch(updateDeckName({name: 'New'}));

      // Assert
      expect(getDeckNameError(getState())).toBe('oops');
    });
  });
});
