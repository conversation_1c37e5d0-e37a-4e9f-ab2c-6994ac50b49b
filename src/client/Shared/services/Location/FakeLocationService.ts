import {LocationService} from "@/src/client/Shared/services/Location/LocationService";

export class FakeLocationService implements LocationService {
  private filters: string[];
  private search: string;
  private view: 'catalog' | 'deck';

  constructor(init: Partial<{ filters: string[]; search: string; view: 'catalog' | 'deck' }> = {}) {
    this.filters = init.filters ?? [];
    this.search = init.search ?? '';
    this.view = init.view ?? 'catalog';
  }

  getFilters(): string[] {
    return this.filters;
  }

  setFilters(filters: string[]): void {
    this.filters = filters;
  }

  getSearch(): string {
    return this.search;
  }

  setSearch(search: string): void {
    this.search = search;
  }

  getView(): 'catalog' | 'deck' {
    return this.view;
  }

  setView(view: 'catalog' | 'deck'): void {
    this.view = view;
  }
}
