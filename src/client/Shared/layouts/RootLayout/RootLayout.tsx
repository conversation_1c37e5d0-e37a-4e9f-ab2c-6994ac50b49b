import {FC, PropsWithChildren} from "react";
import {Theme} from "@radix-ui/themes";
import {ConvexClientProvider} from "@/src/client/Shared/providers/ConvexClientProvider/ConvexClientProvider";
import {ToastProvider} from "@/src/client/Shared/providers/ToastProvider/ToastProvider";
import {ConvexAuthNextjsServerProvider} from "@convex-dev/auth/nextjs/server";
import Head from "next/head";

type Props = {
  font: string;
};

const RootLayout: FC<PropsWithChildren<Props>> = ({children, font}) => (
  <ConvexAuthNextjsServerProvider>
    <html lang="en">
    <Head>
      {/* eslint-disable-next-line @next/next/no-sync-scripts */}
      <script src="https://unpkg.com/react-scan/dist/auto.global.js"/>
    </Head>
    <body className={`${font} antialiased`}>

    <Theme
      appearance="dark"
      accentColor="indigo"
      radius="medium"
      hasBackground={true}
      panelBackground="translucent"
      scaling="95%"
    >
      <ConvexClientProvider>
        <ToastProvider>
          {children}
        </ToastProvider>
      </ConvexClientProvider>
    </Theme>

    </body>
    </html>
  </ConvexAuthNextjsServerProvider>
);

export default RootLayout;