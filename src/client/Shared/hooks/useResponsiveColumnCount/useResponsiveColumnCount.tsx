import {useEffect, useRef, useState} from "react";

export function useResponsiveColumnCount() {
  const [columnCount, setColumnCount] = useState(1);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const update = () => {
      const width = window.innerWidth;
      if (width >= 1280) setColumnCount(5); // xl
      else if (width >= 1024) setColumnCount(3); // lg
      else if (width >= 768) setColumnCount(2); // md
      else setColumnCount(1); // mobile
    };

    update();
    window.addEventListener('resize', update);
    return () => window.removeEventListener('resize', update);
  }, []);

  return {columnCount, containerRef};
}