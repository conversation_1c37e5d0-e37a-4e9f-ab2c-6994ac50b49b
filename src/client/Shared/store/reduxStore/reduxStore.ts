import {configureStore} from "@reduxjs/toolkit";
import {RealLocationService} from "@/src/client/Shared/services/Location/RealLocationService";
import {ConvexCatalogService} from "@/src/client/DeckBuilding/infrastructure/services/CatalogService/ConvexCatalogService";
import {ConvexDeckService} from "@/src/client/DeckBuilding/infrastructure/services/DeckService/ConvexDeckService";
import {RealTimerService} from "@/src/client/Shared/services/Timer/RealTimerService";
import {ConvexGameSettingsService} from "@/src/client/DeckBuilding/infrastructure/services/GameSettingsService/ConvexGameSettingsService";
import {ConvexMatchMakingService} from "@/src/client/Gaming/infrastructure/services/MatchMaking/ConvexMatchMakingService";
import {ConvexDeckListService} from "@/src/client/Gaming/infrastructure/services/DeckList/ConvexDeckListService";
import {ConvexMatchService} from "@/src/client/Gaming/infrastructure/services/Match/ConvexMatchService";
import {ConvexGameService} from "@/src/client/Gaming/infrastructure/services/Game/ConvexGameService";
import {ConvexLoadMulliganDataService} from "@/src/client/Gaming/infrastructure/services/LoadMulliganData/ConvexLoadMulliganDataService";
import {ConvexSubmitMulliganSelectionService} from "@/src/client/Gaming/infrastructure/services/SubmitMulliganSelection/ConvexSubmitMulliganSelectionService";

import {rootReducers} from "@/src/client/Shared/store/appStore/rootReducers";

export const locationService = new RealLocationService();
export const catalogService = new ConvexCatalogService();
export const deckService = new ConvexDeckService();
export const timerService = new RealTimerService();
export const gameSettingsService = new ConvexGameSettingsService();
export const matchMakingService = new ConvexMatchMakingService();
export const deckListService = new ConvexDeckListService();
export const matchService = new ConvexMatchService();
export const gameService = new ConvexGameService();
export const loadMulliganDataService = new ConvexLoadMulliganDataService();
export const submitMulliganSelectionService = new ConvexSubmitMulliganSelectionService();

export const reduxStore = configureStore({
  reducer: rootReducers,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      thunk: {extraArgument: {locationService, catalogService, deckService, timerService, gameSettingsService, matchMakingService, deckListService, matchService, gameService, loadMulliganDataService, submitMulliganSelectionService}},
      serializableCheck: {
        ignoredActions: ['catalog/cardsLoaded'],
      },
    }),
});

