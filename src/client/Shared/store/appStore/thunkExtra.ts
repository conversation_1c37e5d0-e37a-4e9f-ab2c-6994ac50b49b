import type {LocationService} from "@/src/client/Shared/services/Location/LocationService";
import type {CatalogService} from "@/src/client/DeckBuilding/application/ports/CatalogService";
import type {DeckService} from "@/src/client/DeckBuilding/application/ports/DeckService";
import type {TimerService} from "@/src/client/Shared/services/Timer/TimerService";
import type {GameSettingsService} from "@/src/client/DeckBuilding/application/ports/GameSettingsService";
import type {MatchMakingService} from "@/src/client/Gaming/application/ports/MatchMakingService";
import type {DeckListService} from "@/src/client/Gaming/application/ports/DeckListService";
import type {MatchService} from "@/src/client/Gaming/application/ports/MatchService";
import type {GameService} from "@/src/client/Gaming/application/ports/GameService";
import type {LoadMulliganDataService} from "@/src/client/Gaming/application/ports/LoadMulliganDataService";
import type {SubmitMulliganSelectionService} from "@/src/client/Gaming/application/ports/SubmitMulliganSelectionService";

export type ThunkExtra = {
  locationService: LocationService;
  catalogService: CatalogService;
  deckService: DeckService;
  timerService: TimerService;
  gameSettingsService: GameSettingsService;
  matchMakingService: MatchMakingService;
  deckListService: DeckListService;
  matchService: MatchService;
  gameService: GameService;
  loadMulliganDataService: LoadMulliganDataService;
  submitMulliganSelectionService: SubmitMulliganSelectionService;
};
