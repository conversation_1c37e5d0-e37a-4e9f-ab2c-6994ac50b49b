import {initialDeckBuilderState} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer";
import {initialDeckState} from "@/src/client/DeckBuilding/domain/Deck/deckReducer";
import {initialCatalogState} from "@/src/client/DeckBuilding/domain/Catalog/catalogReducer";
import {initialCatalogState as initialCatalogManagementState} from '@/src/client/CatalogManagement/domain/Catalog/catalogReducer';
import {initialCatalogFiltersState} from "@/src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer";
import {initialCatalogSearchState} from "@/src/client/DeckBuilding/domain/Catalog/catalogSearchReducer";
import {initialGameSettingsState} from "@/src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer";
import {initialGameManagementState} from '@/src/client/GameManagement/domain/Game/gameReducer';
import {initialDeckBuilderUiState} from "@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiReducer";
import {initialPlayerHandsState} from '@/src/client/Gaming/domain/PlayerHands/playerHandsReducer';
import {initialCardZoomState} from '@/src/client/Gaming/domain/CardZoom/cardZoomReducer';
import {initialMatchMakingState} from '@/src/client/Gaming/domain/MatchMaking/matchMakingReducer';
import {initialDeckSelectionState} from '@/src/client/Gaming/domain/DeckSelection/deckSelectionReducer';
import {initialMatchState} from '@/src/client/Gaming/domain/Match/matchReducer';
import {initialGameState} from '@/src/client/Gaming/domain/Game/gameReducer';
import {initialAuthState} from '@/src/client/Authentication/domain/Auth/authReducer';
import {initialMulliganState} from '@/src/client/Gaming/domain/Mulligan/mulliganReducer';
import {initialGameBoardState} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

export const rootInitialState = {
  deck: initialDeckState,
  deckBuilder: initialDeckBuilderState,
  deckBuilderUi: initialDeckBuilderUiState,
  catalog: initialCatalogState,
  catalogFilters: initialCatalogFiltersState,
  catalogSearch: initialCatalogSearchState,
  gameSettings: initialGameSettingsState,
  gameManagement: initialGameManagementState,
  catalogManagement: initialCatalogManagementState,
  playerHands: initialPlayerHandsState,
  cardZoom: initialCardZoomState,
  matchMaking: initialMatchMakingState,
  deckSelection: initialDeckSelectionState,
  match: initialMatchState,
  game: initialGameState,
  auth: initialAuthState,
  mulligan: initialMulliganState,
  gameBoard: initialGameBoardState,
};
