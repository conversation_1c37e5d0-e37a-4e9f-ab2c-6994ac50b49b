import {DeckBuilderState} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer";
import {DeckState} from "@/src/client/DeckBuilding/domain/Deck/deckReducer";
import {CatalogState} from "@/src/client/DeckBuilding/domain/Catalog/catalogReducer";
import {CatalogState as CatalogManagementState} from '@/src/client/CatalogManagement/domain/Catalog/catalogReducer';
import {CatalogFilterState} from "@/src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer";
import {CatalogSearchState} from "@/src/client/DeckBuilding/domain/Catalog/catalogSearchReducer";
import {GameSettingsState} from "@/src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer";
import {GameManagementState} from '@/src/client/GameManagement/domain/Game/gameReducer';
import {DeckBuilderUiState} from "@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiReducer";
import {PlayerHandsState} from '@/src/client/Gaming/domain/PlayerHands/playerHandsReducer';
import {CardZoomState} from '@/src/client/Gaming/domain/CardZoom/cardZoomReducer';
import {MatchMakingState} from '@/src/client/Gaming/domain/MatchMaking/matchMakingReducer';
import {DeckSelectionState} from '@/src/client/Gaming/domain/DeckSelection/deckSelectionReducer';
import {MatchState} from '@/src/client/Gaming/domain/Match/matchReducer';
import {GameState} from '@/src/client/Gaming/domain/Game/gameReducer';
import {AuthState} from '@/src/client/Authentication/domain/Auth/authReducer';
import {MulliganState} from '@/src/client/Gaming/domain/Mulligan/mulliganReducer';
import {GameBoardState} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

export interface RootState {
  deck: DeckState;
  deckBuilder: DeckBuilderState;
  deckBuilderUi: DeckBuilderUiState;
  catalog: CatalogState;
  catalogFilters: CatalogFilterState;
  catalogSearch: CatalogSearchState;
  gameSettings: GameSettingsState;
  gameManagement: GameManagementState;
  catalogManagement: CatalogManagementState;
  playerHands: PlayerHandsState;
  cardZoom: CardZoomState;
  matchMaking: MatchMakingState;
  deckSelection: DeckSelectionState;
  match: MatchState;
  game: GameState;
  auth: AuthState;
  mulligan: MulliganState;
  gameBoard: GameBoardState;
}
