import {deckBuilderReducer} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer";
import {deckReducer} from "@/src/client/DeckBuilding/domain/Deck/deckReducer";
import {catalogReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogReducer";
import {catalogReducer as catalogManagementReducer} from '@/src/client/CatalogManagement/domain/Catalog/catalogReducer';
import {catalogFiltersReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer";
import {catalogSearchReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogSearchReducer";
import {gameSettingsReducer} from "@/src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer";
import {gameManagementReducer} from '@/src/client/GameManagement/domain/Game/gameReducer';
import {deckBuilderUiReducer} from "@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiReducer";
import {playerHandsReducer} from '@/src/client/Gaming/domain/PlayerHands/playerHandsReducer';
import {cardZoomReducer} from '@/src/client/Gaming/domain/CardZoom/cardZoomReducer';
import {matchMakingReducer} from '@/src/client/Gaming/domain/MatchMaking/matchMakingReducer';
import {deckSelectionReducer} from '@/src/client/Gaming/domain/DeckSelection/deckSelectionReducer';
import {matchReducer} from '@/src/client/Gaming/domain/Match/matchReducer';
import {gameReducer} from '@/src/client/Gaming/domain/Game/gameReducer';
import {authReducer} from '@/src/client/Authentication/domain/Auth/authReducer';
import {mulliganReducer} from '@/src/client/Gaming/domain/Mulligan/mulliganReducer';
import {gameBoardReducer} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

export const rootReducers = {
  deck: deckReducer,
  deckBuilder: deckBuilderReducer,
  deckBuilderUi: deckBuilderUiReducer,
  catalog: catalogReducer,
  catalogFilters: catalogFiltersReducer,
  catalogSearch: catalogSearchReducer,
  gameSettings: gameSettingsReducer,
  gameManagement: gameManagementReducer,
  catalogManagement: catalogManagementReducer,
  playerHands: playerHandsReducer,
  cardZoom: cardZoomReducer,
  matchMaking: matchMakingReducer,
  deckSelection: deckSelectionReducer,
  match: matchReducer,
  game: gameReducer,
  auth: authReducer,
  mulligan: mulliganReducer,
  gameBoard: gameBoardReducer,
};