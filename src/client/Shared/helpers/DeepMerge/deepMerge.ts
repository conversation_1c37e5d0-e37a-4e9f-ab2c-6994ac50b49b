import {isPlainObject} from "@/src/client/Shared/helpers/IsPlainObject/isPlainObject";

export function deepMerge<T extends object>(
  target: T,
  source: Partial<T>
): T {
  const result = {...target};

  for (const key of Object.keys(source) as (keyof T)[]) {
    const sourceValue = source[key];
    const targetValue = target[key];

    if (
      isPlainObject(sourceValue) &&
      isPlainObject(targetValue)
    ) {
      result[key] = deepMerge(
        targetValue,
        sourceValue
      ) as T[typeof key];
    } else if (sourceValue !== undefined) {
      result[key] = sourceValue as T[typeof key];
    }
  }

  return result;
}