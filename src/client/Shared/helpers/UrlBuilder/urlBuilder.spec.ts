import {
  buildEditDeckUrl,
  buildEditDeckUrlWithQuery,
  buildDeckBuilderUrl,
  buildDecksUrl,
  buildWaitingForOpponentUrl,
} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

describe('When building deck urls', () => {
  it('should create the edit deck url', () => {
    // Arrange
    const locale = 'en';
    const gameId = 'g1';
    const deckId = 'd1';

    // Act
    const url = buildEditDeckUrl(locale, gameId, deckId);

    // Assert
    expect(url).toBe('/en/games/g1/decks/d1');
  });

  it('should append the query string when provided', () => {
    // Arrange
    const locale = 'en';
    const gameId = 'g1';
    const deckId = 'd1';
    const query = '?filters=INKABLE';

    // Act
    const url = buildEditDeckUrlWithQuery(locale, gameId, deckId, query);

    // Assert
    expect(url).toBe('/en/games/g1/decks/d1?filters=INKABLE');
  });

  it('should create the deck builder url', () => {
    // Arrange
    const locale = 'en';
    const gameId = 'g1';

    // Act
    const url = buildDeckBuilderUrl(locale, gameId);

    // Assert
    expect(url).toBe('/en/games/g1/decks');
  });

  it('should create the decks url', () => {
    // Arrange
    const locale = 'en';
    const gameId = 'g1';

    // Act
    const url = buildDecksUrl(locale, gameId);

    // Assert
    expect(url).toBe('/en/games/g1/decks');
  });
});

describe('When building waiting url', () => {
  it('should create the waiting for opponent url', () => {
    // Arrange
    const locale = 'en';
    const gameId = 'g1';

    // Act
    const url = buildWaitingForOpponentUrl(locale, gameId);

    // Assert
    expect(url).toBe('/en/games/g1/waiting-for-opponent');
  });
});
