import {buildStudioUrl, buildStudioGamesUrl, buildStudioCatalogUrl} from './urlBuilder'

describe('When building studio urls', () => {
  it('should build the studio root url', () => {
    // Arrange
    const locale = 'en'

    // Act
    const url = buildStudioUrl(locale)

    // Assert
    expect(url).toBe('/en/studio')
  })

  it('should build the studio games url', () => {
    // Arrange
    const locale = 'en'

    // Act
    const url = buildStudioGamesUrl(locale)

    // Assert
    expect(url).toBe('/en/studio/games')
  })

  it('should build the studio catalog url', () => {
    // Arrange
    const locale = 'en'

    // Act
    const url = buildStudioCatalogUrl(locale)

    // Assert
    expect(url).toBe('/en/studio/catalog')
  })
})
