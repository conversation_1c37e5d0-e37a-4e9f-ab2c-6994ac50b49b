export const buildMatchUrl = (locale: string, matchId: string) => {
  return `/${locale}/matches/${matchId}`;
};

export const buildGameListUrl = (locale: string) => {
  return `/${locale}/games`;
};

export const buildGameUrl = (locale: string, gameId: string) => {
  return `/${locale}/games/${gameId}`;
};

export const buildPlayGameUrl = (locale: string, gameId: string) => {
  return `/${locale}/games/${gameId}/play`;
};

export const buildWaitingForOpponentUrl = (locale: string, gameId: string) => {
  return `/${locale}/games/${gameId}/waiting-for-opponent`;
};

export const buildSignInUrl = (locale: string) => {
  return `/${locale}/signin`;
};

export const buildAccessDeniedUrl = (locale: string) => {
  return `/${locale}/access-denied`;
};

export const buildStudioUrl = (locale: string) => {
  return `/${locale}/studio`;
};

export const buildStudioGamesUrl = (locale: string) => {
  return `${buildStudioUrl(locale)}/games`;
};

export const buildStudioCatalogUrl = (locale: string) => {
  return `${buildStudioUrl(locale)}/catalog`;
};

export const buildEditDeckUrl = (
  locale: string,
  gameId: string,
  deckId: string,
) => {
  return `/${locale}/games/${gameId}/decks/${deckId}`;
};

export const buildEditDeckUrlWithQuery = (
  locale: string,
  gameId: string,
  deckId: string,
  query: string,
) => {
  const base = buildEditDeckUrl(locale, gameId, deckId);
  return query ? `${base}${query}` : base;
};

export const buildDeckBuilderUrl = (
  locale: string,
  gameId: string,
) => {
  return `/${locale}/games/${gameId}/decks`;
};

export const buildDecksUrl = (
  locale: string,
  gameId: string,
) => {
  return `/${locale}/games/${gameId}/decks`;
};
