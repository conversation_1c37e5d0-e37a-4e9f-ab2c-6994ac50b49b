'use client';
import type {Container} from '@tsparticles/engine';
import Particles, {initParticlesEngine} from '@tsparticles/react';
import {loadSlim} from '@tsparticles/slim';
import {motion, useAnimation} from 'motion/react';
import React, {useEffect, useId, useState} from 'react';
import {cn} from "@/src/client/Shared/helpers/Tailwind/utils";

type ParticlesProps = {
  id?: string;
  className?: string;
};

export const Sparkles = (props: ParticlesProps) => {
  const {id, className} = props;
  const [init, setInit] = useState(false);

  useEffect(() => {
    initParticlesEngine(async (engine) => {
      await loadSlim(engine);
    }).then(() => {
      setInit(true);
    });
  }, []);

  const controls = useAnimation();

  const particlesLoaded = async (container?: Container) => {
    if (container) {
      controls.start({
        opacity: 1,
        transition: {
          duration: 1,
        },
      });
    }
  };

  const generatedId = useId();
  return (
    <motion.div animate={controls} className={cn('opacity-0', className)}>
      {init && (
        <Particles
          id={id || generatedId}
          className={cn('h-full w-full')}
          particlesLoaded={particlesLoaded}
        />
      )}
    </motion.div>
  );
};
