import {FC, forwardRef} from "react";
import {Card, Code, Heading} from "@radix-ui/themes";

type Props = {
  data: object
};

const JsonObjectViewer: FC<Props> = forwardRef<HTMLDivElement, Props>(({data, ...props}, ref) => {
  return (
    <Card size="2" ref={ref} {...props}>
      <Heading size="5">Debug</Heading>
      <pre>
        <Code>
          {JSON.stringify(data, null, 2)}
        </Code>
      </pre>
    </Card>
  );
});

JsonObjectViewer.displayName = 'JsonObjectViewer';

export default JsonObjectViewer;