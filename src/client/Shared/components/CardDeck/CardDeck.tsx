'use client'

import { ReactElement } from 'react'
import { ImageProps } from 'next/image'
import Image from 'next/image'
import clsx from 'clsx'

type CardDeckProps = {
  children: [ReactElement<ImageProps>, ReactElement<ImageProps>, ReactElement<ImageProps>]
  cover: string
  coverCardCount?: number
}

export function CardDeck({ children, cover, coverCardCount = 10 }: CardDeckProps) {
  const [first, second, third] = children

  return (
    <div className="group relative w-[180px] h-[250px]">
      {Array.from({ length: coverCardCount }).map((_, i) => (
        <div
          key={i}
          className="absolute top-0 left-0 w-full h-full transition-transform duration-300"
          style={{
            transform: `translateZ(-${(coverCardCount - i) * 2}px)`,
            zIndex: i,
          }}
        >
          <div className="relative w-full h-full">
            <Image
              src={cover}
              alt="Card back"
              fill
              sizes="180px"
              className="object-cover rounded-lg shadow-md"
            />
          </div>
        </div>
      ))}

      {[first, second, third].filter(Boolean).map((child, i) => {
        const hoverTransform = [
          'translateZ(10px) rotateZ(0deg)',
          'translateZ(20px) translateX(-20px) rotateZ(-8deg)',
          'translateZ(30px) translateX(20px) rotateZ(8deg)',
        ][i]

        return (
          <div
            key={i}
            className={clsx(
              'absolute top-0 left-0 w-full h-full transition-transform duration-500 ease-out',
              'rounded-lg shadow-xl',
              'group-hover:z-[200]'
            )}
            style={{
              transform: `translateZ(${i * 4}px)`,
              zIndex: 100 + i,
            }}
          >
            <div
              className="relative w-full h-full transition-transform duration-500 ease-out group-hover:scale-[1.05]"
              style={{
                transform: hoverTransform,
              }}
            >
              <Image
                {...child.props}
                alt={`Card ${i + 1}`}
                fill
                className="object-cover rounded-lg"
              />
            </div>
          </div>
        )
      })}
    </div>
  )
}
