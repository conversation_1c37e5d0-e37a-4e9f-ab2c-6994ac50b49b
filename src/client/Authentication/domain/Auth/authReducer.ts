import {createReducer} from '@reduxjs/toolkit';
import {
  userSignedInEvent,
  signInFailedEvent,
  signInStartedEvent,
} from './authEvents';

export interface AuthState {
  status: 'idle' | 'loading' | 'authenticated' | 'error';
  error: string | null;
}

export const initialAuthState: AuthState = {
  status: 'idle',
  error: null,
};

export const authReducer = createReducer(initialAuthState, builder =>
  builder
    .addCase(signInStartedEvent, state => {
      state.status = 'loading';
      state.error = null;
    })
    .addCase(userSignedInEvent, state => {
      state.status = 'authenticated';
    })
    .addCase(signInFailedEvent, (state, {payload}) => {
      state.status = 'error';
      state.error = payload.error;
    })
);
