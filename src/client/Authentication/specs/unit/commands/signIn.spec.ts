import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {signIn} from '@/src/client/Authentication/application/commands/signIn/signIn';
import {buildGameListUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

describe('signIn', () => {
  describe('When executing the command', () => {
    it('should update state while signing in', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const authSignIn = vi.fn().mockResolvedValue(undefined);
      const locale = 'en';

      // Act
      const promise = dispatch(signIn({authSignIn, locale}));

      // Assert
      expect(getState().auth.status).toBe('loading');
      await promise;
      expect(authSignIn).toHaveBeenCalledWith('github', {redirectTo: buildGameListUrl(locale)});
      expect(getState().auth.status).toBe('authenticated');
    });

    it('should capture an error when sign in fails', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const authSignIn = vi.fn().mockRejectedValue(new Error('failure'));
      const locale = 'en';

      // Act
      await dispatch(signIn({authSignIn, locale}));

      // Assert
      expect(getState().auth.status).toBe('error');
      expect(getState().auth.error).toBe('failure');
    });
  });
});
