import {createAsyncThunk} from '@reduxjs/toolkit';
import {
  signInFailedEvent,
  signInStartedEvent,
  userSignedInEvent,
} from '@/src/client/Authentication/domain/Auth/authEvents';
import {buildGameListUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {SignInRequest} from './signInRequest';

export const signIn = createAsyncThunk<void, SignInRequest, {state: RootState}>(
  'authentication/signIn',
  async ({authSignIn, locale}, {dispatch}) => {
    dispatch(signInStartedEvent());
    try {
      await authSignIn('github', {redirectTo: buildGameListUrl(locale)});
      dispatch(userSignedInEvent());
    } catch (error) {
      dispatch(signInFailedEvent({error: (error as Error).message}));
    }
  }
);
