import {<PERSON>} from "react";
import {Flex, Heading} from "@radix-ui/themes";
import {Ghost} from "lucide-react";

type Props = {
  locale: string
};

const AccessDeniedPage: FC<Props> = ({locale}) => {
  console.log("locale", locale);

  return (
    <Flex align="center" justify="center" className="min-h-svh">
      <Flex direction="column" gap="3" align="center" justify="center">
        <Ghost size={100} className="animate-bounce" />
        <Heading>{'<' + 'AccessDenied' + '/>'}</Heading>
      </Flex>
    </Flex>
  );
};

export default AccessDeniedPage;