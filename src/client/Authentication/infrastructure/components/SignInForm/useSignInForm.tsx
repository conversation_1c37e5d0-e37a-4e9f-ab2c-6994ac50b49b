import {useAuthActions} from '@convex-dev/auth/react';
import {useCallback} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useLocale} from '@/src/client/Shared/hooks/useLocale/useLocale';
import {signIn} from '@/src/client/Authentication/application/commands/signIn/signIn';
import {isSigningIn} from '@/src/client/Authentication/application/queries/isSigningIn/isSigningIn';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';

export const useSignInForm = () => {
  const locale = useLocale();
  const dispatch = useDispatch<AppDispatch>();
  const {signIn: authSignIn} = useAuthActions();
  const isLoading = useSelector(isSigningIn);

  const handleSignIn = useCallback(async () => {
    await dispatch(signIn({authSignIn, locale}));
  }, [dispatch, authSignIn, locale]);

  return {
    isLoading,
    handleSignIn,
  };
};
