import {createAsyncThunk} from '@reduxjs/toolkit';
import {gameCreatedEvent} from '@/src/client/GameManagement/domain/Game/gameEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';

export const createGame = createAsyncThunk<void, {game: {id: string; name: string}}, {state: RootState; extra: ThunkExtra}>(
  'gameManagement/createGame',
  async ({game}, {dispatch}) => {
    dispatch(gameCreatedEvent(game));
  },
);
