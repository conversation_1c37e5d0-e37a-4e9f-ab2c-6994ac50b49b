import {createAsyncThunk} from '@reduxjs/toolkit';
import {gameUpdatedEvent} from '@/src/client/GameManagement/domain/Game/gameEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';

export const updateGame = createAsyncThunk<void, {game: {id: string; name: string}}, {state: RootState; extra: ThunkExtra}>(
  'gameManagement/updateGame',
  async ({game}, {dispatch}) => {
    dispatch(gameUpdatedEvent(game));
  },
);
