import {
  gamesLoadedEvent,
  gamesLoadingFailedEvent,
  gamesLoadingStartedEvent,
} from '@/src/client/GameManagement/domain/Game/gameEvents';
import {LoadGamesRequest} from './loadGamesRequest';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';

export const loadGames = (request: LoadGamesRequest) => (dispatch: AppDispatch): void => {
  if (!request) {
    dispatch(gamesLoadingStartedEvent());
    return;
  }

  if (request.error) {
    dispatch(gamesLoadingFailedEvent({error: request.error}));
    return;
  }

  const games = request.data?.games || [];
  dispatch(gamesLoadedEvent({games}));
};
