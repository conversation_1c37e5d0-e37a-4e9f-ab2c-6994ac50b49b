import {createAsyncThunk} from '@reduxjs/toolkit';
import {gameDeletedEvent} from '@/src/client/GameManagement/domain/Game/gameEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';

export const deleteGame = createAsyncThunk<void, {id: string}, {state: RootState; extra: ThunkExtra}>(
  'gameManagement/deleteGame',
  async ({id}, {dispatch}) => {
    dispatch(gameDeletedEvent(id));
  },
);
