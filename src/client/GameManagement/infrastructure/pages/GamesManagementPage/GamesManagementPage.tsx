'use client'

import {Container, Flex, Grid, Heading} from '@radix-ui/themes'
import {FC} from 'react'
import ShiningCard from '@/src/client/Shared/components/ShiningCard/ShiningCard'
import ShiningButton from '@/src/client/Shared/components/ShiningButton/ShiningButton'

const GamesManagementPage: FC = () => {
  const games = [
    {id: '1', name: 'Game 1'},
    {id: '2', name: 'Game 2'},
  ]

  return (
    <Container p="3">
      <Flex direction="column" gap="3">
        <Heading size="4">Games</Heading>
        <ShiningButton color="green" className="self-start">Create Game</ShiningButton>
        <Grid columns="3" gap="3">
          {games.map(game => (
            <ShiningCard key={game.id} className="p-3">
              <Heading size="3" className="mb-2">{game.name}</Heading>
              <Flex gap="2">
                <ShiningButton size={{initial: '2'}} color="blue">Edit</ShiningButton>
                <ShiningButton size={{initial: '2'}} color="red">Delete</ShiningButton>
              </Flex>
            </ShiningCard>
          ))}
        </Grid>
      </Flex>
    </Container>
  )
}

export default GamesManagementPage
