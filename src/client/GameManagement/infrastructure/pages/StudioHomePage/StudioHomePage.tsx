'use client'

import {Con<PERSON><PERSON>, <PERSON>bs, <PERSON>lex, Heading} from '@radix-ui/themes'
import Link from 'next/link'
import {FC} from 'react'
import ShiningCard from '@/src/client/Shared/components/ShiningCard/ShiningCard'
import ShiningButton from '@/src/client/Shared/components/ShiningButton/ShiningButton'
import {useLocale} from '@/src/client/Shared/hooks/useLocale/useLocale'
import {buildStudioGamesUrl, buildStudioCatalogUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder'

const StudioHomePage: FC = () => {
  const locale = useLocale()
  const gamesUrl = buildStudioGamesUrl(locale)
  const catalogUrl = buildStudioCatalogUrl(locale)

  return (
    <Container p="3">
      <Tabs.Root defaultValue="games">
        <Tabs.List>
          <Tabs.Trigger value="games">Games</Tabs.Trigger>
          <Tabs.Trigger value="catalog">Catalog</Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content value="games" className="mt-3">
          <Flex gap="3" wrap="wrap">
            <ShiningCard className="p-4 w-[260px] text-center">
              <Heading size="4" className="mb-2">Manage Games</Heading>
              <ShiningButton asChild>
                <Link href={gamesUrl}>Open</Link>
              </ShiningButton>
            </ShiningCard>
          </Flex>
        </Tabs.Content>
        <Tabs.Content value="catalog" className="mt-3">
          <Flex gap="3" wrap="wrap">
            <ShiningCard className="p-4 w-[260px] text-center">
              <Heading size="4" className="mb-2">Manage Catalog</Heading>
              <ShiningButton asChild>
                <Link href={catalogUrl}>Open</Link>
              </ShiningButton>
            </ShiningCard>
          </Flex>
        </Tabs.Content>
      </Tabs.Root>
    </Container>
  )
}

export default StudioHomePage
