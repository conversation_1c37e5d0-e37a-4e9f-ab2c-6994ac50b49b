import {createReducer} from '@reduxjs/toolkit';
import {
  gameCreatedEvent,
  gameUpdatedEvent,
  gameDeletedEvent,
  gamesLoadingStartedEvent,
  gamesLoadedEvent,
  gamesLoadingFailedEvent,
} from './gameEvents';
import {Game} from './Game';

export interface GameManagementState {
  games: Record<string, Game>;
  status: 'loading' | 'success' | 'error';
  error: string | null;
}

export const initialGameManagementState: GameManagementState = {
  games: {},
  status: 'loading',
  error: null,
};

export const gameManagementReducer = createReducer(initialGameManagementState, builder =>
  builder
    .addCase(gamesLoadingStartedEvent, state => {
      state.status = 'loading';
      state.error = null;
    })
    .addCase(gamesLoadedEvent, (state, {payload}) => {
      state.status = 'success';
      state.games = Object.fromEntries(payload.games.map(game => [game.id, game]));
    })
    .addCase(gamesLoadingFailedEvent, (state, {payload}) => {
      state.status = 'error';
      state.error = payload.error;
    })
    .addCase(gameCreatedEvent, (state, {payload}) => {
      state.games[payload.id] = payload;
    })
    .addCase(gameUpdatedEvent, (state, {payload}) => {
      state.games[payload.id] = payload;
    })
    .addCase(gameDeletedEvent, (state, {payload}) => {
      delete state.games[payload];
    }),
);
