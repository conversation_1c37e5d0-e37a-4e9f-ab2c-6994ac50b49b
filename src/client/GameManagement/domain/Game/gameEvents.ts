import {createAction} from '@reduxjs/toolkit';
import {Game} from './Game';

export const gameCreatedEvent = createAction<Game>('gameManagement/gameCreated');
export const gameUpdatedEvent = createAction<Game>('gameManagement/gameUpdated');
export const gameDeletedEvent = createAction<string>('gameManagement/gameDeleted');

export const gamesLoadingStartedEvent = createAction('gameManagement/gamesLoadingStarted');
export const gamesLoadedEvent = createAction<{games: Game[]}>('gameManagement/gamesLoaded');
export const gamesLoadingFailedEvent = createAction<{error: string}>('gameManagement/gamesLoadingFailed');
