import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadGames} from '@/src/client/GameManagement/application/commands/loadGames/loadGames';
import {isGameManagementLoading} from '@/src/client/GameManagement/application/queries/isGameManagementLoading/isGameManagementLoading';
import {getGameManagementError} from '@/src/client/GameManagement/application/queries/getGameManagementError/getGameManagementError';
import {getGames} from '@/src/client/GameManagement/application/queries/getGames/getGames';

describe('loadGames', () => {
  describe('When the request is undefined', () => {
    it('should start loading the games', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadGames(request)(dispatch);

      // Assert
      expect(isGameManagementLoading(getState())).toBe(true);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = undefined;

      // Act
      loadGames(request)(dispatch);

      // Assert
      expect(getGameManagementError(getState())).toBeNull();
    });
  });

  describe('When the request has an error', () => {
    it('should return the error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadGames(request)(dispatch);

      // Assert
      expect(getGameManagementError(getState())).toBe('An error occurred');
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: 'An error occurred', data: null};

      // Act
      loadGames(request)(dispatch);

      // Assert
      expect(isGameManagementLoading(getState())).toBe(false);
    });
  });

  describe('When the request is successful', () => {
    const games = [
      {id: '1', name: 'Game 1'},
      {id: '2', name: 'Game 2'},
    ];

    it('should retrieve the games', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {games}};

      // Act
      loadGames(request)(dispatch);

      // Assert
      expect(getGames(getState())).toEqual(games);
    });

    it('should not be loading', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {games}};

      // Act
      loadGames(request)(dispatch);

      // Assert
      expect(isGameManagementLoading(getState())).toBe(false);
    });

    it('should not have any error', () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      const request = {error: null, data: {games}};

      // Act
      loadGames(request)(dispatch);

      // Assert
      expect(getGameManagementError(getState())).toBeNull();
    });
  });
});
