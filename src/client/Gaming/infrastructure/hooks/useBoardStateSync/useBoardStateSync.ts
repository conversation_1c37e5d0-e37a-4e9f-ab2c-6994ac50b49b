import {useQuery} from 'convex/react';
import {useDispatch} from 'react-redux';
import {useEffect} from 'react';
import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {boardStateSyncedEvent} from '@/src/client/Gaming/domain/GameBoard/gameBoardEvents';

export const useBoardStateSync = (matchId: string | undefined) => {
  const dispatch = useDispatch<AppDispatch>();

  const boardStateData = useQuery(
    api.queries.board.getBoardState,
    matchId ? {matchId: matchId as Id<'matches'>} : 'skip'
  );

  useEffect(() => {
    if (boardStateData?.boardState) {
      dispatch(boardStateSyncedEvent({
        boardState: boardStateData.boardState
      }));
    }
  }, [boardStateData, dispatch]);

  return {
    isLoading: boardStateData === undefined && matchId !== undefined,
    error: false
  };
};
