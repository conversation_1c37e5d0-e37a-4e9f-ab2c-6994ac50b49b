import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';

export const useCurrentPlayer = (matchId: string | undefined): {
  currentPlayerPosition: 'player1' | 'player2' | undefined;
  isLoading: boolean;
} => {
  const matchData = useQuery(
    api.queries.match.loadMatchById,
    matchId ? {matchId} : 'skip'
  );

  if (matchId === undefined || matchData === undefined) {
    return {
      currentPlayerPosition: undefined,
      isLoading: matchId !== undefined
    };
  }

  if (matchData.error || !matchData.data?.currentPlayerPosition) {
    return {
      currentPlayerPosition: undefined,
      isLoading: false
    };
  }

  return {
    currentPlayerPosition: matchData.data.currentPlayerPosition,
    isLoading: false
  };
};
