import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {loadMulliganData} from '@/src/client/Gaming/application/commands/loadMulliganData/loadMulliganData';
import {submitMulliganSelection} from '@/src/client/Gaming/application/commands/submitMulliganSelection/submitMulliganSelection';
import {mulliganCardToggledEvent, mulliganSkippedEvent} from '@/src/client/Gaming/domain/Mulligan/mulliganEvents';
import {
  getMulliganData,
  getMulliganSelectedCards,
  isMulliganLoading,
  getMulliganError,
  isMulliganSubmitted
} from '@/src/client/Gaming/application/queries/getMulliganData/getMulliganData';

type UseMulliganScreenProps = {
  matchId: string;
};

type HandCard = {
  id: string;
  imageUrl: string;
};

export const useMulliganScreen = ({matchId}: UseMulliganScreenProps) => {
  const dispatch = useDispatch<AppDispatch>();
  
  const mulliganData = useSelector((state: RootState) => getMulliganData(state));
  const selectedCardIds = useSelector((state: RootState) => getMulliganSelectedCards(state));
  const isLoading = useSelector((state: RootState) => isMulliganLoading(state));
  const error = useSelector((state: RootState) => getMulliganError(state));
  const isSubmitted = useSelector((state: RootState) => isMulliganSubmitted(state));

  useEffect(() => {
    dispatch(loadMulliganData({matchId}));
  }, [dispatch, matchId]);

  const handCards: HandCard[] = mulliganData ? 
    mulliganData.handCardIds.map((cardId: string, index: number) => ({
      id: cardId,
      imageUrl: mulliganData.handCardUrls[index] || '/placeholder-card.png'
    })) : [];

  const finalHandCards: HandCard[] = mulliganData ? 
    mulliganData.finalHandCardIds.map((cardId: string, index: number) => ({
      id: cardId,
      imageUrl: mulliganData.finalHandCardUrls[index] || '/placeholder-card.png'
    })) : [];

  const onToggleCard = (cardId: string) => {
    dispatch(mulliganCardToggledEvent({cardId}));
  };

  const onSkip = () => {
    dispatch(mulliganSkippedEvent({matchId}));
    dispatch(submitMulliganSelection({
      matchId,
      selectedCardIds: [],
      skipped: true,
      round: mulliganData?.currentRound || 1
    }));
  };

  const onSubmit = () => {
    dispatch(submitMulliganSelection({
      matchId,
      selectedCardIds,
      skipped: false,
      round: mulliganData?.currentRound || 1
    }));
  };

  return {
    handCards,
    finalHandCards,
    selectedCardIds,
    isLoading,
    error,
    isSubmitted,
    currentRound: mulliganData?.currentRound || 1,
    maxRounds: mulliganData?.maxRounds || 1,
    onToggleCard,
    onSkip,
    onSubmit
  };
};