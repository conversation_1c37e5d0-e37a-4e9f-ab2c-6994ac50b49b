import {useQuery} from "convex/react";
import {useDispatch} from 'react-redux';
import {api} from "@/convex/_generated/api";
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {hideZoomedCard} from '@/src/client/Gaming/application/commands/hideZoomedCard/hideZoomedCard';

interface ViewModelData {
  status?: string;
  error?: string;
  data?: {
    status?: string;
    players?: string[];
    isWinner?: boolean;
  };
}

interface MatchPageState {
  viewModel: ViewModelData;
  handleCanvasInteraction: () => void;
  pageType: 'finished' | 'mulligan' | 'error' | 'game';
  matchData?: {
    players: string[];
    isWinner: boolean;
  };
  errorMessage?: string;
}

export const useMatchPage = (matchId: string): MatchPageState => {
  const dispatch = useDispatch<AppDispatch>();
  
  const viewModel = useQuery(api.queries.match.loadMatchById, {matchId}) as ViewModelData;

  const handleCanvasInteraction = () => {
    dispatch(hideZoomedCard());
  };
  let pageType: 'finished' | 'mulligan' | 'error' | 'game' = 'game';
  let matchData: { players: string[]; isWinner: boolean; } | undefined;
  let errorMessage: string | undefined;

  if (viewModel?.data?.status === "finished" && viewModel.data.players && typeof viewModel.data.isWinner === 'boolean') {
    pageType = 'finished';
    matchData = {
      players: viewModel.data.players,
      isWinner: viewModel.data.isWinner
    };
  } else if (viewModel?.data?.status === "waiting_for_mulligan") {
    pageType = 'mulligan';
  } else if (viewModel?.error) {
    pageType = 'error';
    errorMessage = viewModel.error;
  }

  return {
    viewModel,
    handleCanvasInteraction,
    pageType,
    matchData,
    errorMessage
  };
};