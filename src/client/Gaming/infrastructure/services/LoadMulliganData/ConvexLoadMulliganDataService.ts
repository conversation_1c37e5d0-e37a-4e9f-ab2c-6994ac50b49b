import {LoadMulliganDataService} from '@/src/client/Gaming/application/ports/LoadMulliganDataService';
import {LoadMulliganDataRequest} from '@/src/client/Gaming/application/commands/loadMulliganData/loadMulliganDataRequest';
import {MulliganData} from '@/src/client/Gaming/domain/Mulligan/mulliganEvents';
import {convexClient} from '@/src/client/Shared/providers/ConvexClientProvider/ConvexClient';
import {api} from '@/convex/_generated/api';

export class ConvexLoadMulliganDataService implements LoadMulliganDataService {
  async loadMulliganData(request: LoadMulliganDataRequest): Promise<MulliganData> {
    try {
      const result = await convexClient.query(
        api.queries.gaming.loadMulliganData,
        { matchId: request.matchId }
      );
      
      if (result.error) {
        throw new Error(result.error);
      }
      
      if (!result.data) {
        throw new Error('No mulligan data returned');
      }
      
      return result.data;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to load mulligan data');
    }
  }
}