import {GameService, MatchData, LoadGameByIdResult, GameData} from '@/src/client/Gaming/application/ports/GameService';

export class FakeGameService implements GameService {
  private readonly matchData: MatchData;
  private readonly games: Record<string, GameData>;

  constructor(
    matchData: MatchData = {
      player1Cards: ['156', '159', '512', '200', '157', '225', '574'],
      player2CardCount: 6
    }
  ) {
    this.matchData = matchData;
    this.games = {};
  }

  async loadMatchData(): Promise<MatchData> {
    return this.matchData;
  }

  addGame(game: GameData): void {
    this.games[game.id] = game;
  }

  async loadGameById(gameId: string): Promise<LoadGameByIdResult> {
    const game = this.games[gameId];

    if (game === undefined) {
      throw new Error('Failed to load game data');
    }

    return {data: game};
  }
};
