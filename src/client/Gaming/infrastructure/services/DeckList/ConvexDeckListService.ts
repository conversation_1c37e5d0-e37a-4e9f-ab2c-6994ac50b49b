import {DeckListService, LoadDecksResult} from '@/src/client/Gaming/application/ports/DeckListService';
import {convexClient} from '@/src/client/Shared/providers/ConvexClientProvider/ConvexClient';
import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';

export class ConvexDeckListService implements DeckListService {
  async loadDecksByUserAndGame(gameId: string, locale: string): Promise<LoadDecksResult> {
    try {
      const result = await convexClient.query(
        api.queries.deck.loadDecksByUserIdAndGameId,
        {gameId: gameId as Id<'games'>, locale}
      );
      
      if (result.error) {
        return {decks: [], error: result.error};
      }

      return {decks: result.data?.decks || []};
    } catch (e) {
      const message = e instanceof Error ? e.message : 'Unknown error';
      return {decks: [], error: message};
    }
  }
};
