import {DeckListService, LoadDecksResult} from '@/src/client/Gaming/application/ports/DeckListService';

export class FakeDeckListService implements DeckListService {
  private readonly result: LoadDecksResult;
  private readonly shouldThrow: boolean;

  constructor(
    result: LoadDecksResult = {decks: []},
    shouldThrow = false
  ) {
    this.result = result;
    this.shouldThrow = shouldThrow;
  }

  async loadDecksByUserAndGame(): Promise<LoadDecksResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to load decks');
    }
    return this.result;
  }
};
