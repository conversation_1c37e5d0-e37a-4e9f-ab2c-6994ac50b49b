'use client';

import {FC, PropsWithChildren} from "react";
import Link from "next/link";
import {useLocale} from "@/src/client/Shared/hooks/useLocale/useLocale";
import {buildGameUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";
import {Button} from "@radix-ui/themes";

type Props = {
  gameId: string
};

const GameDetailsButton: FC<PropsWithChildren<Props>> = ({gameId, children}) => {
  const locale = useLocale();
  const url = buildGameUrl(locale, gameId);

  return (
    <Button className="w-full" size="3" asChild>
      <Link className="w-full" href={url}>{children}</Link>
    </Button>
  );
};

export default GameDetailsButton;