'use client';

import {FC} from "react";
import {useSelector, useDispatch} from 'react-redux';
import {getZoomedCard} from '@/src/client/Gaming/application/queries/getZoomedCard/getZoomedCard';
import {hideZoomedCard} from '@/src/client/Gaming/application/commands/hideZoomedCard/hideZoomedCard';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {Box} from "@radix-ui/themes";
import Image from 'next/image';

export const CardZoom: FC = () => {
  const zoomedCardId = useSelector(getZoomedCard);
  const dispatch = useDispatch<AppDispatch>();

  if (!zoomedCardId) {
    return null;
  }

  return (
    <Box
      style={{
        position: 'absolute',
        top: '80px',
        left: '20px',
        zIndex: 20,
        width: '300px',
        height: '450px',
        borderRadius: '12px',
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        cursor: 'pointer'
      }}
      onClick={() => dispatch(hideZoomedCard())}
    >
      <Image
        src={zoomedCardId}
        alt="Zoomed card"
        width={300}
        height={450}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
      />
    </Box>
  );
};
