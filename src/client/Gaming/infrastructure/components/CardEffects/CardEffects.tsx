import {FC} from "react";
import {Sparkles} from "@react-three/drei";

type Props = {
  type: 'sparkles',
  color: 'white' | 'blue' | 'red' | 'green' | 'yellow'
};

const CardEffects: FC<Props> = ({ type, color }) => {
  if(type === 'sparkles') {
    return (
      <Sparkles
        scale={1.2}
        color={color}
        count={200}
        noise={20}
        opacity={0.4}
        size={50}
        speed={1.2}
      />
    );
  }

  return null;
};

export default CardEffects;