'use client';

import { Badge, Flex, Heading, Text } from '@radix-ui/themes';
import Image from 'next/image';
import { FC } from 'react';

interface GameHeaderProps {
  name: string;
  publisher?: string;
  playerCount?: string;
  playersOnline?: number;
  totalMatches?: number;
  bannerUrl: string;
  locale: string;
  publishedByText: string;
  playersOnlineText: (count: string) => string;
  matchesPlayedText: (count: string) => string;
}

const GameHeader: FC<GameHeaderProps> = ({
  name,
  publisher,
  playerCount,
  playersOnline,
  totalMatches,
  bannerUrl,
  locale,
  publishedByText,
  playersOnlineText,
  matchesPlayedText,
}) => {
  return (
    <Flex direction="column" gap="6">
      <div className="relative w-full overflow-hidden rounded-xl bg-gray-2 shadow-lg">
        <Image
          src={bannerUrl}
          alt={`${name} banner`}
          width={1200}
          height={400}
          className="aspect-[3/1] w-full object-cover"
          sizes="100vw"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
      </div>

      <Flex direction={{ initial: 'column', md: 'row' }} justify="between" align="start" gap="4">
        <Flex direction="column" gap="3" className="flex-1">
          <Flex direction="column" gap="2">
            <Heading size={{ initial: '6', sm: '7', md: '8' }} className="text-gray-12">
              {name}
            </Heading>
            {publisher && (
              <Text size={{ initial: '2', sm: '3' }} color="gray" className="flex flex-wrap items-center gap-1">
                {publishedByText}
                <Text as="span" className="font-medium text-gray-11">
                  {publisher}
                </Text>
              </Text>
            )}
          </Flex>
          {playerCount && (
            <Text size="2" color="gray" className="font-medium">
              {playerCount}
            </Text>
          )}
        </Flex>

        <Flex align="center" gap="3" wrap="wrap" className="justify-start md:justify-end">
          {playersOnline && playersOnline > 0 && (
            <Badge 
              size="2" 
              color="green" 
              variant="solid"
              className="bg-green-9 text-white"
            >
              {playersOnlineText(playersOnline.toLocaleString(locale))}
            </Badge>
          )}
          {totalMatches && totalMatches > 0 && (
            <Badge 
              size="2" 
              color="blue" 
              variant="solid"
              className="bg-blue-9 text-white"
            >
              {matchesPlayedText(totalMatches.toLocaleString(locale))}
            </Badge>
          )}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default GameHeader;