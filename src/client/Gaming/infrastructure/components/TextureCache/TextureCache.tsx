'use client';

import {createContext, useContext, ReactNode, useMemo} from 'react';
import {useLoader} from '@react-three/fiber';
import {TextureLoader, Texture} from 'three';
import {useSelector} from 'react-redux';
import {getPlayer1Hand} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';
import {getPlayer1FirstRow, getPlayer1SecondRow, getPlayer2FirstRow, getPlayer2SecondRow} from '@/src/client/Gaming/application/queries/getGameBoard/getGameBoard';

interface TextureCacheContextType {
  getTexture: (cardId: string) => Texture | undefined;
  backTexture: Texture;
}

const TextureCacheContext = createContext<TextureCacheContextType | null>(null);

interface Props {
  children: ReactNode;
}

export const TextureCacheProvider = ({children}: Props) => {
  // Get all card IDs from hands and board
  const player1Hand = useSelector(getPlayer1Hand);
  const player1FirstRow = useSelector(getPlayer1FirstRow);
  const player1SecondRow = useSelector(getPlayer1SecondRow);
  const player2FirstRow = useSelector(getPlayer2FirstRow);
  const player2SecondRow = useSelector(getPlayer2SecondRow);

  // Collect all unique card IDs
  const allCardIds = useMemo(() => {
    const cardIds = new Set<string>();

    // Add hand cards (only player1 hand is available as individual cards)
    player1Hand.forEach(cardId => cardIds.add(cardId));
    
    // Add board cards
    player1FirstRow.forEach(card => card && cardIds.add(card.cardId));
    player1SecondRow.forEach(card => card && cardIds.add(card.cardId));
    player2FirstRow.forEach(card => card && cardIds.add(card.cardId));
    player2SecondRow.forEach(card => card && cardIds.add(card.cardId));
    
    return Array.from(cardIds);
  }, [player1Hand, player1FirstRow, player1SecondRow, player2FirstRow, player2SecondRow]);

  // Preload all textures
  const textureUrls = useMemo(() => {
    if (allCardIds.length === 0) {
      return ['/game-assets/cards/back-small.png', '/game-assets/cards/back.png'];
    }

    // Handle both simple card IDs and full paths
    const urls = allCardIds.map(cardId => {
      // If it's already a full path, use it as is
      if (cardId.startsWith('/game-assets/') || cardId.startsWith('http')) {
        return cardId;
      }
      // Otherwise, construct the path
      return `/game-assets/cards/en/thumbnail/${cardId}.jpg`;
    });
    urls.push('/game-assets/cards/back.png');
    return urls;
  }, [allCardIds]);

  const textures = useLoader(TextureLoader, textureUrls);
  
  const textureMap = useMemo(() => {
    const map = new Map<string, Texture>();

    if (allCardIds.length === 0) {
      return map;
    }

    allCardIds.forEach((cardId, index) => {
      const texture = Array.isArray(textures) ? textures[index] : textures;

      // Store texture with the original cardId
      map.set(cardId, texture);

      // Also store with simple ID if cardId is a path
      if (cardId.includes('/game-assets/cards/en/thumbnail/')) {
        const simpleId = cardId.replace('/game-assets/cards/en/thumbnail/', '').replace('.jpg', '');
        map.set(simpleId, texture);
      }

      // Also store with full path if cardId is simple
      if (!cardId.startsWith('/game-assets/') && !cardId.startsWith('http')) {
        const fullPath = `/game-assets/cards/en/thumbnail/${cardId}.jpg`;
        map.set(fullPath, texture);
      }
    });

    return map;
  }, [allCardIds, textures]);

  const backTexture = useMemo(() => {
    if (Array.isArray(textures)) {
      return textures[textures.length - 1]; // Last texture is the back texture
    }
    return textures;
  }, [textures]);

  const contextValue = useMemo(() => ({
    getTexture: (cardId: string) => textureMap.get(cardId),
    backTexture
  }), [textureMap, backTexture]);

  return (
    <TextureCacheContext.Provider value={contextValue}>
      {children}
    </TextureCacheContext.Provider>
  );
};

export const useTextureCache = () => {
  const context = useContext(TextureCacheContext);
  if (!context) {
    throw new Error('useTextureCache must be used within a TextureCacheProvider');
  }
  return context;
};
