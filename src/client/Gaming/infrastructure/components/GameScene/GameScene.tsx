'use client';

import {FC, useEffect, useState} from "react";
import Camera from "@/src/client/Gaming/infrastructure/components/Camera/Camera";
import Lights from "@/src/client/Gaming/infrastructure/components/Lights/Lights";
import Table from "@/src/client/Gaming/infrastructure/components/Table/Table";
import Shadow from "@/src/client/Gaming/infrastructure/components/Shadow/Shadow";
import FixedToCamera from "@/src/client/Gaming/infrastructure/components/FixedToCamera/FixedToCamera";
import Player1Hand from "@/src/client/Gaming/infrastructure/components/Player1Hand/Player1Hand";
import Player2Hand from "@/src/client/Gaming/infrastructure/components/Player2Hand/Player2Hand";
import {useDispatch} from 'react-redux';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';
import {initializeGameScene} from '@/src/client/Gaming/application/commands/initializeGameScene/initializeGameScene';
import {placeCard} from '@/src/client/Gaming/application/commands/placeCard/placeCard';
import Game2x7Layout from "@/src/client/Gaming/infrastructure/components/Game2x7Layout/Game2x7Layout";
import {useBoardStateSync} from '@/src/client/Gaming/infrastructure/hooks/useBoardStateSync/useBoardStateSync';
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';
import {TextureCacheProvider} from '@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache';

type Props = {
  matchId?: string;
};

const GameScene: FC<Props> = ({matchId}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const {currentPlayerPosition} = useCurrentPlayer(matchId);
  
  useBoardStateSync(matchId);

  useEffect(() => {
    dispatch(initializeGameScene({matchId}));
  }, [dispatch, matchId]);

  const handleCardSelect = (cardId: string) => {
    setSelectedCard(selectedCard === cardId ? null : cardId);
  };

  const handleSlotClick = async (rowType: string, slotIndex: number) => {
    if (!selectedCard || !matchId || !currentPlayerPosition) {
      return;
    }

    const normalizedRowType = rowType.includes('first') ? 'first' : 'second';
    
    const isCurrentPlayerRow = rowType.includes(currentPlayerPosition);
    if (!isCurrentPlayerRow) {
      return;
    }
    
    try {
      await dispatch(placeCard({
        cardId: selectedCard,
        rowType: normalizedRowType as 'first' | 'second',
        slotIndex,
        matchId,
        currentPlayerPosition
      }));
      setSelectedCard(null);
    } catch (error) {
      console.error('Failed to place card:', error);
    }
  };

  return (
    <TextureCacheProvider>
      <Camera/>
      <Lights/>
      <Table/>
      <Shadow/>
      <Game2x7Layout
        selectedCard={selectedCard}
        onSlotClick={handleSlotClick}
        currentPlayerPosition={currentPlayerPosition}
      />
      <FixedToCamera>
        <Player2Hand/>
        <Player1Hand
          onCardSelect={handleCardSelect}
        />
      </FixedToCamera>
    </TextureCacheProvider>
  );
};

export default GameScene;
