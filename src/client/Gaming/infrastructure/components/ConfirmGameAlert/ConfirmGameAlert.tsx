import {FC, PropsWithChildren} from "react";
import {AlertDialog, Button, Flex} from "@radix-ui/themes";

type Props = {
  title: string;
  text: string;
  okText?: string;
  cancelText?: string;
  onAccept: () => void;
};

const ConfirmGameAlert: FC<PropsWithChildren<Props>> = (props) => {
  const {title, text, okText = 'OK', cancelText = 'Cancel', onAccept, children} = props;

  return (
    <AlertDialog.Root>
      <AlertDialog.Trigger>
        {children}
      </AlertDialog.Trigger>
      <AlertDialog.Content maxWidth="450px">
        <AlertDialog.Title>{title}</AlertDialog.Title>
        <AlertDialog.Description size="5">
          {text}
        </AlertDialog.Description>

        <Flex gap="3" mt="4" justify="end">
          <AlertDialog.Cancel>
            <Button variant="soft" color="gray" size="3">
              {cancelText}
            </Button>
          </AlertDialog.Cancel>
          <AlertDialog.Action>
            <Button variant="solid" color="red" size="3" onClick={onAccept}>
              {okText}
            </Button>
          </AlertDialog.Action>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};

export default ConfirmGameAlert;