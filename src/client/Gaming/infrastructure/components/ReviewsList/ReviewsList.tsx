'use client';

import { Flex, Text } from '@radix-ui/themes';
import { FC } from 'react';
import ShiningCard from '@/src/client/Shared/components/ShiningCard/ShiningCard';

interface Review {
  id: string;
  playerName: string;
  rating: number;
  comment: string;
  createdAt: string;
}

interface ReviewsListProps {
  reviews: Review[];
  emptyMessage: string;
  ratingText: (rating: number) => string;
  reviewDateText: (date: string) => string;
  locale: string;
}

const ReviewsList: FC<ReviewsListProps> = ({
  reviews,
  emptyMessage,
  ratingText,
  reviewDateText,
  locale,
}) => {
  if (!reviews || reviews.length === 0) {
    return (
      <Flex justify="center" align="center" className="min-h-32">
        <Text size="3" color="gray">{emptyMessage}</Text>
      </Flex>
    );
  }

  return (
    <div className="space-y-4 p-4">
      {reviews.map((review) => (
        <ShiningCard
          key={review.id}
          className="border border-gray-6 bg-gray-2 p-4 shadow-sm transition-all duration-300 hover:border-gray-7 hover:shadow-md"
        >
          <Flex direction="column" gap="3">
            <Flex justify="between" align="start" gap="4">
              <Text weight="bold" size="3" className="text-gray-12 flex-1">
                {review.playerName}
              </Text>
              <Flex gap="3" align="center" className="shrink-0">
                <div className="flex items-center gap-1">
                  {Array.from({ length: 5 }, (_, i) => (
                    <span
                      key={i}
                      className={`text-sm ${
                        i < review.rating ? 'text-yellow-500' : 'text-gray-400'
                      }`}
                    >
                      ★
                    </span>
                  ))}
                  <Text size="2" color="gray" className="ml-1">
                    {ratingText(review.rating)}
                  </Text>
                </div>
                <Text size="1" color="gray" className="text-right">
                  {reviewDateText(new Date(review.createdAt).toLocaleDateString(locale))}
                </Text>
              </Flex>
            </Flex>
            <Text size="3" className="leading-relaxed text-gray-11">
              {review.comment}
            </Text>
          </Flex>
        </ShiningCard>
      ))}
    </div>
  );
};

export default ReviewsList;