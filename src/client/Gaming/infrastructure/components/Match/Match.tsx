'use client';

import {FC} from "react";
import {Id} from "@/convex/_generated/dataModel";
import {useMatchEndNavigation} from "@/src/client/Gaming/infrastructure/hooks/useMatchEndNavigation/useMatchEndNavigation";
import MulliganScreen from "../MulliganScreen/MulliganScreen";

type Props = {
  locale: string;
  matchId: Id<"matches">;
  match: {
    players: string[];
    status: string;
    gameId: Id<"games">;
  };
};

const Match: FC<Props> = ({locale, matchId, match}) => {
  useMatchEndNavigation({matchId, locale, gameId: match.gameId});

  if (match.status === 'waiting_for_mulligan') {
    return <MulliganScreen matchId={matchId as string} />;
  }

  if (match.status === 'active') {
    return (
      <div>
        <p>Match ID: {matchId}</p>
        <p>Players: {match?.players.join(" vs ")}</p>
        <p>Status: {match?.status}</p>
        <p>Game is active - implement 3D game view here</p>
      </div>
    );
  }

  return (
    <div>
      <p>Match ID: {matchId}</p>
      <p>Players: {match?.players.join(" vs ")}</p>
      <p>Status: {match?.status}</p>
      <p>Setting up match...</p>
    </div>
  );
};

export default Match;