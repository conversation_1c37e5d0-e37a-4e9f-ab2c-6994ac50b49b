import {useFrame, useThree} from '@react-three/fiber';
import {useRef} from 'react';
import {Mesh, Vector3} from 'three';

const FixedInView = () => {
  const ref = useRef<Mesh>(null);
  const {camera} = useThree();

  const offset = new Vector3(1.5, -1.5, -5);

  useFrame(() => {
    if (!ref.current) return;

    const cameraDirection = camera.getWorldDirection(new Vector3()).normalize();
    const cameraUp = camera.up.clone().normalize();
    const cameraRight = new Vector3().crossVectors(cameraDirection, cameraUp).normalize();

    const pos = camera.position
      .clone()
      .add(cameraRight.clone().multiplyScalar(offset.x))
      .add(cameraUp.clone().multiplyScalar(offset.y))
      .add(cameraDirection.clone().multiplyScalar(-offset.z));

    ref.current.position.copy(pos);
    ref.current.quaternion.copy(camera.quaternion);
  });

  return (
    <mesh ref={ref}>
      <planeGeometry args={[2, 1]} />
      <meshBasicMaterial color="white" />
    </mesh>
  );
};

export default FixedInView;
