'use client';

import {FC} from "react";
import {But<PERSON>} from "@radix-ui/themes";
import Link from "next/link";
import {useLocale} from "@/src/client/Shared/hooks/useLocale/useLocale";
import {buildPlayGameUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";

type Props = {
  gameId: string,
};

const PlayGameButton: FC<Props> = ({gameId}) => {
  const locale = useLocale();
  const url = buildPlayGameUrl(locale, gameId);

  return (
    <Button size="3" color="iris" asChild>
      <Link href={url}>Play Now</Link>
    </Button>
  );
};

export default PlayGameButton;