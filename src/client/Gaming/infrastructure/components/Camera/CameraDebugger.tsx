import {useThree} from '@react-three/fiber';
import {OrbitControls} from '@react-three/drei';
import {useEffect, useRef} from 'react';
import {OrbitControls as OrbitControlsImpl} from 'three-stdlib';

export const CameraDebugger = () => {
  const controlsRef = useRef<OrbitControlsImpl>(null);
  const {camera} = useThree();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'd') {
        console.log('Camera Position:', camera.position.toArray());
        console.log('Camera Target:', controlsRef.current?.target.toArray());
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [camera]);

  return <OrbitControls ref={controlsRef}/>;
};
