'use client';

import {FC} from "react";
import {Flex, Heading, Text} from "@radix-ui/themes";
import ShiningCard from "@/src/client/Shared/components/ShiningCard/ShiningCard";
import GameDetailsButton from "@/src/client/Gaming/infrastructure/components/GameDetailsButton/GameDetailsButton";
import {useTranslations} from "next-intl";
import Image from "next/image";

type Props = {
  gameId: string;
  name: string;
  description?: string;
  playerCount?: string;
  imageUrl?: string;
};

const GameCard: FC<Props> = ({gameId, name, description, playerCount, imageUrl}) => {
  const t = useTranslations('games');
  const defaultImageUrl = imageUrl || "/game-assets/lorcana-banner.png";

  return (
    <ShiningCard className="p-0 hover:shadow-lg transition-all duration-200 overflow-hidden">
      <Flex direction="column" height="100%" justify="between">
        <div className="relative w-full h-32 bg-gradient-to-br from-gray-100 to-gray-200">
          <Image
            src={defaultImageUrl}
            alt={name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
        
        <Flex direction="column" gap="2" p="4" className="flex-1">
          <Heading size="4" className="text-center">
            {name}
          </Heading>
          {description && (
            <Text size="2" color="gray" className="text-center line-clamp-2">
              {description}
            </Text>
          )}
          {playerCount && (
            <Text size="1" color="gray" className="text-center">
              {playerCount}
            </Text>
          )}
        </Flex>
        
        <div className="p-4 pt-0">
          <GameDetailsButton gameId={gameId}>
            {t('playButton')}
          </GameDetailsButton>
        </div>
      </Flex>
    </ShiningCard>
  );
};

export default GameCard;