'use client';

import {FC} from "react";
import {Canvas} from "@react-three/fiber";
import FinishedMatchPage from "@/src/client/Gaming/infrastructure/pages/FinishedMatchPage/FinishedMatchPage";
import ErrorLoadingMatchPage
  from "@/src/client/Gaming/infrastructure/pages/ErrorLoadingMatchPage/ErrorLoadingMatchPage";
import MulliganScreen from "@/src/client/Gaming/infrastructure/components/MulliganScreen/MulliganScreen";
import {useMatchPage} from "@/src/client/Gaming/infrastructure/hooks/useMatchPage/useMatchPage";
import GameScene from "@/src/client/Gaming/infrastructure/components/GameScene/GameScene";
import HUD from "@/src/client/Gaming/infrastructure/components/HUD/HUD";

type Props = {
  matchId: string;
};

const MatchPage: FC<Props> = ({matchId}) => {
  const { pageType, handleCanvasInteraction, matchData, errorMessage } = useMatchPage(matchId);

  if (pageType === 'finished' && matchData) {
    return <FinishedMatchPage matchId={matchId} match={matchData}/>;
  }

  if (pageType === 'mulligan') {
    return <MulliganScreen matchId={matchId}/>;
  }

  if (pageType === 'error' && errorMessage) {
    return <ErrorLoadingMatchPage errorMessage={errorMessage}/>;
  }

  return (
    <div style={{width: '100vw', height: '100vh', position: 'relative'}}>
      <Canvas 
        style={{width: '100%', height: '100%'}} 
        shadows={false}
        onContextMenu={(e) => e.preventDefault()}
        onClick={handleCanvasInteraction}
        onPointerDown={(e) => {
          if (e.button === 2) {
            handleCanvasInteraction();
          }
        }}
      >
        <GameScene matchId={matchId}/>
      </Canvas>

      <HUD matchId={matchId}/>
    </div>
  );
};

export default MatchPage;
