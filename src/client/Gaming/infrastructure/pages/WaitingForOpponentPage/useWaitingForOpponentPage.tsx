import {useCallback, useEffect} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useRouter} from "next/navigation";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";
import {cancelMatchRegistration} from "@/src/client/Gaming/application/commands/cancelMatchRegistration/cancelMatchRegistration";
import {monitorMatchCreation} from "@/src/client/Gaming/application/commands/monitorMatchCreation/monitorMatchCreation";
import {startSearchTimer} from "@/src/client/Gaming/application/commands/startSearchTimer/startSearchTimer";
import {tickSearchTimer} from "@/src/client/Gaming/application/commands/tickSearchTimer/tickSearchTimer";
import {stopSearchTimer} from "@/src/client/Gaming/application/commands/stopSearchTimer/stopSearchTimer";
import {getSearchTime} from "@/src/client/Gaming/application/queries/getSearchTime/getSearchTime";
import {formatSearchTime} from "@/src/client/Gaming/application/queries/formatSearchTime/formatSearchTime";

export const useWaitingForOpponentPage = ({gameId, locale}: {gameId: string; locale: string}) => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const searchTime = useSelector(getSearchTime);

  useEffect(() => {
    dispatch(startSearchTimer());

    const interval = setInterval(() => {
      dispatch(tickSearchTimer());
    }, 1000);

    return () => {
      clearInterval(interval);
      dispatch(stopSearchTimer());
    };
  }, [dispatch]);

  useEffect(() => {
    const checkForMatch = async () => {
      const result = await dispatch(monitorMatchCreation({gameId, locale})).unwrap();
      if (result.matchFound && result.redirectUrl) {
        router.push(result.redirectUrl);
      }
    };

    const interval = setInterval(checkForMatch, 1000);
    return () => clearInterval(interval);
  }, [dispatch, gameId, locale, router]);

  const handleCancel = useCallback(async () => {
    try {
      const result = await dispatch(cancelMatchRegistration({gameId, locale})).unwrap();
      if (result.success && result.redirectUrl) {
        router.push(result.redirectUrl);
      }
    } catch (error) {
      console.error('Failed to cancel match registration:', error);
    }
  }, [dispatch, gameId, locale, router]);

  const formatTime = useCallback((seconds: number) => {
    return formatSearchTime(seconds);
  }, []);

  return {
    searchTime,
    formatTime,
    handleCancel,
  };
};
