'use client';

import {FC} from "react";
import {Box, Button, Container, Flex, Text} from "@radix-ui/themes";
import ShiningCard from "@/src/client/Shared/components/ShiningCard/ShiningCard";
import {useWaitingForOpponentPage} from "@/src/client/Gaming/infrastructure/pages/WaitingForOpponentPage/useWaitingForOpponentPage";

type Props = {
  gameId: string;
  locale: string;
};

const WaitingForOpponentPage: FC<Props> = ({gameId, locale}) => {
  const {searchTime, formatTime, handleCancel} = useWaitingForOpponentPage({gameId, locale});

  return (
    <Container p="3">
      <Flex direction="column" justify="center" align="center" mt="8">
        <ShiningCard size="3" disableScaling selected>
          <Flex direction="column" gap="3">
            <Box style={{textAlign: "center"}}>
              <Flex justify="center" align="center" gap="2" mb="2">
                <Text size="6" weight="medium">
                  Searching for opponent...
                </Text>
              </Flex>
              <Text size="9" weight="bold">
                {formatTime(searchTime)}
              </Text>
              <Text
                size="4"
                color="gray"
                style={{marginTop: "8px", display: "block"}}
              >
                Estimated wait: 2-5 minutes
              </Text>
            </Box>

            <Button
              variant="outline"
              color="red"
              size="4"
              style={{width: "100%"}}
              onClick={handleCancel}
            >
              Cancel Search
            </Button>
          </Flex>
        </ShiningCard>
      </Flex>
    </Container>
  );
};

export default WaitingForOpponentPage;