'use client';

import { Container, Flex, Tabs, Text, Spinner, Callout } from '@radix-ui/themes';
import PlayGameButton from '@/src/client/Gaming/infrastructure/components/PlayGameButton/PlayGameButton';
import GameHeader from '@/src/client/Gaming/infrastructure/components/GameHeader/GameHeader';
import MediaGallery from '@/src/client/Gaming/infrastructure/components/MediaGallery/MediaGallery';
import ReviewsList from '@/src/client/Gaming/infrastructure/components/ReviewsList/ReviewsList';
import { FC } from 'react';
import { useTranslations } from 'next-intl';
import { useGameDetailsPage } from './useGameDetailsPage';
import { ExclamationTriangleIcon } from '@radix-ui/react-icons';

type Props = {
  locale: string;
  gameId: string;
};

const GameDetailsPage: FC<Props> = ({ locale, gameId }) => {
  const t = useTranslations('games.gameDetails');
  const { currentGame, loading, error, activeTab, setActiveTab } = useGameDetailsPage(gameId, locale);

  if (loading || (!currentGame && !error)) {
    return (
      <Container size="4" className="py-6">
        <Flex direction="column" align="center" justify="center" gap="4" className="min-h-96">
          <Spinner size="3" />
          <Text size="3" color="gray">{t('loading')}</Text>
        </Flex>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="4" className="py-6">
        <Callout.Root color="red" size="2">
          <Callout.Icon>
            <ExclamationTriangleIcon />
          </Callout.Icon>
          <Callout.Text>{t('error')}</Callout.Text>
        </Callout.Root>
      </Container>
    );
  }

  if (!currentGame) {
    return (
      <Container size="4" className="py-6">
        <Flex direction="column" align="center" justify="center" gap="4" className="min-h-96">
          <Text size="3" color="gray">{t('error')}</Text>
        </Flex>
      </Container>
    );
  }

  const defaultBannerUrl = currentGame.bannerUrl || currentGame.imageUrl || '/game-assets/lorcana-banner.png';

  const screenshotItems = currentGame.screenshots?.map((screenshot, i) => ({
    src: screenshot,
    alt: `${currentGame.name} screenshot ${i + 1}`,
    type: 'image' as const,
  })) || [];

  const videoItems = currentGame.videos?.map((video, i) => ({
    src: video,
    alt: `${currentGame.name} video ${i + 1}`,
    type: 'video' as const,
  })) || [];

  return (
    <Container size="4" className="py-6">
      <Flex direction="column" gap="8">
        <GameHeader
          name={currentGame.name}
          publisher={currentGame.publisher}
          playerCount={currentGame.playerCount}
          playersOnline={currentGame.playersOnline}
          totalMatches={currentGame.totalMatches}
          bannerUrl={defaultBannerUrl}
          locale={locale}
          publishedByText={t('publishedBy')}
          playersOnlineText={(count) => t('playersOnline', { count })}
          matchesPlayedText={(count) => t('matchesPlayed', { count })}
        />

        <PlayGameButton gameId={gameId} />

        {currentGame.description && (
          <Text size="4" className="leading-relaxed text-gray-11 max-w-4xl">
            {currentGame.description}
          </Text>
        )}

        <Tabs.Root value={activeTab} onValueChange={setActiveTab} className="w-full">
          <Tabs.List size="2" className="mb-6">
            <Tabs.Trigger value="screenshots">{t('tabs.screenshots')}</Tabs.Trigger>
            <Tabs.Trigger value="videos">{t('tabs.videos')}</Tabs.Trigger>
            <Tabs.Trigger value="reviews">{t('tabs.reviews')}</Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value="screenshots">
            <MediaGallery
              items={screenshotItems}
              emptyMessage={t('media.noScreenshots')}
            />
          </Tabs.Content>

          <Tabs.Content value="videos">
            <MediaGallery
              items={videoItems}
              emptyMessage={t('media.noVideos')}
            />
          </Tabs.Content>

          <Tabs.Content value="reviews">
            <ReviewsList
              reviews={currentGame.reviews || []}
              emptyMessage={t('reviews.noReviews')}
              ratingText={(rating) => t('reviews.rating', { rating })}
              reviewDateText={(date) => t('reviews.reviewDate', { date })}
              locale={locale}
            />
          </Tabs.Content>
        </Tabs.Root>
      </Flex>
    </Container>
  );
}

export default GameDetailsPage;
