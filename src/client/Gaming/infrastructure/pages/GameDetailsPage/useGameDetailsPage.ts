import { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

export const useGameDetailsPage = (gameId: string, _locale: string) => { // eslint-disable-line @typescript-eslint/no-unused-vars
  const [activeTab, setActiveTabState] = useState<'screenshots' | 'videos' | 'reviews'>('screenshots');
  
  const viewModel = useQuery(api.queries.catalog.loadGameById, { gameId });
  
  const currentGame = viewModel?.data;
  const loading = viewModel === undefined;
  const error = viewModel?.error;

  const setActiveTab = (value: string) => {
    if (value === 'screenshots' || value === 'videos' || value === 'reviews') {
      setActiveTabState(value);
    }
  };

  return {
    currentGame,
    loading,
    error,
    activeTab,
    setActiveTab,
  };
};