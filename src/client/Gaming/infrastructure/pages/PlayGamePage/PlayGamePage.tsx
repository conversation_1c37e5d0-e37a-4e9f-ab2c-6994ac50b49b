'use client'

import React, {FC} from "react";
import {Box, Container, Flex, Grid, Heading, Text,} from "@radix-ui/themes";
import {PlayIcon,} from "@radix-ui/react-icons";
import ShiningCard from "@/src/client/Shared/components/ShiningCard/ShiningCard";
import ShiningButton from "@/src/client/Shared/components/ShiningButton/ShiningButton";
import Image, {ImageProps} from "next/image";
import {CardDeck} from "@/src/client/Shared/components/CardDeck/CardDeck";
import {SquareCheckBig} from "lucide-react";
import {usePlayGamePage} from "@/src/client/Gaming/infrastructure/pages/PlayGamePage/usePlayGamePage";

type Props = {
  locale: string;
  gameId: string;
};

const PlayGamePage: FC<Props> = ({gameId, locale}) => {
  const {
    deckId,
    decks,
    handleStart,
    setDeckId,
    loadingDecks,
    selectedDeckImages,
    selectedDeckName,
  } = usePlayGamePage({gameId, locale});

  const selectedElements = selectedDeckImages.map((src, index) => (
    <Image key={index} src={src} alt={`Card ${index + 1}`} fill className="object-cover rounded-lg"/>
  )) as [React.ReactElement<ImageProps>, React.ReactElement<ImageProps>, React.ReactElement<ImageProps>];

  return (
    <Container p="3">
      <Flex direction="column" className="h-full w-full">

        <Flex direction="column" gap="6" mt="8">
            <Heading size="8" align="center">Select your deck for the next match, then click Play!</Heading>

            {deckId && selectedDeckImages.length >= 3 && (
              <Flex direction="column" justify="center" align="center" gap="2">
                <Heading size="4">{selectedDeckName}</Heading>
                <Box width="150px" mt="5">
                  <CardDeck cover={`/game-assets/cards/${locale}/thumbnail/1.jpg`}>
                    {selectedElements[0]}
                    {selectedElements[1]}
                    {selectedElements[2]}
                  </CardDeck>
                </Box>
              </Flex>
            )}

            <Flex justify="center" align="center">
              <ShiningButton
                color="iris"
                size="4"
                className="w-4"
                disabled={!deckId}
                onClick={handleStart}
              >
                <PlayIcon width="24" height="24"/>
                Click here to Play
              </ShiningButton>
            </Flex>

            {!deckId && !loadingDecks && (
              <Text size="3" color="gray" align="center">
                You must select a deck in your collection to play.
              </Text>
            )}

            {loadingDecks && (
              <Text size="3" color="gray" align="center">
                 Loading...
              </Text>
            )}

            <Grid columns="5" gap="3">
              {decks.map((deck) => (
                <Flex direction="column" gap="3" key={deck.id}>
                  <ShiningCard
                    disableScaling
                    selected={deckId === deck.id}
                    key={deck.id}
                    onClick={() => setDeckId(deck.id)}
                    className="h-[300px]"
                  >
                    <Flex direction="column" gap="3" align="center" justify="center" className="relative">

                      <Box
                        display={deckId === deck.id ? "block" : "none"}
                        className={`absolute top-0 right-0 left-0 bottom-0 z-10`}>
                        <Flex direction="column" align="center" justify="end" className="h-full">
                          <SquareCheckBig width="120" height="120" className="mt-4"/>
                        </Flex>
                      </Box>

                      <Heading size="3" style={{marginBottom: "4px"}} align="center" weight="regular">
                        {deck.name}
                      </Heading>

                      <Box width="150px" mt="5" height="120px"
                           className={deckId === deck.id ? "opacity-50" : "opacity-100"}>
                        {(() => {
                          const elems = deck.images.map((src, index) => (
                              <Image key={index} src={src} alt={`Card ${index + 1}`} fill
                                     className="object-cover rounded-lg"/>
                            )) as [React.ReactElement<ImageProps>, React.ReactElement<ImageProps>, React.ReactElement<ImageProps>];
                          return (
                            <CardDeck cover={`/game-assets/cards/${locale}/thumbnail/1.jpg`}>
                              {elems[0]}
                              {elems[1]}
                              {elems[2]}
                            </CardDeck>
                          );
                        })()}
                      </Box>

                    </Flex>
                  </ShiningCard>
                </Flex>
              ))}
            </Grid>

          </Flex>

      </Flex>
    </Container>
  );
}

export default PlayGamePage;
