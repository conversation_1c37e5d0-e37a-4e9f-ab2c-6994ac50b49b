import {createReducer} from '@reduxjs/toolkit';
import {
  cardZoomedEvent,
  cardZoomHiddenEvent,
} from './cardZoomEvents';

export interface CardZoomState {
  zoomedCardId: string | null;
  isVisible: boolean;
}

export const initialCardZoomState: CardZoomState = {
  zoomedCardId: null,
  isVisible: false,
};

export const cardZoomReducer = createReducer(
  initialCardZoomState,
  (builder) =>
    builder
      .addCase(cardZoomedEvent, (state, {payload}) => {
        state.zoomedCardId = payload.cardId;
        state.isVisible = true;
      })
      .addCase(cardZoomHiddenEvent, (state) => {
        state.zoomedCardId = null;
        state.isVisible = false;
      }),
);