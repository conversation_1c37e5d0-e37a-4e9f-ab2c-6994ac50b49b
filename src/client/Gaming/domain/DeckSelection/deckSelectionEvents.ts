import {createAction} from '@reduxjs/toolkit';
import {DeckPreview} from '@/src/client/Gaming/application/ports/DeckListService';

export const decksLoadingStartedEvent = createAction('deckSelection/loadingStarted');
export const decksLoadedEvent = createAction<{decks: DeckPreview[]}>('deckSelection/loaded');
export const decksLoadingFailedEvent = createAction<{error: string}>('deckSelection/loadingFailed');
export const deckSelectedEvent = createAction<{deckId: string}>('deckSelection/deckSelected');
