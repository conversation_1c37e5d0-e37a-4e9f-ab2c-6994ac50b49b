import {createReducer} from '@reduxjs/toolkit';
import {
  decksLoadingStartedEvent,
  decksLoadedEvent,
  decksLoadingFailedEvent,
  deckSelectedEvent,
} from './deckSelectionEvents';
import {DeckPreview} from '@/src/client/Gaming/application/ports/DeckListService';

export interface DeckSelectionState {
  decks: DeckPreview[];
  selectedDeckId: string | null;
  status: 'loading' | 'success' | 'error';
  error: string | null;
}

export const initialDeckSelectionState: DeckSelectionState = {
  decks: [],
  selectedDeckId: null,
  status: 'loading',
  error: null,
};

export const deckSelectionReducer = createReducer(
  initialDeckSelectionState,
  (builder) =>
    builder
      .addCase(decksLoadingStartedEvent, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(decksLoadedEvent, (state, {payload}) => {
        state.status = 'success';
        state.decks = payload.decks;
        if (!state.selectedDeckId && payload.decks.length > 0) {
          state.selectedDeckId = payload.decks[0].id;
        }
      })
      .addCase(decksLoadingFailedEvent, (state, {payload}) => {
        state.status = 'error';
        state.error = payload.error;
      })
      .addCase(deckSelectedEvent, (state, {payload}) => {
        state.selectedDeckId = payload.deckId;
      }),
);
