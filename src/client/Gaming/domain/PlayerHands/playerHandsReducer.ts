import {createReducer} from '@reduxjs/toolkit';
import {
  player1HandUpdatedEvent,
  player2HandCountUpdatedEvent,
} from './playerHandEvents';

export interface PlayerHandsState {
  player1: string[];
  player2Count: number;
}

export const initialPlayerHandsState: PlayerHandsState = {
  player1: [],
  player2Count: 0,
};

export const playerHandsReducer = createReducer(
  initialPlayerHandsState,
  (builder) =>
    builder
      .addCase(player1HandUpdatedEvent, (state, {payload}) => {
        state.player1 = payload.cardIds;
      })
      .addCase(player2HandCountUpdatedEvent, (state, {payload}) => {
        state.player2Count = payload.count;
      }),
);
