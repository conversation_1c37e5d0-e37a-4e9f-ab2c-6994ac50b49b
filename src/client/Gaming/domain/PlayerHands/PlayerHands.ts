import {PlayerHandsState} from './playerHandsReducer';
import {
  player1HandUpdatedEvent,
  player2HandCountUpdatedEvent,
} from './playerHandEvents';

type PlayerHandsEvent =
  | ReturnType<typeof player1HandUpdatedEvent>
  | ReturnType<typeof player2HandCountUpdatedEvent>;

export class PlayerHands {
  private events: PlayerHandsEvent[] = [];
  private state: PlayerHandsState;

  private constructor(state: PlayerHandsState) {
    this.state = state;
  }

  static fromState(state: PlayerHandsState): PlayerHands {
    return new PlayerHands({
      player1: [...state.player1],
      player2Count: state.player2Count,
    });
  }

  updateHand(player: 'player1' | 'player2', cardIds: string[]): void {
    if (player === 'player1') {
      this.state.player1 = [...cardIds];
      this.events.push(player1HandUpdatedEvent({cardIds: [...cardIds]}));
    } else {
      this.state.player2Count = cardIds.length;
      this.events.push(player2HandCountUpdatedEvent({count: cardIds.length}));
    }
  }

  removeCardFromHand(player: 'player1' | 'player2', cardId: string): void {
    if (player === 'player1') {
      const cardIndex = this.state.player1.indexOf(cardId);
      if (cardIndex === -1) {
        throw new Error(`Card ${cardId} is not in ${player} hand`);
      }
      this.state.player1 = this.state.player1.filter(id => id !== cardId);
      this.events.push(player1HandUpdatedEvent({cardIds: [...this.state.player1]}));
    } else {
      if (this.state.player2Count <= 0) {
        throw new Error(`No cards in ${player} hand`);
      }
      this.state.player2Count = this.state.player2Count - 1;
      this.events.push(player2HandCountUpdatedEvent({count: this.state.player2Count}));
    }
  }

  addCardToHand(player: 'player1' | 'player2', cardId: string): void {
    if (player === 'player1') {
      this.state.player1 = [...this.state.player1, cardId];
      this.events.push(player1HandUpdatedEvent({cardIds: [...this.state.player1]}));
    } else {
      this.state.player2Count = this.state.player2Count + 1;
      this.events.push(player2HandCountUpdatedEvent({count: this.state.player2Count}));
    }
  }

  getDomainEvents(): PlayerHandsEvent[] {
    const evts = [...this.events];
    this.events = [];
    return evts;
  }
}

