import {createReducer} from '@reduxjs/toolkit';
import {gameLoadingStartedEvent, gameLoadedEvent, gameLoadingFailedEvent} from './gameEvents';

export type GameState = {
  currentGame: {
    id: string;
    name: string;
    description?: string;
    publisher?: string;
    playerCount?: string;
    imageUrl?: string;
    bannerUrl?: string;
    playersOnline?: number;
    totalMatches?: number;
    screenshots?: string[];
    videos?: string[];
    reviews?: {
      id: string;
      playerName: string;
      rating: number;
      comment: string;
      createdAt: string;
    }[];
  } | null;
  isLoading: boolean;
  error: string | null;
};

export const initialGameState: GameState = {
  currentGame: null,
  isLoading: false,
  error: null,
};

export const gameReducer = createReducer(initialGameState, (builder) => {
  builder
    .addCase(gameLoadingStartedEvent, (state) => {
      state.isLoading = true;
      state.error = null;
    })
    .addCase(gameLoadedEvent, (state, action) => {
      state.isLoading = false;
      state.currentGame = action.payload.game;
      state.error = null;
    })
    .addCase(gameLoadingFailedEvent, (state, action) => {
      state.isLoading = false;
      state.error = action.payload.error;
    });
});