import {createAction} from '@reduxjs/toolkit';

export const gameLoadingStartedEvent = createAction('gaming/gameLoadingStarted');

export const gameLoadedEvent = createAction<{
  game: {
    id: string;
    name: string;
    description?: string;
    publisher?: string;
    playerCount?: string;
    imageUrl?: string;
    bannerUrl?: string;
    playersOnline?: number;
    totalMatches?: number;
    screenshots?: string[];
    videos?: string[];
    reviews?: {
      id: string;
      playerName: string;
      rating: number;
      comment: string;
      createdAt: string;
    }[];
  };
}>('gaming/gameLoaded');

export const gameLoadingFailedEvent = createAction<{ error: string }>('gaming/gameLoadingFailed');