import {createReducer} from '@reduxjs/toolkit';
import {
  matchErrorOccurredEvent,
  matchErrorClearedEvent,
} from './matchEvents';

export interface MatchState {
  error: string | null;
}

export const initialMatchState: MatchState = {
  error: null,
};

export const matchReducer = createReducer(
  initialMatchState,
  (builder) =>
    builder
      .addCase(matchErrorOccurredEvent, (state, {payload}) => {
        state.error = payload.error;
      })
      .addCase(matchErrorClearedEvent, (state) => {
        state.error = null;
      }),
);
