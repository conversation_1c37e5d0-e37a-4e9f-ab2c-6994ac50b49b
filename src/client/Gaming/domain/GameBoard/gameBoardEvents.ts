import {createAction} from '@reduxjs/toolkit';
import { PlayerBoard } from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

export const cardPlacedOnBoardEvent = createAction<{
  playerId: 'player1' | 'player2';
  cardId: string;
  rowType: 'first' | 'second';
  slotIndex: number;
  placedAt: string;
}>('gameBoard/cardPlaced');

export const cardRemovedFromBoardEvent = createAction<{
  playerId: 'player1' | 'player2';
  rowType: 'first' | 'second';
  slotIndex: number;
}>('gameBoard/cardRemoved');

export const boardClearedEvent = createAction('gameBoard/boardCleared');

export const boardStateSyncedEvent = createAction<{
  boardState: {
    player1Board: PlayerBoard;
    player2Board: PlayerBoard;
  };
}>('gameBoard/boardStateSynced');
