import {createReducer} from '@reduxjs/toolkit';
import {
  searchTimerStartedEvent,
  searchTimerTickedEvent,
  searchTimerStoppedEvent,
  matchFoundEvent,
} from './matchMakingEvents';
import {getPlayerQueueStatus} from '@/src/client/Gaming/application/queries/getPlayerQueueStatus/getPlayerQueueStatus';

export interface MatchMakingState {
  searchTime: number;
  isSearching: boolean;
  matchId: string | null;
  queueStatus: {
    isInQueue: boolean;
    queueItem: {
      id: string;
      gameId: string;
      deckId: string;
      queuedAt: number;
    } | null;
  } | null;
}

export const initialMatchMakingState: MatchMakingState = {
  searchTime: 0,
  isSearching: false,
  matchId: null,
  queueStatus: null,
};

export const matchMakingReducer = createReducer(
  initialMatchMakingState,
  (builder) =>
    builder
      .addCase(searchTimerStartedEvent, (state) => {
        state.searchTime = 0;
        state.isSearching = true;
        state.matchId = null;
      })
      .addCase(searchTimerTickedEvent, (state) => {
        if (state.isSearching) {
          state.searchTime += 1;
        }
      })
      .addCase(searchTimerStoppedEvent, (state) => {
        state.isSearching = false;
      })
      .addCase(matchFoundEvent, (state, {payload}) => {
        state.isSearching = false;
        state.matchId = payload.matchId;
      })
      .addCase(getPlayerQueueStatus.fulfilled, (state, {payload}) => {
        state.queueStatus = payload;
      }),
);
