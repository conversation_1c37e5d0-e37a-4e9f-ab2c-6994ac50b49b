import {createReducer} from '@reduxjs/toolkit';
import {
  mulliganDataLoadedEvent,
  mulliganCardToggledEvent,
  mulliganSkippedEvent,
  mulliganSubmittedEvent,
  mulliganLoadingStartedEvent,
  mulliganErrorEvent,
  MulliganData
} from './mulliganEvents';

export type MulliganState = {
  data: MulliganData | null;
  selectedCardIds: string[];
  isLoading: boolean;
  error: string | null;
  submitted: boolean;
};

const initialMulliganState: MulliganState = {
  data: null,
  selectedCardIds: [],
  isLoading: false,
  error: null,
  submitted: false
};

export {initialMulliganState};

export const mulliganReducer = createReducer(initialMulliganState, (builder) => {
  builder
    .addCase(mulliganLoadingStartedEvent, (state) => {
      state.isLoading = true;
      state.error = null;
    })
    .addCase(mulliganDataLoadedEvent, (state, action) => {
      const newData = action.payload;
      
      state.data = newData;
      state.selectedCardIds = [];
      state.isLoading = false;
      state.error = null;
      
      state.submitted = newData.hasCompletedAllRounds;
    })
    .addCase(mulliganCardToggledEvent, (state, action) => {
      const {cardId} = action.payload;
      const index = state.selectedCardIds.indexOf(cardId);
      if (index >= 0) {
        state.selectedCardIds.splice(index, 1);
      } else {
        if (state.selectedCardIds.length < 7) {
          state.selectedCardIds.push(cardId);
        }
      }
    })
    .addCase(mulliganSkippedEvent, (state) => {
      state.selectedCardIds = [];
    })
    .addCase(mulliganSubmittedEvent, (state) => {
      state.selectedCardIds = [];
    })
    .addCase(mulliganErrorEvent, (state, action) => {
      state.error = action.payload.error;
      state.isLoading = false;
    });
});