import {createAction} from '@reduxjs/toolkit';

export type MulliganData = {
  matchId: string;
  handCardIds: string[];
  handCardUrls: string[];
  finalHandCardIds: string[];
  finalHandCardUrls: string[];
  currentRound: number;
  maxRounds: number;
  hasCompletedAllRounds: boolean;
  previousSelections: Array<{
    round: number;
    selectedCardIds: string[];
    skipped: boolean;
  }>;
};

export type MulliganSelectionData = {
  matchId: string;
  selectedCardIds: string[];
  skipped: boolean;
  round: number;
};

export const mulliganDataLoadedEvent = createAction<MulliganData>('gaming/mulliganDataLoaded');
export const mulliganCardToggledEvent = createAction<{cardId: string}>('gaming/mulliganCardToggled');
export const mulliganSkippedEvent = createAction<{matchId: string}>('gaming/mulliganSkipped');
export const mulliganSubmittedEvent = createAction<MulliganSelectionData>('gaming/mulliganSubmitted');
export const mulliganLoadingStartedEvent = createAction('gaming/mulliganLoadingStarted');
export const mulliganErrorEvent = createAction<{error: string}>('gaming/mulliganError');