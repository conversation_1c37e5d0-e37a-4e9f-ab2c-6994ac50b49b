import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {LoadMulliganDataRequest} from './loadMulliganDataRequest';
import {mulliganDataLoadedEvent, mulliganLoadingStartedEvent, mulliganErrorEvent} from '@/src/client/Gaming/domain/Mulligan/mulliganEvents';

export const loadMulliganData = createAsyncThunk<void, LoadMulliganDataRequest, {state: RootState; extra: ThunkExtra}>(
  'gaming/loadMulliganData',
  async (request, {dispatch, extra: {loadMulliganDataService}}) => {
    try {
      dispatch(mulliganLoadingStartedEvent());
      
      const data = await loadMulliganDataService.loadMulliganData(request);
      
      dispatch(mulliganDataLoadedEvent(data));
    } catch (error) {
      dispatch(mulliganErrorEvent({
        error: error instanceof Error ? error.message : 'Failed to load mulligan data'
      }));
    }
  }
);