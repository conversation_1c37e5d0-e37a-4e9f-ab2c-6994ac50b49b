import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {GameBoard} from '@/src/client/Gaming/domain/GameBoard/GameBoard';
import {PlayerHands} from '@/src/client/Gaming/domain/PlayerHands/PlayerHands';
import {RemoveCardRequest} from '@/src/client/Gaming/application/commands/removeCard/removeCardRequest';

const getCurrentPlayerId = (): 'player1' | 'player2' => {
  return 'player1';
};

export const removeCard = createAsyncThunk<void, RemoveCardRequest, {state: RootState}>(
  'gaming/removeCard',
  async ({rowType, slotIndex}, {dispatch, getState}) => {
    const state = getState();
    const playerId = getCurrentPlayerId();
    const gameBoard = GameBoard.fromState(state.gameBoard);
    const playerHands = PlayerHands.fromState(state.playerHands);

    const placedCard = gameBoard.getPlacedCard(playerId, rowType, slotIndex);
    if (!placedCard) {
      throw new Error(`No card to remove at slot ${slotIndex} in ${playerId} ${rowType} row`);
    }

    gameBoard.removeCard(playerId, rowType, slotIndex);
    playerHands.addCardToHand(playerId, placedCard.cardId);

    gameBoard.getDomainEvents().forEach(dispatch);
    playerHands.getDomainEvents().forEach(dispatch);
  }
);