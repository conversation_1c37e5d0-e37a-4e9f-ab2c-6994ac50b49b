import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {JoinMatchMakingQueueRequest} from './joinMatchMakingQueueRequest';
import {JoinMatchMakingQueueResult} from './joinMatchMakingQueueResult';
import {buildWaitingForOpponentUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

export const joinMatchMakingQueue = createAsyncThunk<
  JoinMatchMakingQueueResult,
  JoinMatchMakingQueueRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/joinMatchMakingQueue',
  async ({gameId, deckId, locale}, {extra: {matchMakingService}}) => {
    try {
      const result = await matchMakingService.joinMatchMakingQueue(gameId, deckId);
      
      if (result.error) {
        return {success: false, error: result.error};
      }

      const redirectUrl = buildWaitingForOpponentUrl(locale, gameId);
      return {success: true, redirectUrl};
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {success: false, error: errorMessage};
    }
  }
);
