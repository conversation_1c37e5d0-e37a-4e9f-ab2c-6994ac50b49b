import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {PlayerHands} from '@/src/client/Gaming/domain/PlayerHands/PlayerHands';
import {GameBoard} from '@/src/client/Gaming/domain/GameBoard/GameBoard';
import {PlaceCardRequest} from '@/src/client/Gaming/application/commands/placeCard/placeCardRequest';

export const placeCard = createAsyncThunk<
  void, 
  PlaceCardRequest & {matchId: string; currentPlayerPosition: 'player1' | 'player2'}, 
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/placeCard',
  async ({cardId, rowType, slotIndex, matchId, currentPlayerPosition}, {dispatch, getState, extra: {matchService}}) => {
    const result = await matchService.playCard(matchId, cardId, rowType, slotIndex);
    
    if (result.error) {
      throw new Error(result.error);
    }

    const state = getState();

    if (currentPlayerPosition === 'player1') {
      const playerHands = PlayerHands.fromState(state.playerHands);
      playerHands.removeCardFromHand(currentPlayerPosition, cardId);
      playerHands.getDomainEvents().forEach(dispatch);
    }

    const gameBoard = GameBoard.fromState(state.gameBoard);
    gameBoard.placeCard(currentPlayerPosition, cardId, rowType, slotIndex);
    gameBoard.getDomainEvents().forEach(dispatch);
  }
);