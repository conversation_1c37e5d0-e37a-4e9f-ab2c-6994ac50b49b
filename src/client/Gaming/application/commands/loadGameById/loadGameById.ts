import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {
  gameLoadingStartedEvent,
  gameLoadedEvent,
  gameLoadingFailedEvent,
} from '@/src/client/Gaming/domain/Game/gameEvents';

export const loadGameById = createAsyncThunk<
  void,
  { gameId: string; locale?: string },
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/loadGameById',
  async ({gameId, locale}, {dispatch, extra: {gameService}}) => {
    dispatch(gameLoadingStartedEvent());
    
    try {
      const result = await gameService.loadGameById(gameId, locale);
      
      if (result.error) {
        dispatch(gameLoadingFailedEvent({error: result.error}));
        return;
      }

      if (result.data) {
        dispatch(gameLoadedEvent({game: result.data}));
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      dispatch(gameLoadingFailedEvent({error: errorMessage}));
    }
  }
);