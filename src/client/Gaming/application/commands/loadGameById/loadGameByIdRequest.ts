export type LoadGameByIdRequest = {
  data?: {
    id: string;
    name: string;
    description?: string;
    publisher?: string;
    playerCount?: string;
    imageUrl?: string;
    bannerUrl?: string;
    playersOnline?: number;
    totalMatches?: number;
    screenshots?: string[];
    videos?: string[];
    reviews?: {
      id: string;
      playerName: string;
      rating: number;
      comment: string;
      createdAt: string;
    }[];
  } | null;
  error?: string;
};