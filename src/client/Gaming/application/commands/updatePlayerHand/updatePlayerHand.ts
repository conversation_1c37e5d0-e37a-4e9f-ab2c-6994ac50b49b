import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {PlayerHands} from '@/src/client/Gaming/domain/PlayerHands/PlayerHands';
import {
  UpdatePlayerHandRequest
} from "@/src/client/Gaming/application/commands/updatePlayerHand/updatePlayerHandRequest";

export const updatePlayerHand = createAsyncThunk<void, UpdatePlayerHandRequest, {state: RootState}>(
  'gaming/updatePlayerHand',
  async ({player, cardIds}, {dispatch, getState}) => {
    const playerHands = PlayerHands.fromState(getState().playerHands);
    playerHands.updateHand(player, cardIds);
    playerHands.getDomainEvents().forEach(dispatch);
  }
);
