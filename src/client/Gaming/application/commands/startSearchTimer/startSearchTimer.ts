import {createAsyncThunk} from '@reduxjs/toolkit';
import {searchTimerStartedEvent} from '@/src/client/Gaming/domain/MatchMaking/matchMakingEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';

export const startSearchTimer = createAsyncThunk<void, void, {state: RootState}>(
  'gaming/startSearchTimer',
  async (_, {dispatch}) => {
    dispatch(searchTimerStartedEvent());
  }
);
