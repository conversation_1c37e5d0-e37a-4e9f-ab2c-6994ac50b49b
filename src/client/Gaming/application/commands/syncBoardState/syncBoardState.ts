import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {SyncBoardStateRequest} from './syncBoardStateRequest';
import {boardStateSyncedEvent} from '@/src/client/Gaming/domain/GameBoard/gameBoardEvents';

export const syncBoardState = createAsyncThunk<void, SyncBoardStateRequest, {state: RootState; extra: ThunkExtra}>(
  'gaming/syncBoardState',
  async ({matchId}, {dispatch, extra: {matchService}}) => {
    try {
      console.log('Syncing board state for match:', matchId);
      
      const result = await matchService.getBoardState(matchId);
      
      if (result.error) {
        console.error('Failed to sync board state:', result.error);
        return;
      }

      if (result.boardState) {
        dispatch(boardStateSyncedEvent({
          boardState: result.boardState
        }));
      }
    } catch (error) {
      console.error('Failed to sync board state:', error);
    }
  }
);