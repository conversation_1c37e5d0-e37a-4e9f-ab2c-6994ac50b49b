import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {LoadDecksForGameRequest} from './loadDecksForGameRequest';
import {
  decksLoadingStartedEvent,
  decksLoadedEvent,
  decksLoadingFailedEvent,
} from '@/src/client/Gaming/domain/DeckSelection/deckSelectionEvents';

export const loadDecksForGame = createAsyncThunk<
  void,
  LoadDecksForGameRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/loadDecksForGame',
  async ({gameId, locale}, {dispatch, extra: {deckListService}}) => {
    dispatch(decksLoadingStartedEvent());
    
    try {
      const result = await deckListService.loadDecksByUserAndGame(gameId, locale);
      
      if (result.error) {
        dispatch(decksLoadingFailedEvent({error: result.error}));
        return;
      }

      dispatch(decksLoadedEvent({decks: result.decks}));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      dispatch(decksLoadingFailedEvent({error: errorMessage}));
    }
  }
);
