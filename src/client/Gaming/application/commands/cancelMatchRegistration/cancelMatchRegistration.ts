import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {CancelMatchRegistrationRequest} from './cancelMatchRegistrationRequest';
import {CancelMatchRegistrationResult} from './cancelMatchRegistrationResult';
import {buildPlayGameUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

export const cancelMatchRegistration = createAsyncThunk<
  CancelMatchRegistrationResult,
  CancelMatchRegistrationRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/cancelMatchRegistration',
  async ({gameId, locale}, {extra: {matchMakingService}}) => {
    try {
      const result = await matchMakingService.cancelMatchRegistration(gameId);
      
      if (result.error) {
        return {success: false, error: result.error};
      }

      const redirectUrl = buildPlayGameUrl(locale, gameId);
      return {success: true, redirectUrl};
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {success: false, error: errorMessage};
    }
  }
);
