import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {MonitorMatchCreationRequest} from './monitorMatchCreationRequest';
import {MonitorMatchCreationResult} from './monitorMatchCreationResult';
import {matchFoundEvent} from '@/src/client/Gaming/domain/MatchMaking/matchMakingEvents';
import {buildMatchUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

export const monitorMatchCreation = createAsyncThunk<
  MonitorMatchCreationResult,
  MonitorMatchCreationRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/monitorMatchCreation',
  async ({gameId, locale}, {dispatch, extra: {matchMakingService}}) => {
    try {
      const matchData = await matchMakingService.watchForMatchCreated(gameId);

      if (matchData?.payload?.matchId) {
        const matchId = matchData.payload.matchId;
        dispatch(matchFoundEvent({matchId}));

        const redirectUrl = buildMatchUrl(locale, matchId);
        return {
          matchFound: true,
          matchId,
          redirectUrl,
        };
      }

      return {matchFound: false};
    } catch (error) {
      console.error('Error monitoring match creation:', error);
      return {matchFound: false};
    }
  }
);
