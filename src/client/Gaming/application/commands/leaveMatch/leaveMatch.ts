import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {LeaveMatchRequest} from './leaveMatchRequest';
import {LeaveMatchResult} from './leaveMatchResult';
import {matchErrorOccurredEvent, matchErrorClearedEvent} from '@/src/client/Gaming/domain/Match/matchEvents';

export const leaveMatch = createAsyncThunk<
  LeaveMatchResult,
  LeaveMatchRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/leaveMatch',
  async ({matchId}, {dispatch, extra: {matchService}}) => {
    try {
      dispatch(matchErrorClearedEvent());
      const result = await matchService.leaveMatch(matchId);

      if (result.error) {
        dispatch(matchErrorOccurredEvent({error: result.error}));
        return {success: false, error: result.error};
      }

      return {success: true};
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      dispatch(matchErrorOccurredEvent({error: errorMessage}));
      return {success: false, error: errorMessage};
    }
  }
);
