import {createAsyncThunk} from '@reduxjs/toolkit';
import {searchTimerTickedEvent} from '@/src/client/Gaming/domain/MatchMaking/matchMakingEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';

export const tickSearchTimer = createAsyncThunk<void, void, {state: RootState}>(
  'gaming/tickSearchTimer',
  async (_, {dispatch}) => {
    dispatch(searchTimerTickedEvent());
  }
);
