import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {SubmitMulliganSelectionRequest} from './submitMulliganSelectionRequest';
import {mulliganSubmittedEvent, mulliganErrorEvent} from '@/src/client/Gaming/domain/Mulligan/mulliganEvents';
import {loadMulliganData} from '@/src/client/Gaming/application/commands/loadMulliganData/loadMulliganData';

export const submitMulliganSelection = createAsyncThunk<void, SubmitMulliganSelectionRequest, {state: RootState; extra: ThunkExtra}>(
  'gaming/submitMulliganSelection',
  async (request, {dispatch, extra: {submitMulliganSelectionService}}) => {
    try {
      await submitMulliganSelectionService.submitMulliganSelection(request);

      dispatch(mulliganSubmittedEvent({
        matchId: request.matchId,
        selectedCardIds: request.selectedCardIds,
        skipped: request.skipped,
        round: request.round
      }));

      dispatch(loadMulliganData({matchId: request.matchId}));
    } catch (error) {
      dispatch(mulliganErrorEvent({
        error: error instanceof Error ? error.message : 'Failed to submit mulligan selection'
      }));
    }
  }
);