import {createAsyncThunk} from '@reduxjs/toolkit';
import {deckSelectedEvent} from '@/src/client/Gaming/domain/DeckSelection/deckSelectionEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {SelectDeckRequest} from "@/src/client/Gaming/application/commands/selectDeck/selectDeckRequest";

export const selectDeck = createAsyncThunk<void, SelectDeckRequest, {state: RootState}>(
  'gaming/selectDeck',
  async ({deckId}, {dispatch}) => {
    dispatch(deckSelectedEvent({deckId}));
  }
);
