import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {cardZoomedEvent} from '@/src/client/Gaming/domain/CardZoom/cardZoomEvents';
import {
  ShowZoomedCardRequest
} from "@/src/client/Gaming/application/commands/showZoomedCard/showZoomedCardRequest";

export const showZoomedCard = createAsyncThunk<void, ShowZoomedCardRequest, {state: RootState}>(
  'gaming/showZoomedCard',
  async ({cardId}, {dispatch}) => {
    dispatch(cardZoomedEvent({cardId}));
  }
);