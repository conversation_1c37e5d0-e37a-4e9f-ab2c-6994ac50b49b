import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {InitializeGameSceneRequest} from './initializeGameSceneRequest';
import {updatePlayerHand} from '../updatePlayerHand/updatePlayerHand';

export const initializeGameScene = createAsyncThunk<
  void,
  InitializeGameSceneRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/initializeGameScene',
  async ({matchId}, {dispatch, extra: {gameService}, rejectWithValue}) => {
    try {
      console.log('Loading match data for matchId:', matchId);
      
      if (matchId) {
        const matchData = await gameService.loadMatchData(matchId);
        const player1Hand = matchData.player1Cards.slice(0, 7);
        const player2CardCount = Math.min(matchData.player2CardCount, 7);
        const emptyPlayer2Cards = Array(player2CardCount).fill('');
        dispatch(updatePlayerHand({player: 'player1', cardIds: player1Hand}));
        dispatch(updatePlayerHand({player: 'player2', cardIds: emptyPlayer2Cards}));
      } else {
        // Initialize with empty hands for demo mode
        dispatch(updatePlayerHand({player: 'player1', cardIds: []}));
        dispatch(updatePlayerHand({player: 'player2', cardIds: []}));
      }
    } catch (error) {
      console.error('Failed to initialize game scene:', error);
      
      // Instead of hardcoded fallback, initialize with empty hands
      dispatch(updatePlayerHand({player: 'player1', cardIds: []}));
      dispatch(updatePlayerHand({player: 'player2', cardIds: []}));
      
      return rejectWithValue(
        error instanceof Error 
          ? error.message 
          : 'Failed to load match data'
      );
    }
  }
);
