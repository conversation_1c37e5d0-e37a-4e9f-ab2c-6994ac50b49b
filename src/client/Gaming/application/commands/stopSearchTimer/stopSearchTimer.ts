import {createAsyncThunk} from '@reduxjs/toolkit';
import {searchTimerStoppedEvent} from '@/src/client/Gaming/domain/MatchMaking/matchMakingEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';

export const stopSearchTimer = createAsyncThunk<void, void, {state: RootState}>(
  'gaming/stopSearchTimer',
  async (_, {dispatch}) => {
    dispatch(searchTimerStoppedEvent());
  }
);
