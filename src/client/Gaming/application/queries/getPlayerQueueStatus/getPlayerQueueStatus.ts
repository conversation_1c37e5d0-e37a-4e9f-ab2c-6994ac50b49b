import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {GetPlayerQueueStatusRequest} from './getPlayerQueueStatusRequest';
import {PlayerQueueStatusResult} from '@/src/client/Gaming/application/ports/MatchMakingService';

export const getPlayerQueueStatus = createAsyncThunk<
  PlayerQueueStatusResult,
  GetPlayerQueueStatusRequest,
  {state: RootState; extra: ThunkExtra}
>(
  'gaming/getPlayerQueueStatus',
  async ({gameId}, {extra: {matchMakingService}}) => {
    return await matchMakingService.getPlayerQueueStatus(gameId);
  }
);