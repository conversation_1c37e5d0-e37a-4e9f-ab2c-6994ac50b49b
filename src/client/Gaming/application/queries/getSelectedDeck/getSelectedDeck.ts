import {createSelector} from 'reselect';
import {getAvailableDecks} from '../getAvailableDecks/getAvailableDecks';
import {getSelectedDeckId} from '../getSelectedDeckId/getSelectedDeckId';

export const getSelectedDeck = createSelector(
  [getAvailableDecks, getSelectedDeckId],
  (decks, selectedDeckId) => {
    if (!selectedDeckId) return null;
    return decks.find(deck => deck.id === selectedDeckId) || null;
  }
);
