import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {PlacedCard} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

export const getGameBoard = (state: RootState) => state.gameBoard;

export const getGameBoardState = (state: RootState) => state.gameBoard;

export const getPlayer1Board = (state: RootState) => state.gameBoard.player1Board;

export const getPlayer2Board = (state: RootState) => state.gameBoard.player2Board;

export const getPlayer1FirstRow = (state: RootState) => state.gameBoard.player1Board.firstRow;

export const getPlayer1SecondRow = (state: RootState) => state.gameBoard.player1Board.secondRow;

export const getPlayer2FirstRow = (state: RootState) => state.gameBoard.player2Board.firstRow;

export const getPlayer2SecondRow = (state: RootState) => state.gameBoard.player2Board.secondRow;

export const getPlacedCard = (
  state: RootState,
  playerId: 'player1' | 'player2',
  rowType: 'first' | 'second',
  slotIndex: number
): PlacedCard | null => {
  const board = state.gameBoard[`${playerId}Board`];
  const row = rowType === 'first' ? board.firstRow : board.secondRow;
  return slotIndex >= 0 && slotIndex < row.length ? row[slotIndex] : null;
};

export const isSlotEmpty = (
  state: RootState,
  playerId: 'player1' | 'player2',
  rowType: 'first' | 'second',
  slotIndex: number
): boolean => {
  return getPlacedCard(state, playerId, rowType, slotIndex) === null;
};

export const getMaxSlotsPerRow = (state: RootState) => state.gameBoard.maxSlotsPerRow;