import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {MulliganData} from '@/src/client/Gaming/domain/Mulligan/mulliganEvents';

export const getMulliganData = (state: RootState): MulliganData | null => {
  return state.mulligan.data;
};

export const getMulliganSelectedCards = (state: RootState): string[] => {
  return state.mulligan.selectedCardIds;
};

export const isMulliganLoading = (state: RootState): boolean => {
  return state.mulligan.isLoading;
};

export const getMulliganError = (state: RootState): string | null => {
  return state.mulligan.error;
};

export const isMulliganSubmitted = (state: RootState): boolean => {
  return state.mulligan.submitted;
};