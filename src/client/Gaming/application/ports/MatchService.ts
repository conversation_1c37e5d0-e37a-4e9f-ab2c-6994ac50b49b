import { PlayerBoard } from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

export type LeaveMatchResult = {
  error?: string;
};

export type PlayCardResult = {
  error?: string;
};

export type BoardState = {
  player1Board: PlayerBoard;
  player2Board: PlayerBoard;
};

export type BoardStateResult = {
  boardState?: BoardState;
  error?: string;
};

export interface MatchService {
  leaveMatch(matchId: string): Promise<LeaveMatchResult>;
  playCard(matchId: string, cardId: string, rowType: 'first' | 'second', slotIndex: number): Promise<PlayCardResult>;
  getBoardState(matchId: string): Promise<BoardStateResult>;
};
