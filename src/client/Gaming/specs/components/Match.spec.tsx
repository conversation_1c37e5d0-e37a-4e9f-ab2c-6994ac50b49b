import {render} from '@testing-library/react';
import {Provider} from 'react-redux';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import Match from '@/src/client/Gaming/infrastructure/components/Match/Match';
import {buildGameUrl} from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';
import {Id} from '@/convex/_generated/dataModel';

import {useQuery} from 'convex/react';

vi.mock('convex/react');

const pushMock = vi.fn();
vi.mock('next/navigation', () => ({
  useRouter: () => ({ push: pushMock }),
}));

describe('Match', () => {
  describe('When the match has not ended', () => {
    it('should not redirect to the game page', () => {
      // Arrange
      vi.mocked(useQuery).mockReturnValue({error: null, data: null});
      const match = {players: ['u1', 'u2'], status: 'setup', gameId: 'g1' as Id<'games'>};
      const store = createTestingStore();

      // Act
      render(
        <Provider store={store}>
          <Match locale="en" matchId={'m1' as Id<'matches'>} match={match}/>
        </Provider>
      );

      // Assert
      expect(pushMock).not.toHaveBeenCalled();
    });
  });

  describe('When the match has ended', () => {
    it('should redirect to the game page', () => {
      // Arrange
      vi.mocked(useQuery).mockReturnValue({error: null, data: {winner: 'u1'}});
      const match = {players: ['u1', 'u2'], status: 'setup', gameId: 'g1' as Id<'games'>};
      const store = createTestingStore();

      // Act
      render(
        <Provider store={store}>
          <Match locale="en" matchId={'m1' as Id<'matches'>} match={match}/>
        </Provider>
      );

      // Assert
      expect(pushMock).toHaveBeenCalledWith(buildGameUrl('en', match.gameId));
    });
  });
});
