import {render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import MatchPage from '@/src/client/Gaming/infrastructure/pages/MatchPage/MatchPage';
import {useQuery} from 'convex/react';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';

vi.mock('convex/react');

beforeAll(() => {
  // Polyfill ResizeObserver for @react-three/fiber Canvas
  global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  } as unknown as typeof ResizeObserver;
});

describe('MatchPage', () => {
  describe('When the match is loading', () => {
    it('should display the trading card scene and match events console', () => {
      // Arrange
      vi.mocked(useQuery).mockReturnValue({error: null, data: null});
      const store = createTestingStore();

      // Act
      render(
        <Provider store={store}>
          <MatchPage matchId="m1" />
        </Provider>
      );

      // Assert
      expect(document.querySelector('canvas')).toBeTruthy();
      expect(screen.getByText(/Loading.../)).toBeTruthy();
    });
  });
});
