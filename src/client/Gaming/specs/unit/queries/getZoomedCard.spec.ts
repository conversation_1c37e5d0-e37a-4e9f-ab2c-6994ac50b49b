import {getZoomedCard} from '@/src/client/Gaming/application/queries/getZoomedCard/getZoomedCard';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {initialCardZoomState} from '@/src/client/Gaming/domain/CardZoom/cardZoomReducer';

describe("When getting zoomed card", () => {
  it("should return zoomed card when visible", () => {
    // Arrange
    const state: Partial<RootState> = {
      cardZoom: {
        zoomedCardId: "test-card-123",
        isVisible: true
      }
    };
    
    // Act
    const result = getZoomedCard(state as RootState);
    
    // Assert
    expect(result).toBe("test-card-123");
  });

  it("should return null when not visible", () => {
    // Arrange
    const state: Partial<RootState> = {
      cardZoom: {
        zoomedCardId: "test-card-123",
        isVisible: false
      }
    };
    
    // Act
    const result = getZoomedCard(state as RootState);
    
    // Assert
    expect(result).toBe(null);
  });

  it("should return null when no card is zoomed", () => {
    // Arrange
    const state: Partial<RootState> = {
      cardZoom: initialCardZoomState
    };
    
    // Act
    const result = getZoomedCard(state as RootState);
    
    // Assert
    expect(result).toBe(null);
  });
});