import {formatSearchTime} from '@/src/client/Gaming/application/queries/formatSearchTime/formatSearchTime';

describe('formatSearchTime', () => {
  describe('When formatting time in seconds', () => {
    it('should format correctly for less than a minute', () => {
      // Act
      const result1 = formatSearchTime(0);
      const result2 = formatSearchTime(30);
      const result3 = formatSearchTime(59);

      // Assert
      expect(result1).toBe('0:00');
      expect(result2).toBe('0:30');
      expect(result3).toBe('0:59');
    });

    it('should format correctly for minutes', () => {
      // Act
      const result1 = formatSearchTime(60);
      const result2 = formatSearchTime(90);
      const result3 = formatSearchTime(125);

      // Assert
      expect(result1).toBe('1:00');
      expect(result2).toBe('1:30');
      expect(result3).toBe('2:05');
    });

    it('should format correctly for hours', () => {
      // Act
      const result1 = formatSearchTime(3600);
      const result2 = formatSearchTime(3665);

      // Assert
      expect(result1).toBe('60:00');
      expect(result2).toBe('61:05');
    });
  });
});
