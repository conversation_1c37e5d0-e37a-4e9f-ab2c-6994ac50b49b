import {renderHook, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {FC} from 'react';
import {usePlayGamePage} from '@/src/client/Gaming/infrastructure/pages/PlayGamePage/usePlayGamePage';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {FakeDeckListService} from '@/src/client/Gaming/infrastructure/services/DeckList/FakeDeckListService';
import {FakeMatchMakingService} from '@/src/client/Gaming/infrastructure/services/MatchMaking/FakeMatchMakingService';

const pushMock = vi.fn();
vi.mock('next/navigation', () => ({
  useRouter: () => ({ push: pushMock }),
}));

const createWrapper = (store: ReturnType<typeof createTestingStore>) => {
  const Wrapper: FC<{children: React.ReactNode}> = ({children}) => (
    <Provider store={store}>{children}</Provider>
  );
  return Wrapper;
};

describe('usePlayGamePage', () => {
  describe('When decks are loaded', () => {
    it('should select the first deck and expose its images', async () => {
      // Arrange
      const mockDecks = [
        {id: 'd1', name: 'Deck 1', images: ['/game-assets/cards/en/thumbnail/1.jpg']},
        {id: 'd2', name: 'Deck 2', images: ['/game-assets/cards/en/thumbnail/2.jpg']},
      ];
      const deckListService = new FakeDeckListService({decks: mockDecks});
      const matchMakingService = new FakeMatchMakingService();
      const store = createTestingStore({}, {deckListService, matchMakingService});

      // Act
      const {result} = renderHook(
        () => usePlayGamePage({gameId: 'g1', locale: 'en'}),
        {wrapper: createWrapper(store)}
      );

      // Wait for decks to load
      await waitFor(() => {
        expect(result.current.loadingDecks).toBe(false);
      });

      // Assert
      expect(result.current.deckId).toBe('d1');
      expect(result.current.decks[0].images).toEqual([
        '/game-assets/cards/en/thumbnail/1.jpg',
      ]);
    });
  });

  describe('When starting a match', () => {
    it('should enqueue the player and navigate to waiting page', async () => {
      // Arrange
      const mockDecks = [{id: 'd1', name: 'Deck', images: []}];
      const deckListService = new FakeDeckListService({decks: mockDecks});
      const matchMakingService = new FakeMatchMakingService();
      const store = createTestingStore({}, {deckListService, matchMakingService});

      const {result} = renderHook(
        () => usePlayGamePage({gameId: 'g1', locale: 'en'}),
        {wrapper: createWrapper(store)}
      );

      // Wait for decks to load
      await waitFor(() => {
        expect(result.current.loadingDecks).toBe(false);
      });

      // Act
      await result.current.handleStart();

      // Assert
      expect(pushMock).toHaveBeenCalledWith('/en/games/g1/waiting-for-opponent');
    });
  });
});
