import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {placeCard} from '@/src/client/Gaming/application/commands/placeCard/placeCard';
import {updatePlayerHand} from '@/src/client/Gaming/application/commands/updatePlayerHand/updatePlayerHand';
import {getPlayer1Hand} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';
import {FakeMatchService} from '@/src/client/Gaming/infrastructure/services/Match/FakeMatchService';
import {boardStateSyncedEvent} from '@/src/client/Gaming/domain/GameBoard/gameBoardEvents';

describe('placeCard', () => {
  describe('When placing card successfully', () => {
    it('should remove card from hand', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123', 'card-456']}));

      // Act
      await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Assert
      expect(getPlayer1Hand(getState())).toEqual(['card-456']);
    });
  });

  describe('When placing card with player2 position', () => {
    it('should call server with correct parameters', async () => {
      // Arrange
      const {dispatch} = createTestingStore();

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-789',
        rowType: 'second',
        slotIndex: 3,
        matchId: 'match-123',
        currentPlayerPosition: 'player2'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/fulfilled');
    });
  });

  describe('When matchService returns error', () => {
    it('should throw error and not remove card from hand', async () => {
      // Arrange
      const matchService = new FakeMatchService({}, {error: 'Server error'}, {}, false);
      const {dispatch, getState} = createTestingStore({}, {matchService});
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-123', 'card-456']}));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
      expect(getPlayer1Hand(getState())).toEqual(['card-123', 'card-456']);
    });
  });

  describe('When placing card not in player hand', () => {
    it('should throw error during hand removal', async () => {
      // Arrange
      const {dispatch} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-456']}));

      // Act
      const result = await dispatch(placeCard({
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Assert
      expect(result.type).toBe('gaming/placeCard/rejected');
    });
  });

  describe('When player1 places a card', () => {
    it('should update player1Board correctly', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-player1']}));

      // Act
      await dispatch(placeCard({
        cardId: 'card-player1',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Assert
      const boardState = getState().gameBoard;
      expect(boardState.player1Board.firstRow[0]).toEqual({
        cardId: 'card-player1',
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: 'first',
      });
    });
  });

  describe('When player2 places a card', () => {
    it('should update player2Board correctly', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();

      // Act
      await dispatch(placeCard({
        cardId: 'card-player2',
        rowType: 'first',
        slotIndex: 1,
        matchId: 'match-123',
        currentPlayerPosition: 'player2'
      }));

      // Assert
      const boardState = getState().gameBoard;
      expect(boardState.player2Board.firstRow[1]).toEqual({
        cardId: 'card-player2',
        placedAt: expect.any(String),
        slotIndex: 1,
        rowType: 'first',
      });
    });
  });

  describe('When both players place cards sequentially', () => {
    it('should allow both players to place cards without conflicts', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['card-player1']}));

      // Act
      // Player1 places a card first
      await dispatch(placeCard({
        cardId: 'card-player1',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player1'
      }));

      // Player2 places a card in a different slot
      await dispatch(placeCard({
        cardId: 'card-player2',
        rowType: 'first',
        slotIndex: 1,
        matchId: 'match-123',
        currentPlayerPosition: 'player2'
      }));

      // Assert
      const boardState = getState().gameBoard;
      expect(boardState.player1Board.firstRow[0]).toEqual({
        cardId: 'card-player1',
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: 'first',
      });
      expect(boardState.player2Board.firstRow[1]).toEqual({
        cardId: 'card-player2',
        placedAt: expect.any(String),
        slotIndex: 1,
        rowType: 'first',
      });
    });
  });

  describe('When board state sync occurs after optimistic update', () => {
    it('should preserve newer local changes during sync', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();

      // Act
      // Player2 places a card (optimistic update)
      await dispatch(placeCard({
        cardId: 'card-player2-local',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player2'
      }));

      const localPlacedAt = getState().gameBoard.player2Board.firstRow[0]?.placedAt;

      // Simulate server sync with older timestamp
      const olderTimestamp = new Date(Date.now() - 1000).toISOString();
      await dispatch(boardStateSyncedEvent({
        boardState: {
          player1Board: {
            firstRow: Array(8).fill(null),
            secondRow: Array(8).fill(null),
          },
          player2Board: {
            firstRow: [
              {
                cardId: 'card-player2-server',
                placedAt: olderTimestamp,
                slotIndex: 0,
                rowType: 'first'
              },
              ...Array(7).fill(null)
            ],
            secondRow: Array(8).fill(null),
          }
        }
      }));

      // Assert
      // local change should be preserved because it's newer
      const boardState = getState().gameBoard;
      expect(boardState.player2Board.firstRow[0]).toEqual({
        cardId: 'card-player2-local',
        placedAt: localPlacedAt,
        slotIndex: 0,
        rowType: 'first',
      });
    });

    it('should accept newer server changes during sync', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();

      // Act
      // Player2 places a card (optimistic update)
      await dispatch(placeCard({
        cardId: 'card-player2-local',
        rowType: 'first',
        slotIndex: 0,
        matchId: 'match-123',
        currentPlayerPosition: 'player2'
      }));

      // Simulate server sync with newer timestamp
      const newerTimestamp = new Date(Date.now() + 1000).toISOString();
      await dispatch(boardStateSyncedEvent({
        boardState: {
          player1Board: {
            firstRow: Array(8).fill(null),
            secondRow: Array(8).fill(null),
          },
          player2Board: {
            firstRow: [
              {
                cardId: 'card-player2-server',
                placedAt: newerTimestamp,
                slotIndex: 0,
                rowType: 'first'
              },
              ...Array(7).fill(null)
            ],
            secondRow: Array(8).fill(null),
          }
        }
      }));

      // Assert
      // server change should be accepted because it's newer
      const boardState = getState().gameBoard;
      expect(boardState.player2Board.firstRow[0]).toEqual({
        cardId: 'card-player2-server',
        placedAt: newerTimestamp,
        slotIndex: 0,
        rowType: 'first',
      });
    });
  });
});