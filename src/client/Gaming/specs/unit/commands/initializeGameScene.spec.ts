import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {initializeGameScene} from '@/src/client/Gaming/application/commands/initializeGameScene/initializeGameScene';
import {FakeGameService} from '@/src/client/Gaming/infrastructure/services/Game/FakeGameService';
import {getPlayer1Hand, getPlayer2HandCount} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';

describe('When initializing the game scene', () => {
  it('should update both player hands when matchId is provided', async () => {
    // Arrange
    const gameService = new FakeGameService({
      player1Cards: ['a', 'b', 'c'],
      player2CardCount: 2,
    });
    const {dispatch, getState} = createTestingStore({}, {gameService});

    // Act
    await dispatch(initializeGameScene({matchId: 'm1'}));

    // Assert
    const state = getState();
    expect(getPlayer1Hand(state)).toEqual(['a','b','c']);
    expect(getPlayer2HandCount(state)).toBe(2);
  });

  it('should initialize empty hands when matchId is missing', async () => {
    // Arrange
    const gameService = new FakeGameService({
      player1Cards: ['a'],
      player2CardCount: 1,
    });
    const {dispatch, getState} = createTestingStore({}, {gameService});

    // Act
    await dispatch(initializeGameScene({}));

    // Assert
    const state = getState();
    expect(getPlayer1Hand(state)).toEqual([]);
    expect(getPlayer2HandCount(state)).toBe(0);
  });

  it('should set empty hands on failure', async () => {
    // Arrange
    const gameService = new FakeGameService({
      player1Cards: ['a'],
      player2CardCount: 1,
    });
    gameService.loadMatchData = async () => { throw new Error('boom'); };
    const {dispatch, getState} = createTestingStore({}, {gameService});

    // Act
    await dispatch(initializeGameScene({matchId: 'm1'}));

    // Assert
    const state = getState();
    expect(getPlayer1Hand(state)).toEqual([]);
    expect(getPlayer2HandCount(state)).toBe(0);
  });

  it('should limit both player hands to 7 cards', async () => {
    // Arrange
    const gameService = new FakeGameService({
      player1Cards: Array.from({length: 12}, (_, i) => `/game-assets/cards/en/thumbnail/${i + 1}.jpg`),
      player2CardCount: 15,
    });
    const {dispatch, getState} = createTestingStore({}, {gameService});

    // Act
    await dispatch(initializeGameScene({matchId: 'm1'}));

    // Assert
    const state = getState();
    expect(getPlayer1Hand(state)).toHaveLength(7);
    expect(getPlayer2HandCount(state)).toBe(7);
  });
});
