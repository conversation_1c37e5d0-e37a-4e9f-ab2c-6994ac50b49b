import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadDecksForGame} from '@/src/client/Gaming/application/commands/loadDecksForGame/loadDecksForGame';
import {FakeDeckListService} from '@/src/client/Gaming/infrastructure/services/DeckList/FakeDeckListService';
import {getAvailableDecks} from '@/src/client/Gaming/application/queries/getAvailableDecks/getAvailableDecks';

describe('loadDecksForGame', () => {
  describe('When loading decks successfully', () => {
    it('should load and store decks', async () => {
      // Arrange
      const mockDecks = [
        {id: 'deck1', name: 'Deck 1', images: ['img1.jpg', 'img2.jpg', 'img3.jpg']},
        {id: 'deck2', name: 'Deck 2', images: ['img4.jpg', 'img5.jpg', 'img6.jpg']},
      ];
      const deckListService = new FakeDeckListService({decks: mockDecks});
      const {dispatch, getState} = createTestingStore({}, {deckListService});

      // Act
      await dispatch(loadDecksForGame({gameId: 'game1', locale: 'en'}));

      // Assert
      const decks = getAvailableDecks(getState());
      expect(decks).toEqual(mockDecks);
    });
  });

  describe('When loading decks fails', () => {
    it('should handle error', async () => {
      // Arrange
      const deckListService = new FakeDeckListService({decks: [], error: 'Failed to load'});
      const {dispatch, getState} = createTestingStore({}, {deckListService});

      // Act
      await dispatch(loadDecksForGame({gameId: 'game1', locale: 'en'}));

      // Assert
      const state = getState();
      expect(state.deckSelection.status).toBe('error');
      expect(state.deckSelection.error).toBe('Failed to load');
    });
  });

  describe('When service throws an error', () => {
    it('should handle thrown error', async () => {
      // Arrange
      const deckListService = new FakeDeckListService({decks: []}, true);
      const {dispatch, getState} = createTestingStore({}, {deckListService});

      // Act
      await dispatch(loadDecksForGame({gameId: 'game1', locale: 'en'}));

      // Assert
      const state = getState();
      expect(state.deckSelection.status).toBe('error');
      expect(state.deckSelection.error).toBe('Failed to load decks');
    });
  });
});
