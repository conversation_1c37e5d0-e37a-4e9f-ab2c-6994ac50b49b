import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {showZoomedCard} from '@/src/client/Gaming/application/commands/showZoomedCard/showZoomedCard';
import {hideZoomedCard} from '@/src/client/Gaming/application/commands/hideZoomedCard/hideZoomedCard';
import {getZoomedCard} from '@/src/client/Gaming/application/queries/getZoomedCard/getZoomedCard';

describe("When hiding zoomed card", () => {
  it("should clear the zoomed card from state", async () => {
    // Arrange
    const {dispatch, getState} = createTestingStore();
    const cardId = "test-card-123";
    await dispatch(showZoomedCard({cardId}));
    
    // Act
    await dispatch(hideZoomedCard());
    
    // Assert
    expect(getZoomedCard(getState())).toBe(null);
  });
});