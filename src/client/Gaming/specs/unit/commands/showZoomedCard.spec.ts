import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {showZoomedCard} from '@/src/client/Gaming/application/commands/showZoomedCard/showZoomedCard';
import {getZoomedCard} from '@/src/client/Gaming/application/queries/getZoomedCard/getZoomedCard';

describe("When showing zoomed card", () => {
  it("should set the zoomed card in state", async () => {
    // Arrange
    const {dispatch, getState} = createTestingStore();
    const cardId = "test-card-123";
    
    // Act
    await dispatch(showZoomedCard({cardId}));
    
    // Assert
    expect(getZoomedCard(getState())).toBe(cardId);
  });
});