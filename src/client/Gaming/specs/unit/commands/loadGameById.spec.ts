import { createTestingStore } from '@/src/client/Shared/testing/store/createTestingStore';
import { FakeGameService } from '@/src/client/Gaming/infrastructure/services/Game/FakeGameService';
import { loadGameById } from '@/src/client/Gaming/application/commands/loadGameById/loadGameById';
import { getCurrentGame } from '@/src/client/Gaming/application/queries/getCurrentGame/getCurrentGame';
import { isGameLoading } from '@/src/client/Gaming/application/queries/isGameLoading/isGameLoading';
import { getGameError } from '@/src/client/Gaming/application/queries/getGameError/getGameError';

describe('When loading a game by id', () => {

  it('should load game successfully', async () => {
    // Arrange
    const gameId = 'test-game-id';
    const locale = 'en';
    const gameService = new FakeGameService();
    gameService.addGame({ id: gameId, name: 'test' });
    const { dispatch, getState } = createTestingStore({}, { gameService });

    // Act
    await dispatch(loadGameById({ gameId, locale }));

    // Assert
    const state = getState();
    const currentGame = getCurrentGame(state);
    const loading = isGameLoading(state);
    const error = getGameError(state);
    
    expect(currentGame).toBeDefined();
    expect(currentGame?.id).toBe(gameId);
    expect(loading).toBe(false);
    expect(error).toBeNull();
  });

  it('should set loading state during load', async () => {
    // Arrange
    const gameId = 'test-game-id';
    const locale = 'en';
    const gameService = new FakeGameService();
    gameService.loadGameById = () =>
      new Promise(resolve => {
        setTimeout(() => resolve({ data: { id: gameId, name: 'test' } }), 0);
      });
    const { dispatch, getState } = createTestingStore({}, { gameService });

    // Act
    const loadPromise = dispatch(loadGameById({ gameId, locale }));
    
    // Assert
    const loadingState = getState();
    const loading = isGameLoading(loadingState);
    expect(loading).toBe(true);
    
    await loadPromise;
    
    const finalState = getState();
    const finalLoading = isGameLoading(finalState);
    expect(finalLoading).toBe(false);
  });

  it('should handle service returning error', async () => {
    // Arrange
    const gameId = 'test-game-id';
    const locale = 'en';
    
    const gameService = new FakeGameService();
    gameService.loadGameById = async () => ({ error: 'Game not found' });
    const { dispatch, getState } = createTestingStore({}, { gameService });

    // Act
    await dispatch(loadGameById({ gameId, locale }));

    // Assert
    const state = getState();
    const error = getGameError(state);
    const currentGame = getCurrentGame(state);
    const loading = isGameLoading(state);
    
    expect(error).toBe('Game not found');
    expect(currentGame).toBeNull();
    expect(loading).toBe(false);
  });

  it('should handle service throwing error', async () => {
    // Arrange
    const gameId = 'test-game-id';
    const locale = 'en';
    const gameService = new FakeGameService();
    const { dispatch, getState } = createTestingStore({}, { gameService });

    // Act
    await dispatch(loadGameById({ gameId, locale }));

    // Assert
    const state = getState();
    const error = getGameError(state);
    const currentGame = getCurrentGame(state);
    const loading = isGameLoading(state);
    
    expect(error).toBe('Failed to load game data');
    expect(currentGame).toBeNull();
    expect(loading).toBe(false);
  });
});
