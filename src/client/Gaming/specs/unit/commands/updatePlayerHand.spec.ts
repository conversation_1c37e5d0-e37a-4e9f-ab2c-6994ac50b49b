import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {updatePlayerHand} from '@/src/client/Gaming/application/commands/updatePlayerHand/updatePlayerHand';
import {
  getPlayer1Hand,
  getPlayer2HandCount,
} from '@/src/client/Gaming/application/queries/getPlayerHands/getPlayerHands';

describe('updatePlayerHand', () => {
  describe('When updating player1 hand', () => {
    it('should store the card ids', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();

      // Act
      await dispatch(updatePlayerHand({player: 'player1', cardIds: ['a', 'b']}));

      // Assert
      expect(getPlayer1Hand(getState())).toEqual(['a', 'b']);
    });
  });

  describe('When updating player2 hand', () => {
    it('should store the card count', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore();

      // Act
      await dispatch(updatePlayerHand({player: 'player2', cardIds: ['c']}));

      // Assert
      expect(getPlayer2HandCount(getState())).toBe(1);
    });
  });
});
