import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {joinMatchMakingQueue} from '@/src/client/Gaming/application/commands/joinMatchMakingQueue/joinMatchMakingQueue';
import {FakeMatchMakingService} from '@/src/client/Gaming/infrastructure/services/MatchMaking/FakeMatchMakingService';

describe('joinMatchMakingQueue', () => {
  describe('When joining queue successfully', () => {
    it('should return success with redirect URL', async () => {
      // Arrange
      const matchMakingService = new FakeMatchMakingService({}, null, false, {});
      const {dispatch} = createTestingStore({}, {matchMakingService});

      // Act
      const result = await dispatch(joinMatchMakingQueue({gameId: 'game1', deckId: 'deck1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(true);
      expect(result.redirectUrl).toBe('/en/games/game1/waiting-for-opponent');
    });
  });

  describe('When joining queue fails', () => {
    it('should return failure with error', async () => {
      // Arrange
      const matchMakingService = new FakeMatchMakingService({}, null, false, {error: 'Queue is full'});
      const {dispatch} = createTestingStore({}, {matchMakingService});

      // Act
      const result = await dispatch(joinMatchMakingQueue({gameId: 'game1', deckId: 'deck1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Queue is full');
    });
  });

  describe('When service throws an error', () => {
    it('should return failure with error message', async () => {
      // Arrange
      const matchMakingService = new FakeMatchMakingService({}, null, true);
      const {dispatch} = createTestingStore({}, {matchMakingService});

      // Act
      const result = await dispatch(joinMatchMakingQueue({gameId: 'game1', deckId: 'deck1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to join match making queue');
    });
  });
});
