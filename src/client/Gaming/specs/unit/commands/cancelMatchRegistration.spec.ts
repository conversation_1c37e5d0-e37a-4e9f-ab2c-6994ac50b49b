import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {cancelMatchRegistration} from '@/src/client/Gaming/application/commands/cancelMatchRegistration/cancelMatchRegistration';
import {FakeMatchMakingService} from '@/src/client/Gaming/infrastructure/services/MatchMaking/FakeMatchMakingService';

describe('cancelMatchRegistration', () => {
  describe('When canceling match registration successfully', () => {
    it('should return success with redirect URL', async () => {
      // Arrange
      const matchMakingService = new FakeMatchMakingService();
      const {dispatch} = createTestingStore({}, {matchMakingService});

      // Act
      const result = await dispatch(cancelMatchRegistration({gameId: 'game1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(true);
      expect(result.redirectUrl).toBe('/en/games/game1/play');
    });
  });

  describe('When canceling match registration fails', () => {
    it('should return failure with error', async () => {
      // Arrange
      const matchMakingService = new FakeMatchMakingService({error: 'Failed to cancel'});
      const {dispatch} = createTestingStore({}, {matchMakingService});

      // Act
      const result = await dispatch(cancelMatchRegistration({gameId: 'game1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to cancel');
    });
  });

  describe('When service throws an error', () => {
    it('should return failure with error message', async () => {
      // Arrange
      const matchMakingService = new FakeMatchMakingService({}, null, true);
      const {dispatch} = createTestingStore({}, {matchMakingService});

      // Act
      const result = await dispatch(cancelMatchRegistration({gameId: 'game1', locale: 'en'})).unwrap();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to cancel match registration');
    });
  });
});
