import {matchMakingReducer, initialMatchMakingState} from '@/src/client/Gaming/domain/MatchMaking/matchMakingReducer';
import {
  searchTimerStartedEvent,
  searchTimerTickedEvent,
  searchTimerStoppedEvent,
  matchFoundEvent,
} from '@/src/client/Gaming/domain/MatchMaking/matchMakingEvents';

describe('matchMakingReducer', () => {
  describe('When search timer starts', () => {
    it('should reset search time and set searching to true', () => {
      // Arrange
      const state = {...initialMatchMakingState, searchTime: 30, isSearching: false};

      // Act
      const result = matchMakingReducer(state, searchTimerStartedEvent());

      // Assert
      expect(result.searchTime).toBe(0);
      expect(result.isSearching).toBe(true);
      expect(result.matchId).toBeNull();
    });
  });

  describe('When search timer ticks', () => {
    it('should increment search time when searching', () => {
      // Arrange
      const state = {...initialMatchMakingState, searchTime: 5, isSearching: true};

      // Act
      const result = matchMakingReducer(state, searchTimerTickedEvent());

      // Assert
      expect(result.searchTime).toBe(6);
    });

    it('should not increment search time when not searching', () => {
      // Arrange
      const state = {...initialMatchMakingState, searchTime: 5, isSearching: false};

      // Act
      const result = matchMakingReducer(state, searchTimerTickedEvent());

      // Assert
      expect(result.searchTime).toBe(5);
    });
  });

  describe('When search timer stops', () => {
    it('should set searching to false', () => {
      // Arrange
      const state = {...initialMatchMakingState, isSearching: true};

      // Act
      const result = matchMakingReducer(state, searchTimerStoppedEvent());

      // Assert
      expect(result.isSearching).toBe(false);
    });
  });

  describe('When match is found', () => {
    it('should stop searching and set match ID', () => {
      // Arrange
      const state = {...initialMatchMakingState, isSearching: true};

      // Act
      const result = matchMakingReducer(state, matchFoundEvent({matchId: 'match123'}));

      // Assert
      expect(result.isSearching).toBe(false);
      expect(result.matchId).toBe('match123');
    });
  });
});
