import {gameBoardReducer, initialGameBoardState} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';
import {
  cardPlacedOnBoardEvent,
  cardRemovedFromBoardEvent,
  boardClearedEvent,
} from '@/src/client/Gaming/domain/GameBoard/gameBoardEvents';

describe('When managing game board state', () => {
  it('should place card on player1 first row', () => {
    // Arrange
    const placedAt = new Date().toISOString();
    const action = cardPlacedOnBoardEvent({
      playerId: 'player1',
      cardId: 'card-123',
      rowType: 'first',
      slotIndex: 0,
      placedAt,
    });

    // Act
    const newState = gameBoardReducer(initialGameBoardState, action);

    // Assert
    expect(newState.player1Board.firstRow[0]).toEqual({
      cardId: 'card-123',
      placedAt,
      slotIndex: 0,
      rowType: 'first',
    });
  });

  it('should place card on player2 second row', () => {
    // Arrange
    const placedAt = new Date().toISOString();
    const action = cardPlacedOnBoardEvent({
      playerId: 'player2',
      cardId: 'card-456',
      rowType: 'second',
      slotIndex: 3,
      placedAt,
    });

    // Act
    const newState = gameBoardReducer(initialGameBoardState, action);

    // Assert
    expect(newState.player2Board.secondRow[3]).toEqual({
      cardId: 'card-456',
      placedAt,
      slotIndex: 3,
      rowType: 'second',
    });
  });

  it('should remove card from player1 first row', () => {
    // Arrange
    const stateWithCard = gameBoardReducer(
      initialGameBoardState,
      cardPlacedOnBoardEvent({
        playerId: 'player1',
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 2,
        placedAt: new Date().toISOString(),
      })
    );
    const action = cardRemovedFromBoardEvent({
      playerId: 'player1',
      rowType: 'first',
      slotIndex: 2,
    });

    // Act
    const newState = gameBoardReducer(stateWithCard, action);

    // Assert
    expect(newState.player1Board.firstRow[2]).toBeNull();
  });

  it('should clear entire board', () => {
    // Arrange
    const stateWithCards = gameBoardReducer(
      initialGameBoardState,
      cardPlacedOnBoardEvent({
        playerId: 'player1',
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        placedAt: new Date().toISOString(),
      })
    );
    const action = boardClearedEvent();

    // Act
    const newState = gameBoardReducer(stateWithCards, action);

    // Assert
    expect(newState.player1Board.firstRow.every(slot => slot === null)).toBe(true);
    expect(newState.player1Board.secondRow.every(slot => slot === null)).toBe(true);
    expect(newState.player2Board.firstRow.every(slot => slot === null)).toBe(true);
    expect(newState.player2Board.secondRow.every(slot => slot === null)).toBe(true);
  });

  it('should handle invalid slot index gracefully', () => {
    // Arrange
    const action = cardPlacedOnBoardEvent({
      playerId: 'player1',
      cardId: 'card-123',
      rowType: 'first',
      slotIndex: 99,
      placedAt: new Date().toISOString(),
    });

    // Act
    const newState = gameBoardReducer(initialGameBoardState, action);

    // Assert
    expect(newState).toEqual(initialGameBoardState);
  });
});