import {cardZoomReducer, initialCardZoomState} from '@/src/client/Gaming/domain/CardZoom/cardZoomReducer';
import {cardZoomedEvent, cardZoomHiddenEvent} from '@/src/client/Gaming/domain/CardZoom/cardZoomEvents';

describe("When card zoom events are dispatched", () => {
  it("should show zoomed card when card zoomed event is dispatched", () => {
    // Arrange
    const cardId = "test-card-123";
    
    // Act
    const result = cardZoomReducer(initialCardZoomState, cardZoomedEvent({cardId}));
    
    // Assert
    expect(result).toEqual({
      zoomedCardId: cardId,
      isVisible: true
    });
  });

  it("should hide zoomed card when card zoom hidden event is dispatched", () => {
    // Arrange
    const initialState = {
      zoomedCardId: "test-card-123",
      isVisible: true
    };
    
    // Act
    const result = cardZoomReducer(initialState, cardZoomHiddenEvent());
    
    // Assert
    expect(result).toEqual({
      zoomedCardId: null,
      isVisible: false
    });
  });

  it("should handle unknown actions without changing state", () => {
    // Arrange
    const unknownAction = {type: "unknown/action", payload: {}};
    
    // Act
    const result = cardZoomReducer(initialCardZoomState, unknownAction);
    
    // Assert
    expect(result).toEqual(initialCardZoomState);
  });
});