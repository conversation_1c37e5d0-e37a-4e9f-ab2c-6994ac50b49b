import {deckSelectionReducer, initialDeckSelectionState} from '@/src/client/Gaming/domain/DeckSelection/deckSelectionReducer';
import {
  decksLoadingStartedEvent,
  decksLoadedEvent,
  decksLoadingFailedEvent,
  deckSelectedEvent,
} from '@/src/client/Gaming/domain/DeckSelection/deckSelectionEvents';

describe('deckSelectionReducer', () => {
  describe('When decks loading starts', () => {
    it('should set status to loading and clear error', () => {
      // Arrange
      const state = {...initialDeckSelectionState, status: 'error' as const, error: 'Previous error'};

      // Act
      const result = deckSelectionReducer(state, decksLoadingStartedEvent());

      // Assert
      expect(result.status).toBe('loading');
      expect(result.error).toBeNull();
    });
  });

  describe('When decks are loaded', () => {
    it('should set decks and auto-select first deck', () => {
      // Arrange
      const decks = [
        {id: 'deck1', name: 'Deck 1', images: []},
        {id: 'deck2', name: 'Deck 2', images: []},
      ];
      const state = {...initialDeckSelectionState, status: 'loading' as const};

      // Act
      const result = deckSelectionReducer(state, decksLoadedEvent({decks}));

      // Assert
      expect(result.status).toBe('success');
      expect(result.decks).toEqual(decks);
      expect(result.selectedDeckId).toBe('deck1');
    });

    it('should not change selection if deck already selected', () => {
      // Arrange
      const decks = [
        {id: 'deck1', name: 'Deck 1', images: []},
        {id: 'deck2', name: 'Deck 2', images: []},
      ];
      const state = {...initialDeckSelectionState, selectedDeckId: 'deck2'};

      // Act
      const result = deckSelectionReducer(state, decksLoadedEvent({decks}));

      // Assert
      expect(result.selectedDeckId).toBe('deck2');
    });
  });

  describe('When deck loading fails', () => {
    it('should set error status and message', () => {
      // Arrange
      const state = {...initialDeckSelectionState, status: 'loading' as const};

      // Act
      const result = deckSelectionReducer(state, decksLoadingFailedEvent({error: 'Failed to load'}));

      // Assert
      expect(result.status).toBe('error');
      expect(result.error).toBe('Failed to load');
    });
  });

  describe('When deck is selected', () => {
    it('should update selected deck ID', () => {
      // Arrange
      const state = {...initialDeckSelectionState, selectedDeckId: 'deck1'};

      // Act
      const result = deckSelectionReducer(state, deckSelectedEvent({deckId: 'deck2'}));

      // Assert
      expect(result.selectedDeckId).toBe('deck2');
    });
  });
});
