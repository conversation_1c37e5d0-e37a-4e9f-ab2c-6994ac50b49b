import {GameBoard} from '@/src/client/Gaming/domain/GameBoard/GameBoard';
import {initialGameBoardState} from '@/src/client/Gaming/domain/GameBoard/gameBoardReducer';

describe('When managing game board', () => {
  it('should place card successfully', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);

    // Act
    gameBoard.placeCard('player1', 'card-123', 'first', 0);

    // Assert
    const placedCard = gameBoard.getPlacedCard('player1', 'first', 0);
    expect(placedCard).not.toBeNull();
    expect(placedCard!.cardId).toBe('card-123');
    expect(placedCard!.slotIndex).toBe(0);
    expect(placedCard!.rowType).toBe('first');
  });

  it('should throw error when placing card in occupied slot', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);
    gameBoard.placeCard('player1', 'card-123', 'first', 0);

    // Act
    const placeCardInOccupiedSlot = () => gameBoard.placeCard('player1', 'card-456', 'first', 0);

    // Assert
    expect(placeCardInOccupiedSlot).toThrow('Slot 0 in player1 first row is already occupied');
  });

  it('should throw error when placing card in invalid slot', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);

    // Act
    const placeCardInInvalidSlot = () => gameBoard.placeCard('player1', 'card-123', 'first', -1);

    // Assert
    expect(placeCardInInvalidSlot).toThrow('Invalid slot index: -1');
  });

  it('should remove card successfully', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);
    gameBoard.placeCard('player1', 'card-123', 'first', 0);

    // Act
    gameBoard.removeCard('player1', 'first', 0);

    // Assert
    expect(gameBoard.isSlotEmpty('player1', 'first', 0)).toBe(true);
  });

  it('should throw error when removing card from empty slot', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);

    // Act
    const removeCardFromEmptySlot = () => gameBoard.removeCard('player1', 'first', 0);

    // Assert
    expect(removeCardFromEmptySlot).toThrow('No card to remove at slot 0 in player1 first row');
  });

  it('should clear board successfully', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);
    gameBoard.placeCard('player1', 'card-123', 'first', 0);
    gameBoard.placeCard('player2', 'card-456', 'second', 3);

    // Act
    gameBoard.clearBoard();

    // Assert
    expect(gameBoard.isSlotEmpty('player1', 'first', 0)).toBe(true);
    expect(gameBoard.isSlotEmpty('player2', 'second', 3)).toBe(true);
  });

  it('should generate domain events when placing card', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);

    // Act
    gameBoard.placeCard('player1', 'card-123', 'first', 0);
    const events = gameBoard.getDomainEvents();

    // Assert
    expect(events).toHaveLength(1);
    expect(events[0].type).toBe('gameBoard/cardPlaced');
  });

  it('should clear domain events after getting them', () => {
    // Arrange
    const gameBoard = GameBoard.fromState(initialGameBoardState);
    gameBoard.placeCard('player1', 'card-123', 'first', 0);

    // Act
    gameBoard.getDomainEvents();
    const eventsAfter = gameBoard.getDomainEvents();

    // Assert
    expect(eventsAfter).toHaveLength(0);
  });
});