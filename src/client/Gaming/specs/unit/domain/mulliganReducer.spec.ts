import {mulliganReducer, initialMulliganState} from '@/src/client/Gaming/domain/Mulligan/mulliganReducer';
import {mulliganDataLoadedEvent, mulliganSubmittedEvent, mulliganCardToggledEvent} from '@/src/client/Gaming/domain/Mulligan/mulliganEvents';

describe('MulliganReducer', () => {
  describe('When handling multiple rounds', () => {
    it('should set submitted to false for new round with no previous selection', () => {
      // Arrange
      const state = initialMulliganState;
      const mulliganData = {
        matchId: 'match1',
        handCardIds: ['c1', 'c2'],
        handCardUrls: ['url1', 'url2'],
        finalHandCardIds: ['c1', 'c2'],
        finalHandCardUrls: ['url1', 'url2'],
        currentRound: 1,
        maxRounds: 2,
        hasCompletedAllRounds: false,
        previousSelections: []
      };

      // Act
      const newState = mulliganReducer(state, mulliganDataLoadedEvent(mulliganData));

      // Assert
      expect(newState.submitted).toBe(false);
      expect(newState.data?.currentRound).toBe(1);
    });

    it('should set submitted to true when player has completed all rounds', () => {
      // Arrange
      const state = initialMulliganState;
      const mulliganData = {
        matchId: 'match1',
        handCardIds: ['c1', 'c2'],
        handCardUrls: ['url1', 'url2'],
        finalHandCardIds: ['c2'],
        finalHandCardUrls: ['url2'],
        currentRound: 2,
        maxRounds: 2,
        hasCompletedAllRounds: true,
        previousSelections: [{
          round: 1,
          selectedCardIds: ['c1'],
          skipped: false
        }]
      };

      // Act
      const newState = mulliganReducer(state, mulliganDataLoadedEvent(mulliganData));

      // Assert
      expect(newState.submitted).toBe(true);
      expect(newState.data?.currentRound).toBe(2);
    });

    it('should set submitted to false when player can continue with more rounds', () => {
      // Arrange
      const state = {
        ...initialMulliganState,
        submitted: false,
        data: {
          matchId: 'match1',
          handCardIds: ['c1', 'c2'],
          handCardUrls: ['url1', 'url2'],
          finalHandCardIds: ['c2'],
          finalHandCardUrls: ['url2'],
          currentRound: 2,
          maxRounds: 2,
          hasCompletedAllRounds: false,
          previousSelections: [{
            round: 1,
            selectedCardIds: ['c1'],
            skipped: false
          }]
        }
      };

      const newRoundData = {
        matchId: 'match1',
        handCardIds: ['c1', 'c2'],
        handCardUrls: ['url1', 'url2'],
        finalHandCardIds: ['c2'],
        finalHandCardUrls: ['url2'],
        currentRound: 2,
        maxRounds: 2,
        hasCompletedAllRounds: false,
        previousSelections: [{
          round: 1,
          selectedCardIds: ['c1'],
          skipped: false
        }]
      };

      // Act
      const newState = mulliganReducer(state, mulliganDataLoadedEvent(newRoundData));

      // Assert
      expect(newState.submitted).toBe(false);
      expect(newState.data?.hasCompletedAllRounds).toBe(false);
    });

    it('should set submitted to true after submitting selection', () => {
      // Arrange
      const state = {
        ...initialMulliganState,
        data: {
          matchId: 'match1',
          handCardIds: ['c1', 'c2'],
          handCardUrls: ['url1', 'url2'],
          finalHandCardIds: ['c1', 'c2'],
          finalHandCardUrls: ['url1', 'url2'],
          currentRound: 1,
          maxRounds: 2,
          hasCompletedAllRounds: false,
          previousSelections: []
        }
      };

      // Act
      const newState = mulliganReducer(state, mulliganSubmittedEvent({
        matchId: 'match1',
        selectedCardIds: ['c1'],
        skipped: false,
        round: 1
      }));

      // Assert
      expect(newState.selectedCardIds).toEqual([]);
    });
  });

  describe('When toggling card selection', () => {
    it('should add card to selection when not selected', () => {
      // Arrange
      const state = {
        ...initialMulliganState,
        selectedCardIds: []
      };

      // Act
      const newState = mulliganReducer(state, mulliganCardToggledEvent({cardId: 'card1'}));

      // Assert
      expect(newState.selectedCardIds).toEqual(['card1']);
    });

    it('should remove card from selection when already selected', () => {
      // Arrange
      const state = {
        ...initialMulliganState,
        selectedCardIds: ['card1', 'card2']
      };

      // Act
      const newState = mulliganReducer(state, mulliganCardToggledEvent({cardId: 'card1'}));

      // Assert
      expect(newState.selectedCardIds).toEqual(['card2']);
    });

    it('should not add card when 7 cards are already selected', () => {
      // Arrange
      const state = {
        ...initialMulliganState,
        selectedCardIds: ['c1', 'c2', 'c3', 'c4', 'c5', 'c6', 'c7']
      };

      // Act
      const newState = mulliganReducer(state, mulliganCardToggledEvent({cardId: 'c8'}));

      // Assert
      expect(newState.selectedCardIds).toEqual(['c1', 'c2', 'c3', 'c4', 'c5', 'c6', 'c7']);
    });

    it('should remove selected card even when 7 cards are selected', () => {
      // Arrange
      const state = {
        ...initialMulliganState,
        selectedCardIds: ['c1', 'c2', 'c3', 'c4', 'c5', 'c6', 'c7']
      };

      // Act
      const newState = mulliganReducer(state, mulliganCardToggledEvent({cardId: 'c3'}));

      // Assert
      expect(newState.selectedCardIds).toEqual(['c1', 'c2', 'c4', 'c5', 'c6', 'c7']);
    });
  });
});