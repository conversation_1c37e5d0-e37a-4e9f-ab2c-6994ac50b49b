// Using global Vitest APIs as configured

describe('TextureCache URL handling', () => {
  describe('When processing card IDs', () => {
    it('should handle simple card IDs correctly', () => {
      // Arrange
      const cardId = '72';
      const expectedUrl = '/game-assets/cards/en/thumbnail/72.jpg';
      // Act
      const url = cardId.startsWith('/game-assets/') || cardId.startsWith('http') 
        ? cardId 
        : `/game-assets/cards/en/thumbnail/${cardId}.jpg`;
      // Assert
      expect(url).toBe(expectedUrl);
    });

    it('should handle full path card IDs correctly', () => {
      // Arrange
      const cardId = '/game-assets/cards/en/thumbnail/72.jpg';
      const expectedUrl = '/game-assets/cards/en/thumbnail/72.jpg';
      // Act
      const url = cardId.startsWith('/game-assets/') || cardId.startsWith('http') 
        ? cardId 
        : `/game-assets/cards/en/thumbnail/${cardId}.jpg`;
      // Assert
      expect(url).toBe(expectedUrl);
    });

    it('should extract simple ID from full path correctly', () => {
      // Arrange
      const fullPath = '/game-assets/cards/en/thumbnail/72.jpg';
      const expectedSimpleId = '72';
      // Act
      const simpleId = fullPath.includes('/game-assets/cards/en/thumbnail/') 
        ? fullPath.replace('/game-assets/cards/en/thumbnail/', '').replace('.jpg', '')
        : fullPath;
      // Assert
      expect(simpleId).toBe(expectedSimpleId);
    });

    it('should not double-process paths', () => {
      // Arrange
      const alreadyProcessedPath = '/game-assets/cards/en/thumbnail/72.jpg';
      // Act
      const url = alreadyProcessedPath.startsWith('/game-assets/') || alreadyProcessedPath.startsWith('http') 
        ? alreadyProcessedPath 
        : `/game-assets/cards/en/thumbnail/${alreadyProcessedPath}.jpg`;
      // Assert
      expect(url).toBe('/game-assets/cards/en/thumbnail/72.jpg');
      expect(url).not.toContain('//game-assets/cards/en/thumbnail/');
    });
  });
});
