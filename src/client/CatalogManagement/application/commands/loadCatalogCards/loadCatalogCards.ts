import {
  catalogLoadedEvent,
  catalogLoadingFailedEvent,
  catalogLoadingStartedEvent,
} from '@/src/client/CatalogManagement/domain/Catalog/catalogEvents';
import {LoadCatalogCardsRequest} from './loadCatalogCardsRequest';
import {AppDispatch} from '@/src/client/Shared/store/appStore/appDispatch';

export const loadCatalogCards = (request: LoadCatalogCardsRequest) => (dispatch: AppDispatch): void => {
  if (!request) {
    dispatch(catalogLoadingStartedEvent());
    return;
  }

  if (request.error) {
    dispatch(catalogLoadingFailedEvent({error: request.error}));
    return;
  }

  const cards = request.data?.cards || [];
  dispatch(catalogLoadedEvent({cards}));
};
