import {createAsyncThunk} from '@reduxjs/toolkit';
import {catalogCardCreatedEvent} from '@/src/client/CatalogManagement/domain/Catalog/catalogEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {
  CreateCatalogCardRequest
} from "@/src/client/CatalogManagement/application/commands/createCatalogCard/createCatalogCardRequest";

export const createCatalogCard = createAsyncThunk<void, CreateCatalogCardRequest, {state: RootState; extra: ThunkExtra}>(
  'catalogManagement/createCatalogCard',
  async ({gameId, card}, {dispatch}) => {
    dispatch(catalogCardCreatedEvent({gameId, card}));
  },
);
