import {createAsyncThunk} from '@reduxjs/toolkit';
import {catalogCardUpdatedEvent} from '@/src/client/CatalogManagement/domain/Catalog/catalogEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {
  UpdateCatalogCardRequest
} from "@/src/client/CatalogManagement/application/commands/updateCatalogCard/updateCatalogCardRequest";

export const updateCatalogCard = createAsyncThunk<void, UpdateCatalogCardRequest, {state: RootState; extra: ThunkExtra}>(
  'catalogManagement/updateCatalogCard',
  async ({gameId, card}, {dispatch}) => {
    dispatch(catalogCardUpdatedEvent({gameId, card}));
  },
);
