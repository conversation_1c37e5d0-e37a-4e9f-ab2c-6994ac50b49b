import {createAction} from '@reduxjs/toolkit';
import {CatalogCard} from './CatalogCard';

export const catalogCardCreatedEvent = createAction<{gameId: string; card: CatalogCard}>('catalogManagement/cardCreated');
export const catalogCardUpdatedEvent = createAction<{gameId: string; card: CatalogCard}>('catalogManagement/cardUpdated');
export const catalogCardDeletedEvent = createAction<{gameId: string; cardId: string}>('catalogManagement/cardDeleted');

export const catalogLoadingStartedEvent = createAction('catalogManagement/loadingStarted');
export const catalogLoadedEvent = createAction<{cards: CatalogCard[]}>('catalogManagement/loaded');
export const catalogLoadingFailedEvent = createAction<{error: string}>('catalogManagement/loadingFailed');
