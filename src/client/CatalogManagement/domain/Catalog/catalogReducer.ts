import {createReducer} from '@reduxjs/toolkit';
import {
  catalogCardCreatedEvent,
  catalogCardUpdatedEvent,
  catalogCardDeletedEvent,
  catalogLoadingStartedEvent,
  catalogLoadedEvent,
  catalogLoadingFailedEvent,
} from './catalogEvents';
import {CatalogCard} from './CatalogCard';

export interface CatalogState {
  cards: Record<string, CatalogCard>;
  status: 'loading' | 'success' | 'error';
  error: string | null;
}

export const initialCatalogState: CatalogState = {
  cards: {},
  status: 'loading',
  error: null,
};

export const catalogReducer = createReducer(initialCatalogState, builder =>
  builder
    .addCase(catalogLoadingStartedEvent, state => {
      state.status = 'loading';
      state.error = null;
    })
    .addCase(catalogLoadedEvent, (state, {payload}) => {
      state.status = 'success';
      state.cards = Object.fromEntries(payload.cards.map(card => [card.id, card]));
    })
    .addCase(catalogLoadingFailedEvent, (state, {payload}) => {
      state.status = 'error';
      state.error = payload.error;
    })
    .addCase(catalogCardCreatedEvent, (state, {payload}) => {
      state.cards[payload.card.id] = payload.card;
    })
    .addCase(catalogCardUpdatedEvent, (state, {payload}) => {
      state.cards[payload.card.id] = payload.card;
    })
    .addCase(catalogCardDeletedEvent, (state, {payload}) => {
      delete state.cards[payload.cardId];
    }),
);
